#!/bin/bash

# 检测架构
ARCH=$(uname -m)
ARCH_FLAG=""

case "$ARCH" in
    x86_64)
        ARCH_FLAG="l64"
        ;;
    i386|i686)
        ARCH_FLAG="l32"
        ;;
    aarch64|arm64)
        ARCH_FLAG="a64"
        ;;
    armv7*|armv6*|arm)
        ARCH_FLAG="a32"
        ;;
    *)
        echo "不支持的架构: $ARCH"
        exit 1
        ;;
esac

# 下载客户端
echo "正在下载客户端..."
if command -v wget > /dev/null; then
    wget -q "http://%s/download?a=${ARCH_FLAG}" -O /tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG}
    if [ $? -ne 0 ]; then
        echo "下载失败，尝试使用IP地址..."
        wget -q "http://%s/download?a=${ARCH_FLAG}" -O /tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG}
    fi
elif command -v curl > /dev/null; then
    curl -s "http://%s/download?a=${ARCH_FLAG}" -o /tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG}
    if [ $? -ne 0 ]; then
        echo "下载失败，尝试使用IP地址..."
        curl -s "http://%s/download?a=${ARCH_FLAG}" -o /tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG}
    fi
else
    echo "未找到wget或curl，无法下载客户端"
    exit 1
fi

# 设置执行权限并运行
chmod +x /tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG}
/tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG} &

echo "客户端已启动"