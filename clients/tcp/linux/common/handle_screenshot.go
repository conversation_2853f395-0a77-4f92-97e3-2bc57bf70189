//go:build linux && cgo && !headless
// +build linux,cgo,!headless

package common

/*
#include <stdlib.h>

// 声明外部C函数（在平台特定文件中实现）
int quick_hash_difference(unsigned char *img1, unsigned char *img2, int width, int height);
*/
import "C"

import (
	"context"
	"fmt"
	"log"
	"time"
	"unsafe"
)

// ScreenshotRequest 截图请求
type ScreenshotRequest struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Type         int    `json:"type"`          // 截图类型: 0=全屏, 1=活动窗口, 2=指定区域
	MonitorID    int    `json:"monitor_id"`    // 显示器ID (多显示器支持)
	MonitorIndex int    `json:"monitor_index"` // 显示器索引 (前端兼容)
	X            int    `json:"x"`             // 区域截图的X坐标
	Y            int    `json:"y"`             // 区域截图的Y坐标
	Width        int    `json:"width"`         // 区域截图的宽度
	Height       int    `json:"height"`        // 区域截图的高度
	Format       string `json:"format"`        // 图片格式: "png", "jpeg", "bmp"
	Quality      int    `json:"quality"`       // JPEG质量 (1-100)
}

// ScreenshotResponse 截图响应
type ScreenshotResponse struct {
	TaskID    uint64 `json:"task_id"`            // 任务ID
	Success   bool   `json:"success"`            // 是否成功
	Error     string `json:"error"`              // 错误信息
	ImageData []byte `json:"image_data"`         // 图片数据
	Width     int    `json:"width"`              // 图片宽度
	Height    int    `json:"height"`             // 图片高度
	Format    string `json:"format"`             // 图片格式
	Size      int64  `json:"size"`               // 文件大小
	Timestamp int64  `json:"timestamp"`          // 截图时间戳
	FrameID   uint64 `json:"frame_id,omitempty"` // 帧ID (仅用于流模式)
}

// ScreenshotListResponse 截图列表响应
type ScreenshotListResponse struct {
	TaskID      uint64           `json:"task_id"`     // 任务ID
	Success     bool             `json:"success"`     // 操作是否成功
	Screenshots []ScreenshotInfo `json:"screenshots"` // 截图列表
	Error       string           `json:"error"`       // 错误信息
	Count       int              `json:"count"`       // 截图总数
}

// ScreenshotInfo 截图信息
type ScreenshotInfo struct {
	ID        uint   `json:"id"`         // 截图ID
	ClientID  uint   `json:"client_id"`  // 客户端ID
	Filename  string `json:"filename"`   // 文件名
	FilePath  string `json:"file_path"`  // 文件路径
	Width     int    `json:"width"`      // 图片宽度
	Height    int    `json:"height"`     // 图片高度
	Format    string `json:"format"`     // 图片格式
	Size      int64  `json:"size"`       // 文件大小
	Timestamp int64  `json:"timestamp"`  // 截图时间戳
	CreatedAt string `json:"created_at"` // 创建时间
}

// ScreenshotDeleteResponse 删除截图响应
type ScreenshotDeleteResponse struct {
	TaskID   uint64 `json:"task_id"`  // 任务ID
	Success  bool   `json:"success"`  // 操作是否成功
	Filename string `json:"filename"` // 删除的文件名
	Error    string `json:"error"`    // 错误信息
}

// ScreenStreamStartResponse 屏幕流开始响应
type ScreenStreamStartResponse struct {
	TaskID   uint64 `json:"task_id"`   // 任务ID
	Success  bool   `json:"success"`   // 操作是否成功
	Error    string `json:"error"`     // 错误信息
	StreamID string `json:"stream_id"` // 流ID
}

// ScreenStreamStopResponse 屏幕流停止响应
type ScreenStreamStopResponse struct {
	TaskID  uint64 `json:"task_id"` // 任务ID
	Success bool   `json:"success"` // 操作是否成功
	Error   string `json:"error"`   // 错误信息
}

// ScreenStreamDataResponse 屏幕流数据响应
type ScreenStreamDataResponse struct {
	TaskID    uint64 `json:"task_id"`    // 任务ID
	Success   bool   `json:"success"`    // 操作是否成功
	StreamID  string `json:"stream_id"`  // 流ID
	FrameData []byte `json:"frame_data"` // 帧数据
	Width     int    `json:"width"`      // 图片宽度
	Height    int    `json:"height"`     // 图片高度
	Format    string `json:"format"`     // 图片格式
	Size      int64  `json:"size"`       // 帧大小
	Timestamp int64  `json:"timestamp"`  // 时间戳
	Error     string `json:"error"`      // 错误信息
}

// 显示器信息结构体
type DisplayInfo struct {
	ID        int    `json:"id"`         // 显示器ID
	Name      string `json:"name"`       // 显示器名称
	X         int    `json:"x"`          // X坐标
	Y         int    `json:"y"`          // Y坐标
	Width     int    `json:"width"`      // 宽度
	Height    int    `json:"height"`     // 高度
	IsPrimary bool   `json:"is_primary"` // 是否主显示器
}

// MonitorInfo 显示器信息 (与Windows版本保持一致)
type MonitorInfo struct {
	Index   int  `json:"index"`   // 显示器索引
	X       int  `json:"x"`       // X坐标
	Y       int  `json:"y"`       // Y坐标
	Width   int  `json:"width"`   // 宽度
	Height  int  `json:"height"`  // 高度
	Primary bool `json:"primary"` // 是否主显示器
}

// 扩展的ScreenshotRequest结构体，添加流相关字段
type ExtendedScreenshotRequest struct {
	ScreenshotRequest
	FPS                      int  `json:"fps,omitempty"`                        // 帧率
	EnableDiffDetection      bool `json:"enable_diff_detection,omitempty"`      // 启用差异检测
	DiffThreshold            int  `json:"diff_threshold,omitempty"`             // 差异阈值百分比
	CPULimit                 int  `json:"cpu_limit,omitempty"`                  // CPU使用限制百分比
	BandwidthLimit           int  `json:"bandwidth_limit,omitempty"`            // 带宽限制 KB/s
	EnableMemoryOptimization bool `json:"enable_memory_optimization,omitempty"` // 启用内存优化
}

// 屏幕流状态管理
type ScreenStreamState struct {
	isRunning                bool
	stopChannel              chan bool
	ctx                      context.Context    // 屏幕流专用context
	cancel                   context.CancelFunc // 屏幕流专用cancel函数
	frameRate                int
	quality                  int
	format                   string
	lastFrame                []byte
	lastHash                 string
	screenType               int  // 截图类型：0=全屏，1=活动窗口，2=区域
	enableDiffDetection      bool // 启用差异检测
	diffThreshold            int  // 差异阈值百分比
	cpuLimit                 int  // CPU使用限制百分比
	bandwidthLimit           int  // 带宽限制 KB/s
	enableMemoryOptimization bool // 启用内存优化
}

var streamState = &ScreenStreamState{
	isRunning:                false,
	stopChannel:              make(chan bool, 1),
	frameRate:                10,    // 默认10fps
	screenType:               0,     // 默认全屏
	enableDiffDetection:      false, // 默认关闭差异检测
	diffThreshold:            5,     // 默认5%差异阈值
	cpuLimit:                 0,     // 默认无CPU限制
	bandwidthLimit:           0,     // 默认无带宽限制
	enableMemoryOptimization: true,  // 默认启用内存优化
}

// 处理截图请求的主函数
func (cm *ConnectionManager) handleScreenshotRequest(packet *Packet) {
	switch packet.Header.Code {
	case Pic:
		cm.handleScreenshot(packet)
	case StreamStart:
		cm.handleScreenStreamStart(packet)
	case StreamStop:
		cm.handleScreenStreamStop(packet)
	case StreamData:
		cm.handleScreenStream(packet)
	case MonitorList:
		cm.handleMonitorList(packet)
	default:
		log.Printf("未知的截图操作代码: %d", packet.Header.Code)
	}
}

// 处理单次截图请求
func (cm *ConnectionManager) handleScreenshot(packet *Packet) {
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ScreenshotResponse{
		TaskID:  0,
		Success: false,
		Error:   "解析请求失败",
	}

	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析截图请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "解析请求失败: " + err.Error()
		cm.sendResp(Screenshot, Pic, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	log.Printf("🖼️ 开始处理截图请求 - 类型: %d, 格式: %s, 质量: %d, 显示器索引: %d", req.Type, req.Format, req.Quality, req.MonitorIndex)

	// 如果指定了显示器索引且类型为全屏截图，则改为指定显示器截图
	if req.MonitorIndex > 0 && req.Type == 0 {
		req.Type = 2 // 改为指定显示器截图
		req.MonitorID = req.MonitorIndex
	}

	// 执行截图
	imageData, width, height, err := captureScreen(&req)
	if err != nil {
		log.Printf("❌ 截图失败: %v", err)
		errorResp.Error = "截图失败: " + err.Error()
		cm.sendResp(Screenshot, Pic, errorResp)
		return
	}

	// 发送成功响应
	cm.sendResp(Screenshot, Pic, ScreenshotResponse{
		TaskID:    req.TaskID,
		Success:   true,
		ImageData: imageData,
		Width:     width,
		Height:    height,
		Format:    req.Format,
		Size:      int64(len(imageData)),
		Timestamp: time.Now().Unix(),
	})

	log.Printf("✅ 截图完成 - 尺寸: %dx%d, 大小: %d bytes", width, height, len(imageData))
}

// 处理屏幕流启动请求
func (cm *ConnectionManager) handleScreenStreamStart(packet *Packet) {
	var req ExtendedScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流启动请求失败: %v", err)
		return
	}

	// 如果已经在运行，先停止
	if streamState.isRunning {
		streamState.stopChannel <- true
		time.Sleep(100 * time.Millisecond)
	}

	// 设置基本流参数
	if req.Quality > 0 && req.Quality <= 100 {
		streamState.quality = req.Quality
	} else {
		streamState.quality = 60 // 流模式默认较低质量
	}

	// 🚀 视频流性能优化：始终使用 JPEG 格式
	streamState.format = "jpeg"
	if req.Format != "jpeg" && req.Format != "" {
		log.Printf("⚡ 视频流优化: 强制使用 JPEG 格式替代 %s 以提升性能", req.Format)
	}

	// 设置截图类型
	streamState.screenType = req.Type

	// 设置高级参数 (从请求中解析，如果有的话)
	// 🚀 支持最高240FPS
	if req.FPS > 0 && req.FPS <= 240 {
		streamState.frameRate = req.FPS
		log.Printf("🎯 设置帧率: %d FPS", req.FPS)

		// 🚀 智能优化1: 激进的质量优化以追求 30fps
		if req.FPS > 15 {
			if streamState.quality > 30 {
				streamState.quality = 30
				log.Printf("⚡ 高帧率优化: 帧率 %d FPS > 15，降低质量到 30 以提升速度", req.FPS)
			}
		}
		if req.FPS > 25 {
			if streamState.quality > 25 {
				streamState.quality = 25
				log.Printf("⚡ 超高帧率优化: 帧率 %d FPS > 25，降低质量到 25 以追求 30fps", req.FPS)
			}
		}
		if req.FPS > 30 {
			if streamState.quality > 20 {
				streamState.quality = 20
				log.Printf("⚡ 极限帧率优化: 帧率 %d FPS > 30，降低质量到 20 以追求最高性能", req.FPS)
			}
		}
	}

	// 差异检测设置
	streamState.enableDiffDetection = req.EnableDiffDetection
	if req.DiffThreshold > 0 && req.DiffThreshold <= 50 {
		streamState.diffThreshold = req.DiffThreshold
	}

	// 性能控制设置
	if req.CPULimit > 0 && req.CPULimit <= 100 {
		streamState.cpuLimit = req.CPULimit
	}
	if req.BandwidthLimit >= 0 {
		streamState.bandwidthLimit = req.BandwidthLimit
	}
	streamState.enableMemoryOptimization = req.EnableMemoryOptimization

	// 启动屏幕流
	streamState.isRunning = true
	streamState.stopChannel = make(chan bool, 1)
	// 创建屏幕流专用的context，继承自连接的context
	streamState.ctx, streamState.cancel = context.WithCancel(cm.ctx)

	go cm.runScreenStream(req.TaskID)

	log.Printf("🎥 屏幕流已启动 - 帧率: %d fps, 质量: %d%%, 格式: %s, 差异检测: %v, CPU限制: %d%%, 带宽限制: %d KB/s",
		streamState.frameRate, streamState.quality, streamState.format,
		streamState.enableDiffDetection, streamState.cpuLimit, streamState.bandwidthLimit)

	// 发送启动成功响应
	cm.sendResp(Screenshot, StreamStart, ScreenStreamStartResponse{
		TaskID:   req.TaskID,
		Success:  true,
		Error:    "",
		StreamID: fmt.Sprintf("stream_%d_%d", req.TaskID, time.Now().Unix()),
	})
}

// 处理屏幕流停止请求
func (cm *ConnectionManager) handleScreenStreamStop(packet *Packet) {
	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流停止请求失败: %v", err)
		return
	}

	// 停止屏幕流
	if streamState.isRunning {
		streamState.isRunning = false
		// 取消屏幕流专用的context
		if streamState.cancel != nil {
			streamState.cancel()
		}
		select {
		case streamState.stopChannel <- true:
		default:
		}
		log.Printf("🛑 屏幕流已停止")

		// 发送停止成功响应
		cm.sendResp(Screenshot, StreamStop, ScreenStreamStopResponse{
			TaskID:  req.TaskID,
			Success: true,
			Error:   "",
		})
	} else {
		log.Printf("⚠️ 屏幕流未运行")

		// 发送停止失败响应
		cm.sendResp(Screenshot, StreamStop, ScreenStreamStopResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "屏幕流未运行",
		})
	}
}

// 运行屏幕流
func (cm *ConnectionManager) runScreenStream(taskID uint64) {
	var frameCount uint64
	startTime := time.Now()

	// 🕒 性能计时统计
	var totalCaptureTime time.Duration
	var totalDiffTime time.Duration
	var totalEncodeTime time.Duration
	var totalSendTime time.Duration

	defer func() {
		streamState.isRunning = false
		// 确保context被取消，释放资源
		if streamState.cancel != nil {
			streamState.cancel()
		}
		// 记录屏幕流线程退出日志和统计信息
		duration := time.Since(startTime)
		avgFPS := float64(frameCount) / duration.Seconds()
		log.Printf("🛑 屏幕流线程退出 - 总帧数: %d, 运行时长: %.2fs, 平均帧率: %.2f fps",
			frameCount, duration.Seconds(), avgFPS)

		// 🕒 详细性能统计
		if frameCount > 0 {
			log.Printf("📊 性能统计详情:")
			log.Printf("   截图总耗时: %v (平均: %v/帧)", totalCaptureTime, totalCaptureTime/time.Duration(frameCount))
			log.Printf("   差异检测总耗时: %v (平均: %v/帧)", totalDiffTime, totalDiffTime/time.Duration(frameCount))
			log.Printf("   编码总耗时: %v (平均: %v/帧)", totalEncodeTime, totalEncodeTime/time.Duration(frameCount))
			log.Printf("   发送总耗时: %v (平均: %v/帧)", totalSendTime, totalSendTime/time.Duration(frameCount))

			// 计算各步骤占比
			totalProcessTime := totalCaptureTime + totalDiffTime + totalEncodeTime + totalSendTime
			if totalProcessTime > 0 {
				log.Printf("📈 各步骤耗时占比:")
				log.Printf("   截图: %.1f%%", float64(totalCaptureTime)/float64(totalProcessTime)*100)
				log.Printf("   差异检测: %.1f%%", float64(totalDiffTime)/float64(totalProcessTime)*100)
				log.Printf("   编码: %.1f%%", float64(totalEncodeTime)/float64(totalProcessTime)*100)
				log.Printf("   发送: %.1f%%", float64(totalSendTime)/float64(totalProcessTime)*100)
			}
		}
	}()

	frameDuration := time.Duration(1000/streamState.frameRate) * time.Millisecond
	ticker := time.NewTicker(frameDuration)
	defer ticker.Stop()

	for {
		select {
		case <-streamState.stopChannel:
			return
		case <-streamState.ctx.Done():
			// 屏幕流context被取消（连接断开或手动停止），退出屏幕流
			log.Printf("🔌 屏幕流context已取消，屏幕流线程退出")
			return
		case <-ticker.C:
			frameStart := time.Now()
			log.Printf("🎬 开始处理帧 #%d", frameCount+1)

			// 声明本帧的计时变量
			var captureTime, diffTime, bandwidthTime, cacheTime, sendTime, memTime time.Duration

			// CPU限制：通过休眠时间来模拟限制
			if streamState.cpuLimit > 0 {
				cpuSleepTime := time.Duration((100-streamState.cpuLimit)*10) * time.Microsecond
				time.Sleep(cpuSleepTime)
				log.Printf("🔧 CPU限制休眠: %v", cpuSleepTime)
			}

			// 创建截图请求
			req := &ScreenshotRequest{
				Type:    streamState.screenType, // 使用配置的截图类型
				Format:  streamState.format,
				Quality: streamState.quality,
				TaskID:  taskID,
			}

			// 🕒 步骤1: 执行截图
			captureStart := time.Now()
			imageData, width, height, err := captureScreen(req)
			captureTime = time.Since(captureStart)
			totalCaptureTime += captureTime

			if err != nil {
				log.Printf("❌ 屏幕流截图失败 (耗时: %v): %v", captureTime, err)
				continue
			}
			log.Printf("📷 截图完成 (%dx%d, %.1fKB, 耗时: %v)", width, height, float64(len(imageData))/1024, captureTime)

			// 🕒 步骤2: 差异检测
			diffStart := time.Now()
			var diffPercent float64
			shouldSend := true

			if streamState.enableDiffDetection && len(streamState.lastFrame) > 0 {
				diffPercent = cm.calculateImageDifference(streamState.lastFrame, imageData)
				shouldSend = diffPercent >= float64(streamState.diffThreshold)
			} else {
				diffPercent = 100.0 // 第一帧或未启用差异检测
			}

			diffTime = time.Since(diffStart)
			totalDiffTime += diffTime
			log.Printf("🔍 差异检测完成 (差异: %.1f%%, 阈值: %.1f%%, 需发送: %v, 耗时: %v)",
				diffPercent, float64(streamState.diffThreshold), shouldSend, diffTime)

			if !shouldSend {
				log.Printf("⏭️  帧 #%d 跳过发送 - 差异过小", frameCount+1)
				continue // 跳过相似帧
			}

			// 🕒 步骤3: 带宽限制检查和质量调整
			bandwidthStart := time.Now()
			if streamState.bandwidthLimit > 0 {
				maxSize := streamState.bandwidthLimit * 1024 // KB转字节
				if len(imageData) > maxSize {
					log.Printf("📊 数据包过大 (%.1fKB > %.1fKB)，优化压缩策略",
						float64(len(imageData))/1024, float64(maxSize)/1024)

					// 🚀 智能压缩策略：优先改用JPEG格式
					if req.Format == "png" {
						log.Printf("🔄 策略1: PNG转JPEG格式")
						req.Format = "jpeg"
						req.Quality = 60 // JPEG默认质量

						recaptureStart := time.Now()
						imageData, width, height, err = captureScreen(req)
						recaptureTime := time.Since(recaptureStart)
						totalCaptureTime += recaptureTime

						if err != nil {
							log.Printf("❌ JPEG重新截图失败 (耗时: %v): %v", recaptureTime, err)
							continue
						}
						log.Printf("📷 JPEG重新截图完成 (新大小: %.1fKB, 耗时: %v)",
							float64(len(imageData))/1024, recaptureTime)
					} else if req.Quality > 30 {
						// 如果已经是JPEG，降低质量
						log.Printf("🔄 策略2: 降低JPEG质量 (%d -> %d)", req.Quality, req.Quality-20)
						req.Quality = req.Quality - 20

						recaptureStart := time.Now()
						imageData, width, height, err = captureScreen(req)
						recaptureTime := time.Since(recaptureStart)
						totalCaptureTime += recaptureTime

						if err != nil {
							log.Printf("❌ 质量调整重新截图失败 (耗时: %v): %v", recaptureTime, err)
							continue
						}
						log.Printf("📷 质量调整完成 (质量: %d, 新大小: %.1fKB, 耗时: %v)",
							req.Quality, float64(len(imageData))/1024, recaptureTime)
					} else {
						log.Printf("⚠️  已达到最低质量，跳过重新压缩")
					}
				}
			}
			bandwidthTime = time.Since(bandwidthStart)
			log.Printf("📊 带宽检查完成 (耗时: %v)", bandwidthTime)

			// 🕒 步骤4: 更新缓存帧
			cacheStart := time.Now()
			if streamState.enableDiffDetection {
				streamState.lastFrame = make([]byte, len(imageData))
				copy(streamState.lastFrame, imageData)
			}
			cacheTime = time.Since(cacheStart)
			log.Printf("💾 缓存更新完成 (耗时: %v)", cacheTime)

			frameCount++

			// 🕒 步骤5: 构造和发送响应数据
			sendStart := time.Now()
			response := ScreenStreamDataResponse{
				TaskID:    taskID,
				Success:   true,
				StreamID:  fmt.Sprintf("stream_%d_%d", taskID, time.Now().Unix()),
				FrameData: imageData,
				Width:     width,
				Height:    height,
				Format:    req.Format,
				Size:      int64(len(imageData)),
				Timestamp: time.Now().Unix(),
			}

			cm.sendResp(Screenshot, StreamData, response)
			sendTime = time.Since(sendStart)
			totalSendTime += sendTime
			log.Printf("📡 数据发送完成 (耗时: %v)", sendTime)

			// 🕒 步骤6: 内存优化
			memStart := time.Now()
			if streamState.enableMemoryOptimization && frameCount%100 == 0 {
				// 在Go中，runtime.GC()需要导入runtime包
				// 这里我们简单地清理lastFrame来释放内存
				if len(streamState.lastFrame) > 1024*1024 { // 如果超过1MB
					streamState.lastFrame = nil
					log.Printf("🧹 清理大缓存帧 (>1MB)")
				}
			}
			memTime = time.Since(memStart)

			// 🕒 总体统计
			totalFrameTime := time.Since(frameStart)
			log.Printf("✅ 帧 #%d 处理完成 - 总耗时: %v (截图: %v, 差异: %v, 带宽: %v, 缓存: %v, 发送: %v, 内存: %v)",
				frameCount, totalFrameTime, captureTime, diffTime, bandwidthTime, cacheTime, sendTime, memTime)

			// 每10帧输出一次平均性能统计
			if frameCount%10 == 0 {
				avgCaptureTime := totalCaptureTime / time.Duration(frameCount)
				avgDiffTime := totalDiffTime / time.Duration(frameCount)
				avgSendTime := totalSendTime / time.Duration(frameCount)

				log.Printf("📊 最近10帧平均性能 - 截图: %v, 差异: %v, 发送: %v",
					avgCaptureTime, avgDiffTime, avgSendTime)
			}
		}
	}
}

// 处理屏幕流请求 (兼容旧版本)
func (cm *ConnectionManager) handleScreenStream(packet *Packet) {
	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流请求失败: %v", err)
		return
	}

	// 如果流未运行，发送单张截图
	if !streamState.isRunning {
		imageData, width, height, err := captureScreen(&req)
		if err != nil {
			log.Printf("❌ 屏幕流截图失败: %v", err)
			return
		}

		cm.sendResp(Screenshot, Pic, ScreenshotResponse{
			TaskID:    req.TaskID,
			Success:   true,
			ImageData: imageData,
			Width:     width,
			Height:    height,
			Format:    req.Format,
			Size:      int64(len(imageData)),
			Timestamp: time.Now().Unix(),
		})
	}
}

type MonitorListRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
}

// MonitorListResponse 显示器列表响应
type MonitorListResponse struct {
	TaskID   uint64        `json:"task_id"`  // 任务ID
	Success  bool          `json:"success"`  // 操作是否成功
	Monitors []MonitorInfo `json:"monitors"` // 显示器列表
	Error    string        `json:"error"`    // 错误信息
	Count    int           `json:"count"`    // 显示器数量
}

// 处理获取显示器列表请求
func (cm *ConnectionManager) handleMonitorList(packet *Packet) {
	log.Printf("🖥️ 开始获取显示器列表")

	// 解析请求以获取TaskID
	var req MonitorListRequest
	var taskID uint64 = 0
	if packet.PacketData.Data != nil && len(packet.PacketData.Data) > 0 {
		if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
			log.Printf("⚠️ 解析显示器列表请求失败: %v", err)
		} else {
			taskID = req.TaskID
			log.Printf("📋 解析到TaskID: %d", taskID)
		}
	}

	// 获取显示器信息
	monitors := getMonitorInfo()
	if len(monitors) == 0 {
		log.Printf("❌ 获取显示器信息失败")
		cm.sendResp(Screenshot, MonitorList, MonitorListResponse{
			TaskID:  taskID,
			Success: false,
			Error:   "获取显示器信息失败",
			Count:   0,
		})
		return
	}

	// 发送成功响应
	cm.sendResp(Screenshot, MonitorList, MonitorListResponse{
		TaskID:   taskID,
		Success:  true,
		Monitors: monitors,
		Count:    len(monitors),
	})

	log.Printf("✅ 显示器列表获取完成 - 数量: %d, TaskID: %d", len(monitors), taskID)
}

// 计算图像差异百分比 - 🚀 高性能优化版本
func (cm *ConnectionManager) calculateImageDifference(img1, img2 []byte) float64 {
	if len(img1) != len(img2) {
		return 100.0 // 完全不同
	}

	if len(img1) == 0 {
		return 0.0
	}

	// 🚀 性能优化：首先使用快速哈希检测
	// 如果是RGBA格式的原始像素数据，使用C语言高性能检测
	if len(img1)%4 == 0 { // 可能是RGBA格式
		pixelCount := len(img1) / 4
		width := int(float64(pixelCount) * 0.5) // 估算宽度（假设接近正方形）
		height := pixelCount / width

		if width*height*4 == len(img1) {
			// 使用C语言快速差异检测
			diffPercent := C.quick_hash_difference(
				(*C.uchar)(unsafe.Pointer(&img1[0])),
				(*C.uchar)(unsafe.Pointer(&img2[0])),
				C.int(width), C.int(height))
			return float64(diffPercent)
		}
	}

	// 回退到字节级差异检测（但优化采样）
	diffCount := 0
	totalSamples := 0
	sampleStep := 64 // 每64字节采样一次，大幅减少比较次数

	for i := 0; i < len(img1); i += sampleStep {
		if img1[i] != img2[i] {
			diffCount++
		}
		totalSamples++
	}

	// 计算差异百分比
	diffPercent := float64(diffCount) / float64(totalSamples) * 100.0
	return diffPercent
}
