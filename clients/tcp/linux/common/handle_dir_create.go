//go:build linux
// +build linux

package common

import (
	"fmt"
	"log"
	"os"
	"strings"
)

// DirCreateRequest 目录创建请求
type DirCreateRequest struct {
	TaskID    uint64 `json:"task_id"`   // 任务ID
	Path      string `json:"path"`      // 目录路径
	Recursive bool   `json:"recursive"` // 是否递归创建父目录
	Mode      uint32 `json:"mode"`      // 目录权限模式
}

// DirCreateResponse 目录创建响应
type DirCreateResponse struct {
	TaskID      uint64 `json:"task_id"`      // 任务ID
	Success     bool   `json:"success"`      // 操作是否成功
	NotAllow    bool   `json:"not_allow"`    // 是否权限不足
	Exists      bool   `json:"exists"`       // 目录是否已存在
	CreatedPath string `json:"created_path"` // 实际创建的路径
	Error       string `json:"error"`        // 错误信息
}

// handleDirCreate 处理目录创建请求
func (cm *ConnectionManager) handleDirCreate(packet *Packet) {
	var req DirCreateRequest
	// 创建错误响应结构体，TaskID初始化为0
	respErr := &DirCreateResponse{
		TaskID:  0,
		Success: false,
		Error:   "",
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("目录创建请求反序列化失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		respErr.Error = fmt.Sprintf("目录创建请求反序列化失败: %v", err)
		cm.sendResp(Dir, DirCreate, respErr)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	respErr.TaskID = req.TaskID

	// 参数验证
	path := strings.TrimSpace(req.Path)
	if path == "" {
		log.Printf("目录路径为空")
		respErr.Error = "目录路径为空"
		cm.sendResp(Dir, DirCreate, respErr)
		return
	}

	// 路径安全检查
	if !isValidPath(path) {
		log.Printf("无效的路径: %s", path)
		respErr.Error = fmt.Sprintf("无效的路径: %s", path)
		cm.sendResp(Dir, DirCreate, respErr)
		return
	}

	// 获取绝对路径
	absPath, err := getAbsolutePath(path)
	if err != nil {
		log.Printf("获取绝对路径失败: %v", err)
		respErr.Error = fmt.Sprintf("获取绝对路径失败: %v", err)
		cm.sendResp(Dir, DirCreate, respErr)
		return
	}

	// 使用互斥锁防止并发创建冲突
	dirOpMutex.Lock()
	defer dirOpMutex.Unlock()

	resp := cm.createDirectory(absPath, &req)
	cm.sendResp(Dir, DirCreate, resp)
}

// createDirectory 创建目录的核心逻辑
func (cm *ConnectionManager) createDirectory(path string, req *DirCreateRequest) *DirCreateResponse {
	resp := &DirCreateResponse{
		TaskID:      req.TaskID,
		Success:     false,
		NotAllow:    false,
		Exists:      false,
		CreatedPath: "",
		Error:       "",
	}

	// 检查路径是否已存在
	exists, err := PathExists(path)
	if err != nil {
		log.Printf("检查路径存在性出错: %v", err)
		if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "权限不足"
		} else {
			resp.Error = fmt.Sprintf("路径检查失败: %v", err)
		}
		return resp
	}

	if exists {
		resp.Exists = true
		resp.Error = "目录已存在"
		return resp
	}

	// 确定权限模式
	mode := os.FileMode(0755) // 默认权限
	if req.Mode != 0 {
		mode = os.FileMode(req.Mode)
	}

	// 创建目录
	var createErr error
	if req.Recursive {
		createErr = os.MkdirAll(path, mode)
	} else {
		createErr = os.Mkdir(path, mode)
	}

	if createErr != nil {
		log.Printf("目录创建失败: %v", createErr)
		if os.IsPermission(createErr) {
			resp.NotAllow = true
			resp.Error = "权限不足"
		} else if os.IsExist(createErr) {
			resp.Exists = true
			resp.Error = "目录已存在"
		} else {
			resp.Error = fmt.Sprintf("创建失败: %v", createErr)
		}
		return resp
	}

	// 验证创建结果
	if created, _ := PathExists(path); created {
		resp.Success = true
		resp.CreatedPath = path
	} else {
		resp.Error = "创建验证失败"
	}

	return resp
}
