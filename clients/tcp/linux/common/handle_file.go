//go:build linux
// +build linux

package common

import (
	"log"
	"os"
	"sync"
	"time"
)

// 文件操作互斥锁
var (
	fileMutex sync.RWMutex // 文件操作读写锁
)

// handleFileRequest 处理文件操作请求的主入口
func (cm *ConnectionManager) handleFileRequest(packet *Packet) {
	switch packet.Header.Code {
	case FileInfo:
		cm.handleFileInfo(packet)
	case FileDownload:
		cm.handleFileDownload(packet)
	case FileUpload:
		cm.handleFileUpload(packet)
	case FileCopy:
		cm.handleFileCopy(packet)
	case FileDelete:
		cm.handleFileDelete(packet)
	case FileMove:
		cm.handleFileMove(packet)
	case FileRead:
		cm.handleFileRead(packet)
	case FileWrite:
		cm.handleFileWrite(packet)
	case FileCreate:
		cm.handleFileCreate(packet)
	default:
		log.Printf("未知的文件操作子类型: %d", packet.Header.Code)
	}
}

// FileInfoRequest 文件信息请求
type FileInfoRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
	Path   string `json:"path"`    // 文件路径
	IsFull bool   `json:"is_full"` // 是否获取完整信息
}

// FileInfoResponse 文件信息响应
type FileInfoResponse struct {
	TaskID      uint64      `json:"task_id"`     // 任务ID
	Path        string      `json:"path"`        // 请求的文件路径
	ActualPath  string      `json:"actual_path"` // 实际解析的绝对路径
	Exist       bool        `json:"exist"`       // 文件是否存在
	Name        string      `json:"name"`        // 文件名
	Size        int64       `json:"size"`        // 文件大小（字节）
	Mode        os.FileMode `json:"mode"`        // 文件权限模式
	ModTime     time.Time   `json:"mod_time"`    // 最后修改时间
	IsDir       bool        `json:"is_dir"`      // 是否为目录
	IsSymlink   bool        `json:"is_symlink"`  // 是否为符号链接
	Error       string      `json:"error"`       // 错误信息
	Permissions string      `json:"permissions"` // 文件权限字符串（如：-rwxr-xr-x）
	Owner       string      `json:"owner"`       // 文件拥有者
	Group       string      `json:"group"`       // 文件所属组
}

// handleFileInfo 处理文件信息请求
func (cm *ConnectionManager) handleFileInfo(packet *Packet) {
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := &FileInfoResponse{
		TaskID: 0,
		Path:   "",
		Exist:  false,
		Error:  "请求格式错误",
	}

	var req FileInfoRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("反序列化FileInfoRequest失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		cm.sendResp(File, FileInfo, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID和Path
	errorResp.TaskID = req.TaskID
	errorResp.Path = req.Path

	// 使用读锁保护文件信息获取
	fileMutex.RLock()
	resp, err := getUnixFileInfo(req.Path, req.TaskID)
	fileMutex.RUnlock()

	if err != nil {
		errorResp.Error = err.Error()
		cm.sendResp(File, FileInfo, errorResp)
		return
	}

	cm.sendResp(File, FileInfo, resp)

}
