//go:build linux
// +build linux

package common

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
)

// FileCreateRequest 文件创建请求
type FileCreateRequest struct {
	TaskID     uint64 `json:"task_id"`     // 任务ID
	Path       string `json:"path"`        // 文件路径
	Content    string `json:"content"`     // 文件内容
	Encoding   string `json:"encoding"`    // 文件编码
	Force      bool   `json:"force"`       // 是否强制覆盖
	CreateDirs bool   `json:"create_dirs"` // 是否创建目录
	FileMode   uint32 `json:"file_mode"`   // 文件权限模式
}

// FileCreateResponse 文件创建响应
type FileCreateResponse struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Success      bool   `json:"success"`       // 操作是否成功
	Permissions  string `json:"permissions"`   // 文件权限
	Exists       bool   `json:"exists"`        // 文件是否已存在
	BytesWritten int64  `json:"bytes_written"` // 写入的字节数
	Error        string `json:"error"`         // 错误信息
	ActualPath   string `json:"actual_path"`   // 实际文件路径
	CreatedPath  string `json:"created_path"`  // 创建的文件路径
}

// handleFileCreate 处理文件创建请求
func (cm *ConnectionManager) handleFileCreate(packet *Packet) {
	var req FileCreateRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := &FileCreateResponse{
		TaskID:  0,
		Success: false,
		Error:   "",
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("反序列化FileCreateRequest失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "请求格式错误"
		cm.sendResp(File, FileCreate, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	// 使用写锁保护文件创建
	fileMutex.Lock()
	resp := createFile(&req)
	fileMutex.Unlock()

	cm.sendResp(File, FileCreate, resp)
}

// createFile 创建文件
func createFile(req *FileCreateRequest) *FileCreateResponse {
	resp := &FileCreateResponse{
		TaskID:     req.TaskID,
		ActualPath: req.Path,
	}

	// 获取绝对路径
	absPath, err := filepath.Abs(req.Path)
	if err != nil {
		resp.Error = fmt.Sprintf("获取绝对路径失败: %v", err)
		return resp
	}
	resp.ActualPath = absPath
	resp.CreatedPath = absPath

	// 检查文件是否已存在
	if _, err = os.Stat(absPath); err == nil {
		resp.Exists = true
		if !req.Force {
			resp.Error = "文件已存在，使用force参数强制覆盖"
			return resp
		}
	}

	// 创建目录（如果需要）
	if req.CreateDirs {
		dir := filepath.Dir(absPath)
		if err = os.MkdirAll(dir, 0755); err != nil {
			resp.Error = fmt.Sprintf("创建目录失败: %v", err)
			return resp
		}
	}

	// 设置文件权限模式
	fileMode := os.FileMode(0644) // 默认权限
	if req.FileMode != 0 {
		fileMode = os.FileMode(req.FileMode)
	}

	// 写入文件内容
	content := []byte(req.Content)
	err = os.WriteFile(absPath, content, fileMode)
	if err != nil {
		resp.Error = fmt.Sprintf("创建文件失败: %v", err)
		return resp
	}

	// 获取文件信息
	fileInfo, err := os.Stat(absPath)
	if err != nil {
		resp.Error = fmt.Sprintf("获取文件信息失败: %v", err)
		return resp
	}

	resp.Success = true
	resp.BytesWritten = int64(len(content))
	resp.Permissions = formatUnixPermissions(fileInfo.Mode())
	log.Printf("文件创建成功: %s (大小: %d 字节, 权限: %s)", absPath, len(content), resp.Permissions)
	return resp
}
