//go:build darwin
// +build darwin
package common

import (
	"context"
	"errors"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net"
	"sync"
	"time"

	"github.com/xtaci/smux"
)

// 基于iox-master的协议定义
const (
	CTL_HANDSHAKE = iota
	CTL_CONNECT_ME
	CTL_CLEANUP

	MAX_CONNECTION   = 0x800
	CLIENT_HANDSHAKE = 0xC0
	SERVER_HANDSHAKE = 0xE0
)

// 协议结构
type Protocol struct {
	CMD byte
	N   byte
}

var PROTO_END = []byte{0xEE, 0xFF}

// 协议编解码函数
func marshal(p Protocol) []byte {
	buf := make([]byte, 4)
	buf[0] = p.CMD
	buf[1] = p.N
	buf[2], buf[3] = PROTO_END[0], PROTO_END[1]
	return buf
}

func unmarshal(b []byte) Protocol {
	return Protocol{
		CMD: b[0],
		N:   b[1],
	}
}

func bytesEq(a, b []byte) bool {
	for i := 0; i < len(a); i++ {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func readUntilEnd(conn net.Conn) ([]byte, error) {
	buf := make([]byte, 1)
	output := make([]byte, 0, 4)

	for {
		n, err := conn.Read(buf)
		if err != nil {
			return nil, err
		}

		if n != 1 || len(output) > 4 {
			return nil, errors.New("transmission error")
		}

		output = append(output, buf[0])

		if len(output) == 4 && bytesEq(PROTO_END, output[len(output)-2:]) {
			break
		}
	}

	return output[:2], nil
}

// ProxyRequest 代理请求结构
type ProxyRequest struct {
	TaskID      uint64 `json:"task_id"`
	ProxyID     string `json:"proxy_id"`
	Type        string `json:"type"`         // forward, reverse
	Name        string `json:"name"`
	UserPort    uint16 `json:"user_port"`
	ClientPort  uint16 `json:"client_port"`  // 服务器分配的ClientPort
	AllocMode   string `json:"alloc_mode"`   // auto, manual
	ManualAlloc bool   `json:"manual_alloc"`
}

// ProxyResponse 代理响应结构
type ProxyResponse struct {
	TaskID  uint64 `json:"task_id"`
	Success bool   `json:"success"`
	ProxyID string `json:"proxy_id"`
	Port    uint16 `json:"port,omitempty"`
	Error   string `json:"error,omitempty"`
}

// CheckPortRequest 端口检查请求
type CheckPortRequest struct {
	TaskID uint64 `json:"task_id"`
	Port   uint16 `json:"port"`
	Type   string `json:"type"`
}

// CheckPortResponse 端口检查响应
type CheckPortResponse struct {
	TaskID    uint64 `json:"task_id"`
	Available bool   `json:"available"`
	Port      uint16 `json:"port"`
	Error     string `json:"error,omitempty"`
}

// ProxyManager 代理管理器
type ProxyManager struct {
	proxies    map[string]*ProxyInstance
	mutex      sync.RWMutex
	cm         *ConnectionManager
	ctx        context.Context
	cancelFunc context.CancelFunc
}

// ProxyInstance 代理实例
type ProxyInstance struct {
	ProxyID     string
	Type        string
	Port        uint16 // UserPort
	ClientPort  uint16 // 服务器分配的ClientPort
	Listener    net.Listener
	ctx         context.Context
	cancel      context.CancelFunc
	connections map[string]net.Conn
	connMutex   sync.RWMutex
}

// NewProxyManager 创建代理管理器
func NewProxyManager(cm *ConnectionManager) *ProxyManager {
	ctx, cancel := context.WithCancel(context.Background())
	return &ProxyManager{
		proxies:    make(map[string]*ProxyInstance),
		cm:         cm,
		ctx:        ctx,
		cancelFunc: cancel,
	}
}

// handleProxyRequest 处理代理请求
func (cm *ConnectionManager) handleProxyRequest(packet *Packet) {
	if cm.proxyManager == nil {
		cm.proxyManager = NewProxyManager(cm)
	}

	switch packet.Header.Code {
	case CheckPort:
		cm.handleCheckPortRequest(packet)
	case ProxyStart:
		cm.handleProxyStartRequest(packet)
	case ProxyStop:
		cm.handleProxyStopRequest(packet)
	case ProxyDelete:
		cm.handleProxyDeleteRequest(packet)
	default:
		log.Printf("未知的代理操作类型: %d", packet.Header.Code)
	}
}

// handleCheckPortRequest 处理端口检查请求
func (cm *ConnectionManager) handleCheckPortRequest(packet *Packet) {
	var req CheckPortRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析端口检查请求失败: %v", err)
		return
	}

	log.Printf("🔍 检查端口可用性: %d", req.Port)

	// 检查端口是否可用
	available := cm.isPortAvailable(req.Port)
	
	resp := CheckPortResponse{
		TaskID:    req.TaskID,
		Available: available,
		Port:      req.Port,
	}

	if !available {
		resp.Error = fmt.Sprintf("端口 %d 不可用", req.Port)
	}

	cm.sendResp(Proxy,CheckPort, resp)
}

// handleProxyStartRequest 处理代理启动请求
func (cm *ConnectionManager) handleProxyStartRequest(packet *Packet) {
	var req ProxyRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析代理启动请求失败: %v", err)
		return
	}

	log.Printf("🚀 [CLIENT] 启动代理: %s (类型: %s, 端口: %d)", req.Name, req.Type, req.UserPort)
	log.Printf("📋 [CLIENT] 代理详细信息: ProxyID=%s, TaskID=%d, AllocMode=%s, ManualAlloc=%t",
		req.ProxyID, req.TaskID, req.AllocMode, req.ManualAlloc)

	var resp ProxyResponse
	resp.TaskID = req.TaskID
	resp.ProxyID = req.ProxyID

	switch req.Type {
	case "forward":
		log.Printf("🔄 [CLIENT] 开始启动正向代理，调用startForwardProxy")
		port, err := cm.startForwardProxy(&req)
		if err != nil {
			resp.Success = false
			resp.Error = err.Error()
			log.Printf("🔴 [CLIENT] 启动正向代理失败: %v", err)
		} else {
			resp.Success = true
			resp.Port = port
			log.Printf("✅ [CLIENT] 正向代理启动成功，监听端口: %d", port)
		}
	case "reverse":
		err := cm.startReverseProxy(&req)
		if err != nil {
			resp.Success = false
			resp.Error = err.Error()
			log.Printf("❌ 启动反向代理失败: %v", err)
		} else {
			resp.Success = true
			log.Printf("✅ 反向代理启动成功")
		}
	default:
		resp.Success = false
		resp.Error = fmt.Sprintf("不支持的代理类型: %s", req.Type)
		log.Printf("❌ 不支持的代理类型: %s", req.Type)
	}

	cm.sendResp(Proxy,ProxyStart, resp)
}

// handleProxyStopRequest 处理代理停止请求
func (cm *ConnectionManager) handleProxyStopRequest(packet *Packet) {
	var req ProxyRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析代理停止请求失败: %v", err)
		return
	}

	log.Printf("🛑 停止代理: %s", req.ProxyID)

	resp := ProxyResponse{
		TaskID:  req.TaskID,
		ProxyID: req.ProxyID,
	}

	if err := cm.proxyManager.StopProxy(req.ProxyID); err != nil {
		resp.Success = false
		resp.Error = err.Error()
		log.Printf("❌ 停止代理失败: %v", err)
	} else {
		resp.Success = true
		log.Printf("✅ 代理停止成功")
	}

	cm.sendResp(Proxy,ProxyStop, resp)
}

// handleProxyDeleteRequest 处理代理删除请求
func (cm *ConnectionManager) handleProxyDeleteRequest(packet *Packet) {
	var req ProxyRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析代理删除请求失败: %v", err)
		return
	}

	log.Printf("🗑️ 删除代理: %s", req.ProxyID)

	resp := ProxyResponse{
		TaskID:  req.TaskID,
		ProxyID: req.ProxyID,
	}

	if err := cm.proxyManager.DeleteProxy(req.ProxyID); err != nil {
		resp.Success = false
		resp.Error = err.Error()
		log.Printf("❌ 删除代理失败: %v", err)
	} else {
		resp.Success = true
		log.Printf("✅ 代理删除成功")
	}

	cm.sendResp(Proxy,ProxyDelete, resp)
}


// isPortAvailable 检查端口是否可用
func (cm *ConnectionManager) isPortAvailable(port uint16) bool {
	address := fmt.Sprintf(":%d", port)
	listener, err := net.Listen("tcp", address)
	if err != nil {
		return false
	}
	listener.Close()
	return true
}

// startForwardProxy 启动正向代理（Client启动SOCKS5服务）
func (cm *ConnectionManager) startForwardProxy(req *ProxyRequest) (uint16, error) {
	log.Printf("🚀 [CLIENT-FORWARD] 开始启动正向代理")
	log.Printf("📋 [CLIENT-FORWARD] 请求参数: ProxyID=%s, UserPort=%d, ManualAlloc=%t, AllocMode=%s",
		req.ProxyID, req.UserPort, req.ManualAlloc, req.AllocMode)

	var port uint16

	// 端口分配逻辑
	if req.ManualAlloc && req.UserPort > 0 {
		port = req.UserPort
		log.Printf("📌 [CLIENT-FORWARD] 使用手动指定端口: %d", port)
	} else {
		// 自动分配端口
		log.Printf("🎯 [CLIENT-FORWARD] 自动分配端口")
		var err error
		port, err = cm.findAvailablePort()
		if err != nil {
			log.Printf("🔴 [CLIENT-FORWARD] 无法找到可用端口: %v", err)
			return 0, fmt.Errorf("无法找到可用端口: %v", err)
		}
		log.Printf("✅ [CLIENT-FORWARD] 自动分配端口成功: %d", port)
	}

	// 检查端口是否可用
	log.Printf("🔍 [CLIENT-FORWARD] 检查端口可用性: %d", port)
	if !cm.isPortAvailable(port) {
		log.Printf("🔴 [CLIENT-FORWARD] 端口不可用: %d", port)
		return 0, fmt.Errorf("端口 %d 不可用", port)
	}
	log.Printf("✅ [CLIENT-FORWARD] 端口可用性检查通过: %d", port)

	// 启动SOCKS5服务器
	log.Printf("🔧 [CLIENT-FORWARD] 创建代理实例")
	ctx, cancel := context.WithCancel(cm.proxyManager.ctx)
	instance := &ProxyInstance{
		ProxyID:     req.ProxyID,
		Type:        req.Type,
		Port:        port,
		ctx:         ctx,
		cancel:      cancel,
		connections: make(map[string]net.Conn),
	}

	listenAddr := fmt.Sprintf(":%d", port)
	log.Printf("🌐 [CLIENT-FORWARD] 启动TCP监听器: %s", listenAddr)
	listener, err := net.Listen("tcp", listenAddr)
	if err != nil {
		log.Printf("🔴 [CLIENT-FORWARD] 启动监听器失败: %v", err)
		cancel()
		return 0, fmt.Errorf("启动监听器失败: %v", err)
	}
	log.Printf("✅ [CLIENT-FORWARD] TCP监听器启动成功: %s", listenAddr)

	instance.Listener = listener

	// 保存代理实例
	log.Printf("💾 [CLIENT-FORWARD] 保存代理实例到管理器")
	cm.proxyManager.mutex.Lock()
	cm.proxyManager.proxies[req.ProxyID] = instance
	cm.proxyManager.mutex.Unlock()

	// 启动SOCKS5服务协程
	log.Printf("🔄 [CLIENT-FORWARD] 启动SOCKS5服务器协程")
	go cm.runForwardProxyServer(instance)

	log.Printf("✅ [CLIENT-FORWARD] 正向代理启动完成，返回端口: %d", port)
	return port, nil
}

// startReverseProxy 启动反向代理（Client连接Server）
func (cm *ConnectionManager) startReverseProxy(req *ProxyRequest) error {
	ctx, cancel := context.WithCancel(cm.proxyManager.ctx)
	instance := &ProxyInstance{
		ProxyID:     req.ProxyID,
		Type:        req.Type,
		Port:        req.UserPort,
		ClientPort:  req.ClientPort, // 服务器分配的ClientPort
		ctx:         ctx,
		cancel:      cancel,
		connections: make(map[string]net.Conn),
	}

	// 保存代理实例
	cm.proxyManager.mutex.Lock()
	cm.proxyManager.proxies[req.ProxyID] = instance
	cm.proxyManager.mutex.Unlock()

	// 启动反向代理客户端协程
	go cm.runReverseProxyClient(instance)

	return nil
}

// findAvailablePort 查找可用端口
func (cm *ConnectionManager) findAvailablePort() (uint16, error) {
	return cm.findAvailablePortInRange(10000, 20000)
}

// findAvailablePortInRange 在指定范围内查找可用端口
func (cm *ConnectionManager) findAvailablePortInRange(start, end uint16) (uint16, error) {
	log.Printf("🔍 [CLIENT] 在端口范围 %d-%d 中查找可用端口", start, end)

	// 随机起始点，避免多个客户端同时从同一端口开始
	offset := uint16(rand.Intn(int(end - start + 1)))

	for i := uint16(0); i <= end-start; i++ {
		port := start + (offset+i)%(end-start+1)
		if cm.isPortAvailable(port) {
			log.Printf("✅ [CLIENT] 找到可用端口: %d", port)
			return port, nil
		}
	}

	log.Printf("🔴 [CLIENT] 在范围 %d-%d 中未找到可用端口", start, end)
	return 0, fmt.Errorf("在范围 %d-%d 中没有可用端口", start, end)
}

// runForwardProxyServer 运行正向代理SOCKS5服务器
func (cm *ConnectionManager) runForwardProxyServer(instance *ProxyInstance) {
	defer instance.Listener.Close()
	defer instance.cancel()

	log.Printf("🌐 正向代理SOCKS5服务器开始监听端口: %d", instance.Port)

	for {
		// 检查上下文是否已取消
		select {
		case <-instance.ctx.Done():
			log.Printf("🛑 正向代理服务器停止: %s", instance.ProxyID)
			return
		default:
		}

		conn, err := instance.Listener.Accept()
		if err != nil {
			if instance.ctx.Err() != nil {
				return // 上下文已取消
			}
			log.Printf("接受连接失败: %v", err)
			continue
		}

		// 为每个连接启动处理协程
		go cm.handleSocks5Connection(instance, conn)
	}
}

// runReverseProxyClient 运行反向代理客户端
func (cm *ConnectionManager) runReverseProxyClient(instance *ProxyInstance) {
	defer instance.cancel()

	log.Printf("🔄 反向代理客户端启动: %s, 准备连接服务器ClientPort", instance.ProxyID)

	// 获取服务器地址
	serverAddr := cm.getServerAddress()
	if serverAddr == "" {
		log.Printf("❌ 无法获取服务器地址")
		return
	}

	// 反向代理连接重试逻辑
	maxRetries := 5
	retryDelay := time.Second * 2

	for retryCount := 0; retryCount < maxRetries; retryCount++ {
		select {
		case <-instance.ctx.Done():
			return
		default:
			if cm.connectToServerClientPort(instance, serverAddr, retryCount) {
				return // 连接成功并处理完毕
			}

			// 连接失败，等待重试
			if retryCount < maxRetries-1 {
				log.Printf("⏳ 反向代理连接失败，%v后重试 (%d/%d)", retryDelay, retryCount+1, maxRetries)
				time.Sleep(retryDelay)
				retryDelay *= 2 // 指数退避
			}
		}
	}

	log.Printf("❌ 反向代理客户端连接失败，已达到最大重试次数: %s", instance.ProxyID)
}

// getServerAddress 获取服务器地址
func (cm *ConnectionManager) getServerAddress() string {
	if cm.conn == nil {
		return ""
	}

	// 从现有连接获取服务器地址
	if addr, ok := cm.conn.RemoteAddr().(*net.TCPAddr); ok {
		return addr.IP.String()
	}

	return ""
}

// connectToServerClientPort 连接到服务器的ClientPort (基于iox-master的smux实现)
func (cm *ConnectionManager) connectToServerClientPort(instance *ProxyInstance, serverAddr string, retryCount int) bool {
	// 构建ClientPort地址 (使用服务器分配的ClientPort)
	clientPortAddr := fmt.Sprintf("%s:%d", serverAddr, instance.ClientPort)

	log.Printf("🔗 尝试连接服务器ClientPort: %s (重试: %d)", clientPortAddr, retryCount)

	// 建立TCP连接
	conn, err := net.DialTimeout("tcp", clientPortAddr, 10*time.Second)
	if err != nil {
		log.Printf("❌ 连接服务器ClientPort失败: %v", err)
		return false
	}

	log.Printf("✅ 成功建立TCP连接到服务器ClientPort: %s", clientPortAddr)

	// 建立smux客户端session
	session, err := smux.Client(conn, &smux.Config{
		Version:           2,
		KeepAliveInterval: 30 * time.Second,
		KeepAliveTimeout:  60 * time.Second,
		MaxFrameSize:      32768,
		MaxReceiveBuffer:  4194304,
		MaxStreamBuffer:   65536,
	})
	if err != nil {
		conn.Close()
		log.Printf("❌ 建立smux session失败: %v", err)
		return false
	}

	// 建立控制流
	ctlStream, err := session.OpenStream()
	if err != nil {
		session.Close()
		log.Printf("❌ 建立控制流失败: %v", err)
		return false
	}

	// 进行握手
	err = cm.clientHandshake(ctlStream)
	if err != nil {
		ctlStream.Close()
		session.Close()
		log.Printf("❌ 握手失败: %v", err)
		return false
	}

	log.Printf("✅ 握手成功，开始处理反向代理")

	// 处理控制流和数据流
	cm.handleSmuxSession(instance, session, ctlStream)

	return false // 连接断开后返回false，触发重连
}

// handleControlConnection 处理控制连接
func (cm *ConnectionManager) handleControlConnection(instance *ProxyInstance, controlConn net.Conn, serverAddr string) {
	defer controlConn.Close()
	defer func() {
		log.Printf("🔚 控制连接处理结束: %s", instance.ProxyID)
	}()

	log.Printf("📡 开始处理控制连接: %s", instance.ProxyID)

	for {
		// 检查上下文是否已取消
		select {
		case <-instance.ctx.Done():
			return
		default:
		}

		// 读取控制消息
		controlConn.SetReadDeadline(time.Now().Add(60 * time.Second))

		// 先读取第一个字节判断消息类型
		firstByte := make([]byte, 1)
		_, err := io.ReadFull(controlConn, firstByte)
		if err != nil {
			if err != io.EOF {
				log.Printf("❌ 读取控制消息失败: %v", err)
			}
			return
		}

		cmd := firstByte[0]

		// 处理心跳消息
		if cmd == 0xFF {
			// 心跳消息，继续循环
			continue
		}

		// 读取剩余的4字节连接ID
		idBuf := make([]byte, 4)
		_, err = io.ReadFull(controlConn, idBuf)
		if err != nil {
			log.Printf("❌ 读取连接ID失败: %v", err)
			return
		}

		connID := uint32(idBuf[0])<<24 | uint32(idBuf[1])<<16 | uint32(idBuf[2])<<8 | uint32(idBuf[3])

		switch cmd {
		case 0x01: // CMD_CONNECT_REQUEST
			log.Printf("📥 收到连接请求, connID: %d", connID)
			go cm.handleConnectRequest(instance, serverAddr, connID)

		default:
			log.Printf("⚠️ 未知控制命令: 0x%02x", cmd)
		}
	}
}

// handleConnectRequest 处理连接请求
func (cm *ConnectionManager) handleConnectRequest(instance *ProxyInstance, serverAddr string, connID uint32) {
	// 建立数据连接到服务器
	clientPortAddr := fmt.Sprintf("%s:%d", serverAddr, instance.ClientPort)
	dataConn, err := net.DialTimeout("tcp", clientPortAddr, 10*time.Second)
	if err != nil {
		log.Printf("❌ 建立数据连接失败: %v", err)
		return
	}
	defer dataConn.Close()

	// 发送连接ID (大端序)
	idBuf := make([]byte, 4)
	idBuf[0] = byte(connID >> 24)
	idBuf[1] = byte(connID >> 16)
	idBuf[2] = byte(connID >> 8)
	idBuf[3] = byte(connID)

	_, err = dataConn.Write(idBuf)
	if err != nil {
		log.Printf("❌ 发送连接ID失败: %v", err)
		return
	}

	log.Printf("📤 已发送连接ID: %d", connID)

	// 接收目标地址
	target, err := cm.receiveTarget(dataConn)
	if err != nil {
		log.Printf("❌ 接收目标地址失败: %v", err)
		return
	}

	log.Printf("🎯 收到目标地址: %s", target)

	// 连接到目标
	targetConn, err := net.DialTimeout("tcp", target, 10*time.Second)
	if err != nil {
		log.Printf("❌ 连接目标失败 %s: %v", target, err)
		return
	}
	defer targetConn.Close()

	log.Printf("✅ 成功连接到目标: %s", target)

	// 双向转发
	cm.forwardData(dataConn, targetConn)

	log.Printf("✅ 双向转发完成, connID: %d", connID)
}

// receiveTarget 接收目标地址
func (cm *ConnectionManager) receiveTarget(conn net.Conn) (string, error) {
	// 设置读超时
	conn.SetReadDeadline(time.Now().Add(15 * time.Second))

	// 读取长度 (2字节，大端序)
	lengthBuf := make([]byte, 2)
	_, err := io.ReadFull(conn, lengthBuf)
	if err != nil {
		return "", err
	}

	length := uint16(lengthBuf[0])<<8 | uint16(lengthBuf[1])
	if length == 0 || length > 256 {
		return "", fmt.Errorf("无效的目标地址长度: %d", length)
	}

	// 读取目标地址
	targetBuf := make([]byte, length)
	_, err = io.ReadFull(conn, targetBuf)
	if err != nil {
		return "", err
	}

	return string(targetBuf), nil
}

// forwardData 双向数据转发
func (cm *ConnectionManager) forwardData(dataConn, targetConn net.Conn) {
	done := make(chan struct{}, 2)

	// Data -> Target
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(targetConn, dataConn)
		if err != nil && err != io.EOF {
			log.Printf("❌ 数据->目标转发失败: %v", err)
		} else {
			log.Printf("📤 数据->目标转发完成: %d bytes", written)
		}
	}()

	// Target -> Data
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(dataConn, targetConn)
		if err != nil && err != io.EOF {
			log.Printf("❌ 目标->数据转发失败: %v", err)
		} else {
			log.Printf("📥 目标->数据转发完成: %d bytes", written)
		}
	}()

	// 等待任一方向结束
	<-done
}

// handleTargetConnection 处理目标连接
func (cm *ConnectionManager) handleTargetConnection(instance *ProxyInstance, serverConn net.Conn, targetAddr string) {
	// 连接到目标服务器
	targetConn, err := net.DialTimeout("tcp", targetAddr, 10*time.Second)
	if err != nil {
		log.Printf("❌ 连接目标服务器失败 %s: %v", targetAddr, err)
		return
	}
	defer targetConn.Close()

	log.Printf("✅ 成功连接到目标服务器: %s", targetAddr)

	// 双向数据转发
	done := make(chan struct{}, 2)

	// 服务器 -> 目标服务器
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(targetConn, serverConn)
		if err != nil && err != io.EOF {
			log.Printf("❌ 服务器->目标转发失败: %v", err)
		} else {
			log.Printf("📤 服务器->目标转发完成: %d bytes", written)
		}
	}()

	// 目标服务器 -> 服务器
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(serverConn, targetConn)
		if err != nil && err != io.EOF {
			log.Printf("❌ 目标->服务器转发失败: %v", err)
		} else {
			log.Printf("📥 目标->服务器转发完成: %d bytes", written)
		}
	}()

	// 等待任一方向完成
	<-done
	log.Printf("🔚 目标连接数据转发结束: %s", targetAddr)
}

// handleSocks5Connection 处理SOCKS5连接
func (cm *ConnectionManager) handleSocks5Connection(instance *ProxyInstance, clientConn net.Conn) {
	defer clientConn.Close()

	connID := fmt.Sprintf("%s-%d", clientConn.RemoteAddr().String(), time.Now().UnixNano())

	// 添加到连接管理
	instance.connMutex.Lock()
	instance.connections[connID] = clientConn
	instance.connMutex.Unlock()

	defer func() {
		instance.connMutex.Lock()
		delete(instance.connections, connID)
		instance.connMutex.Unlock()
	}()

	log.Printf("📡 新的SOCKS5连接: %s", connID)

	// SOCKS5握手
	if err := cm.socks5Handshake(clientConn); err != nil {
		log.Printf("SOCKS5握手失败: %v", err)
		return
	}

	// 处理SOCKS5请求
	targetAddr, err := cm.socks5Request(clientConn)
	if err != nil {
		log.Printf("SOCKS5请求处理失败: %v", err)
		return
	}

	// 连接目标服务器
	targetConn, err := net.DialTimeout("tcp", targetAddr, 10*time.Second)
	if err != nil {
		log.Printf("连接目标服务器失败 %s: %v", targetAddr, err)
		cm.socks5Reply(clientConn, 0x01) // General failure
		return
	}
	defer targetConn.Close()

	// 发送成功响应
	cm.socks5Reply(clientConn, 0x00) // Success

	log.Printf("✅ SOCKS5连接建立成功: %s -> %s", connID, targetAddr)

	// 数据转发
	cm.forwardData(clientConn, targetConn)
}

// socks5Handshake 执行SOCKS5握手
func (cm *ConnectionManager) socks5Handshake(conn net.Conn) error {
	// 读取客户端握手请求
	buffer := make([]byte, 256)
	n, err := conn.Read(buffer)
	if err != nil {
		return fmt.Errorf("读取握手请求失败: %v", err)
	}

	if n < 3 || buffer[0] != 0x05 {
		return fmt.Errorf("无效的SOCKS5握手请求")
	}

	// 发送握手响应（无认证）
	_, err = conn.Write([]byte{0x05, 0x00})
	if err != nil {
		return fmt.Errorf("发送握手响应失败: %v", err)
	}

	return nil
}

// socks5Request 处理SOCKS5请求
func (cm *ConnectionManager) socks5Request(conn net.Conn) (string, error) {
	buffer := make([]byte, 256)
	n, err := conn.Read(buffer)
	if err != nil {
		return "", fmt.Errorf("读取SOCKS5请求失败: %v", err)
	}

	if n < 7 || buffer[0] != 0x05 || buffer[1] != 0x01 {
		return "", fmt.Errorf("无效的SOCKS5请求")
	}

	// 解析目标地址
	var targetAddr string
	switch buffer[3] {
	case 0x01: // IPv4
		if n < 10 {
			return "", fmt.Errorf("IPv4地址数据不完整")
		}
		ip := fmt.Sprintf("%d.%d.%d.%d", buffer[4], buffer[5], buffer[6], buffer[7])
		port := uint16(buffer[8])<<8 + uint16(buffer[9])
		targetAddr = fmt.Sprintf("%s:%d", ip, port)
	case 0x03: // 域名
		if n < 7 {
			return "", fmt.Errorf("域名地址数据不完整")
		}
		domainLen := int(buffer[4])
		if n < 7+domainLen {
			return "", fmt.Errorf("域名地址数据不完整")
		}
		domain := string(buffer[5 : 5+domainLen])
		port := uint16(buffer[5+domainLen])<<8 + uint16(buffer[6+domainLen])
		targetAddr = fmt.Sprintf("%s:%d", domain, port)
	default:
		return "", fmt.Errorf("不支持的地址类型: %d", buffer[3])
	}

	return targetAddr, nil
}

// socks5Reply 发送SOCKS5响应
func (cm *ConnectionManager) socks5Reply(conn net.Conn, status byte) error {
	reply := []byte{0x05, status, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}
	_, err := conn.Write(reply)
	return err
}



// StopProxy 停止代理
func (pm *ProxyManager) StopProxy(proxyID string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	instance, exists := pm.proxies[proxyID]
	if !exists {
		return fmt.Errorf("代理不存在: %s", proxyID)
	}

	// 关闭所有连接
	instance.connMutex.Lock()
	for _, conn := range instance.connections {
		conn.Close()
	}
	instance.connMutex.Unlock()

	// 取消上下文
	instance.cancel()

	// 关闭监听器
	if instance.Listener != nil {
		instance.Listener.Close()
	}

	return nil
}

// DeleteProxy 删除代理
func (pm *ProxyManager) DeleteProxy(proxyID string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	instance, exists := pm.proxies[proxyID]
	if !exists {
		return fmt.Errorf("代理不存在: %s", proxyID)
	}

	// 停止代理
	instance.cancel()
	if instance.Listener != nil {
		instance.Listener.Close()
	}

	// 关闭所有连接
	instance.connMutex.Lock()
	for _, conn := range instance.connections {
		conn.Close()
	}
	instance.connMutex.Unlock()

	// 从管理器中删除
	delete(pm.proxies, proxyID)

	return nil
}

// clientHandshake 客户端握手
func (cm *ConnectionManager) clientHandshake(ctlStream *smux.Stream) error {
	// 发送握手请求
	_, err := ctlStream.Write(marshal(Protocol{
		CMD: CTL_HANDSHAKE,
		N:   CLIENT_HANDSHAKE,
	}))
	if err != nil {
		return err
	}

	// 读取握手响应
	pb, err := readUntilEnd(ctlStream)
	if err != nil {
		return errors.New("握手响应读取失败")
	}

	p := unmarshal(pb)
	if !(p.CMD == CTL_HANDSHAKE && p.N == SERVER_HANDSHAKE) {
		return errors.New("握手响应无效")
	}

	return nil
}

// handleSmuxSession 处理smux session
func (cm *ConnectionManager) handleSmuxSession(instance *ProxyInstance, session *smux.Session, ctlStream *smux.Stream) {
	defer session.Close()
	defer ctlStream.Close()

	log.Printf("📡 开始处理smux session: %s", instance.ProxyID)

	// 处理控制流消息
	go func() {
		for {
			select {
			case <-instance.ctx.Done():
				return
			default:
			}

			pb, err := readUntilEnd(ctlStream)
			if err != nil {
				log.Printf("❌ 控制流读取失败: %v", err)
				return
			}

			p := unmarshal(pb)
			switch p.CMD {
			case CTL_CONNECT_ME:
				// 收到连接请求，建立数据流
				for i := byte(0); i < p.N; i++ {
					go cm.handleDataStream(session)
				}
			case CTL_CLEANUP:
				log.Printf("📥 收到清理信号")
				return
			}
		}
	}()

	// 保持session活跃
	<-instance.ctx.Done()
}

// handleDataStream 处理数据流
func (cm *ConnectionManager) handleDataStream(session *smux.Session) {
	stream, err := session.OpenStream()
	if err != nil {
		log.Printf("❌ 建立数据流失败: %v", err)
		return
	}
	defer stream.Close()

	// 在数据流上处理SOCKS5连接
	cm.handleSocks5OnStream(stream)
}

// handleSocks5OnStream 在流上处理SOCKS5连接
func (cm *ConnectionManager) handleSocks5OnStream(stream net.Conn) {
	// SOCKS5握手
	if !cm.performSocks5Handshake(stream) {
		return
	}

	// 处理连接请求
	targetAddr, err := cm.handleSocks5ConnectRequest(stream)
	if err != nil {
		log.Printf("❌ SOCKS5连接请求失败: %v", err)
		return
	}

	// 连接到目标地址
	targetConn, err := net.DialTimeout("tcp", targetAddr, 10*time.Second)
	if err != nil {
		log.Printf("❌ 连接目标地址失败: %s, %v", targetAddr, err)
		// 发送连接失败响应
		stream.Write([]byte{0x05, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00})
		return
	}
	defer targetConn.Close()

	// 发送连接成功响应
	stream.Write([]byte{0x05, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00})

	log.Printf("✅ 成功连接到目标: %s", targetAddr)

	// 双向转发数据
	cm.forwardData(stream, targetConn)
}

// performSocks5Handshake 执行SOCKS5握手
func (cm *ConnectionManager) performSocks5Handshake(conn net.Conn) bool {
	// 读取客户端握手请求
	buf := make([]byte, 256)
	n, err := conn.Read(buf)
	if err != nil || n < 3 {
		log.Printf("❌ 读取SOCKS5握手请求失败: %v", err)
		return false
	}

	// 检查SOCKS版本
	if buf[0] != 0x05 {
		log.Printf("❌ 不支持的SOCKS版本: %d", buf[0])
		return false
	}

	// 发送握手响应 (无认证)
	_, err = conn.Write([]byte{0x05, 0x00})
	if err != nil {
		log.Printf("❌ 发送SOCKS5握手响应失败: %v", err)
		return false
	}

	return true
}

// handleSocks5ConnectRequest 处理SOCKS5连接请求
func (cm *ConnectionManager) handleSocks5ConnectRequest(conn net.Conn) (string, error) {
	// 读取连接请求
	buf := make([]byte, 256)
	n, err := conn.Read(buf)
	if err != nil || n < 7 {
		return "", fmt.Errorf("读取连接请求失败: %v", err)
	}

	// 检查请求格式
	if buf[0] != 0x05 || buf[1] != 0x01 || buf[2] != 0x00 {
		return "", fmt.Errorf("无效的连接请求")
	}

	var targetAddr string
	var port uint16

	// 解析地址类型
	switch buf[3] {
	case 0x01: // IPv4
		if n < 10 {
			return "", fmt.Errorf("IPv4地址数据不完整")
		}
		targetAddr = fmt.Sprintf("%d.%d.%d.%d", buf[4], buf[5], buf[6], buf[7])
		port = uint16(buf[8])<<8 | uint16(buf[9])
	case 0x03: // 域名
		if n < 5 {
			return "", fmt.Errorf("域名数据不完整")
		}
		domainLen := int(buf[4])
		if n < 5+domainLen+2 {
			return "", fmt.Errorf("域名数据不完整")
		}
		targetAddr = string(buf[5 : 5+domainLen])
		port = uint16(buf[5+domainLen])<<8 | uint16(buf[5+domainLen+1])
	case 0x04: // IPv6
		return "", fmt.Errorf("暂不支持IPv6")
	default:
		return "", fmt.Errorf("不支持的地址类型: %d", buf[3])
	}

	return fmt.Sprintf("%s:%d", targetAddr, port), nil
}
