//go:build darwin
// +build darwin
package common

import (
	"fmt"
	"log"
	"os"
	"strings"
)

// DirDeleteRequest 目录删除请求
type DirDeleteRequest struct {
	TaskID    uint64 `json:"task_id"`   // 任务ID
	Path      string `json:"path"`      // 目录路径
	Recursive bool   `json:"recursive"` // 是否递归删除
	Force     bool   `json:"force"`     // 是否强制删除（忽略只读属性）
}

// DirDeleteResponse 目录删除响应
type DirDeleteResponse struct {
	TaskID      uint64 `json:"task_id"`      // 任务ID
	Success     bool   `json:"success"`      // 操作是否成功
	NotExists   bool   `json:"not_exists"`   // 目录是否不存在
	NotAllow    bool   `json:"not_allow"`    // 是否权限不足
	NotEmpty    bool   `json:"not_empty"`    // 目录是否非空
	DeletedPath string `json:"deleted_path"` // 实际删除的路径
	Error       string `json:"error"`        // 错误信息
}

// handleDirDelete 处理目录删除请求
func (cm *ConnectionManager) handleDirDelete(packet *Packet) {
	var req DirDeleteRequest
	// 创建错误响应结构体，TaskID初始化为0
	respErr := &DirDeleteResponse{
		TaskID:  0,
		Success: false,
		Error:   "",
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("目录删除请求反序列化失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		respErr.Error = fmt.Sprintf("目录删除请求反序列化失败: %v", err)
		cm.sendResp(Dir, DirDelete, respErr)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	respErr.TaskID = req.TaskID

	// 参数验证
	path := strings.TrimSpace(req.Path)
	if path == "" {
		log.Printf("目录路径为空")
		respErr.Error = "目录路径为空"
		cm.sendResp(Dir, DirDelete, respErr)
		return
	}

	// 路径安全检查
	if !isValidPath(path) {
		log.Printf("无效的路径: %s", path)
		respErr.Error = fmt.Sprintf("无效的路径: %s", path)
		cm.sendResp(Dir, DirDelete, respErr)
		return
	}

	// 获取绝对路径
	absPath, err := getAbsolutePath(path)
	if err != nil {
		log.Printf("获取绝对路径失败: %v", err)
		respErr.Error = fmt.Sprintf("获取绝对路径失败: %v", err)
		cm.sendResp(Dir, DirDelete, respErr)
		return
	}

	// 使用互斥锁防止并发删除冲突
	dirOpMutex.Lock()
	defer dirOpMutex.Unlock()

	resp := cm.deleteDirectory(absPath, &req)
	cm.sendResp(Dir, DirDelete, resp)
}

// deleteDirectory 删除目录的核心逻辑
func (cm *ConnectionManager) deleteDirectory(path string, req *DirDeleteRequest) *DirDeleteResponse {
	resp := &DirDeleteResponse{
		TaskID:      req.TaskID,
		Success:     false,
		NotExists:   false,
		NotAllow:    false,
		NotEmpty:    false,
		DeletedPath: "",
		Error:       "",
	}

	// 检查路径是否存在且是目录
	exists, err := isDirectory(path)
	if err != nil {
		log.Printf("检查路径出错: %v", err)
		if os.IsNotExist(err) {
			resp.NotExists = true
			resp.Error = "目录不存在"
		} else if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "权限不足"
		} else {
			resp.Error = fmt.Sprintf("路径检查失败: %v", err)
		}
		return resp
	}

	if !exists {
		resp.NotExists = true
		resp.Error = "目录不存在"
		return resp
	}

	// 检查目录是否为空（如果不是递归删除）
	if !req.Recursive {
		empty, err := isDirEmpty(path)
		if err != nil {
			log.Printf("检查目录是否为空出错: %v", err)
			if os.IsPermission(err) {
				resp.NotAllow = true
				resp.Error = "权限不足"
			} else {
				resp.Error = fmt.Sprintf("检查目录状态失败: %v", err)
			}
			return resp
		}

		if !empty {
			resp.NotEmpty = true
			resp.Error = "目录非空且未启用递归删除"
			return resp
		}
	}

	// 如果启用强制删除，尝试移除只读属性
	if req.Force {
		if err := removeReadOnlyAttribute(path); err != nil {
			log.Printf("移除只读属性失败: %v", err)
			// 不阻止删除操作，只记录警告
		}
	}

	// 执行删除操作
	var deleteErr error
	if req.Recursive {
		deleteErr = os.RemoveAll(path)
	} else {
		deleteErr = os.Remove(path)
	}

	if deleteErr != nil {
		log.Printf("目录删除失败: %v", deleteErr)
		if os.IsPermission(deleteErr) {
			resp.NotAllow = true
			resp.Error = "删除权限不足"
		} else if os.IsNotExist(deleteErr) {
			resp.NotExists = true
			resp.Error = "目录不存在"
		} else {
			resp.Error = fmt.Sprintf("删除失败: %v", deleteErr)
		}
		return resp
	}

	// 验证删除结果
	if stillExists, _ := PathExists(path); stillExists {
		resp.Error = "删除验证失败"
	} else {
		resp.Success = true
		resp.DeletedPath = path
	}

	return resp
}
