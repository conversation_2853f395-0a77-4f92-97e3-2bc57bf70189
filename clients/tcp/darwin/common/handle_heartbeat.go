//go:build darwin
// +build darwin

package common

import (
	"context"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net"
	"net/http"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/load"
	"github.com/shirou/gopsutil/v3/mem"
	psnet "github.com/shirou/gopsutil/v3/net"
)

// CreateHeartbeatPacket 创建心跳包
func (cm *ConnectionManager) CreateHeartbeatPacket(isPong bool) (*Packet, error) {
	var Code uint8
	if isPong {
		Code = PONG
	} else {
		Code = PING
	}
	packet := cm.CreatePacket([]byte("heartbeat"), Heartbeat, Code)
	// 加密数据包
	if err := packet.EncryptPacket(cm.metadata); err != nil {
		return nil, fmt.Errorf("加密心跳包失败: %v", err)
	}
	return packet, nil
}

// ClientHeartbeatRequest 客户端心跳请求结构体
type ClientHeartbeatRequest struct {
	ClientID    string              `json:"client_id"`
	Timestamp   time.Time           `json:"timestamp"`
	SequenceNum uint64              `json:"sequence_num"`
	SystemInfo  ClientSystemStatus  `json:"system_info"`
	NetworkInfo ClientNetworkStatus `json:"network_info"`
	Type        uint8               `json:"type"`
	Jitter      int                 `json:"jitter"`
}

// ClientSystemStatus 客户端系统状态
type ClientSystemStatus struct {
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage float64 `json:"memory_usage"`
	DiskUsage   float64 `json:"disk_usage"`
	Uptime      int64   `json:"uptime"`
	LoadAvg     float64 `json:"load_avg"`
	OS          string  `json:"os"`
	Arch        string  `json:"arch"`
}

// ClientNetworkStatus 客户端网络状态
type ClientNetworkStatus struct {
	LocalIP    string  `json:"local_ip"`
	PublicIP   string  `json:"public_ip"`
	Latency    int64   `json:"latency"`
	PacketLoss float64 `json:"packet_loss"`
	Bandwidth  int64   `json:"bandwidth"`
}

// startClientHeartbeat 启动客户端主动心跳检测
func (cm *ConnectionManager) startClientHeartbeat(ctx context.Context) {
	// 心跳间隔：30秒 + 随机抖动
	baseInterval := 30 * time.Second

	// 添加±20%的随机抖动，避免所有客户端同时发送心跳
	jitter := time.Duration(float64(baseInterval) * 0.2 * (rand.Float64()*2 - 1))
	heartbeatInterval := baseInterval + jitter

	// 确保间隔不小于10秒
	if heartbeatInterval < 10*time.Second {
		heartbeatInterval = 10 * time.Second
	}

	log.Printf("Darwin客户端心跳检测已启动，间隔: %v", heartbeatInterval)

	ticker := time.NewTicker(heartbeatInterval)
	defer ticker.Stop()

	// 服务器响应超时计数
	timeoutCount := 0
	maxTimeoutCount := 3 // 最大超时3次就认为服务器离线

	for {
		select {
		case <-ctx.Done():
			log.Println("Darwin客户端心跳检测收到退出信号")
			return
		case <-ticker.C:
			// 发送心跳包到服务器
			if err := cm.sendClientHeartbeat(); err != nil {
				timeoutCount++
				log.Printf("发送Darwin客户端心跳失败 (%d/%d): %v", timeoutCount, maxTimeoutCount, err)

				if timeoutCount >= maxTimeoutCount {
					log.Printf("服务器连续%d次心跳超时，认为服务器离线，断开连接", maxTimeoutCount)
					// 触发重连机制
					if cm.conn != nil {
						cm.conn.Close()
					}
					return
				}
			} else {
				// 心跳发送成功，重置超时计数
				if timeoutCount > 0 {
					log.Printf("Darwin心跳恢复正常，重置超时计数")
					timeoutCount = 0
				}
				log.Println("Darwin客户端心跳发送成功")
			}
		}
	}
}

// sendClientHeartbeat 发送结构化客户端心跳包
func (cm *ConnectionManager) sendClientHeartbeat() error {
	// 创建结构化心跳请求
	heartbeatReq := cm.createClientHeartbeatRequest()

	// 序列化心跳数据
	heartbeatData, err := cm.serializer.Serialize(heartbeatReq)
	if err != nil {
		return fmt.Errorf("序列化心跳数据失败: %v", err)
	}

	// 创建TLV包
	heartbeatPacket, err := cm.createAdvancedHeartbeatPacket(heartbeatData, PING)
	if err != nil {
		return fmt.Errorf("创建心跳包失败: %v", err)
	}

	// 序列化并发送
	packetBytes := heartbeatPacket.Serialize()

	cm.mu.Lock()
	conn := cm.conn
	cm.mu.Unlock()

	if conn == nil {
		return fmt.Errorf("连接已断开")
	}

	if _, err := conn.Write(packetBytes); err != nil {
		return fmt.Errorf("发送心跳包失败: %v", err)
	}

	return nil
}

// createClientHeartbeatRequest 创建客户端心跳请求
func (cm *ConnectionManager) createClientHeartbeatRequest() *ClientHeartbeatRequest {
	return &ClientHeartbeatRequest{
		ClientID:    cm.getClientID(),
		Timestamp:   time.Now(),
		SequenceNum: cm.generateSequenceNumber(),
		SystemInfo:  cm.getDarwinSystemStatus(),
		NetworkInfo: cm.getDarwinNetworkStatus(),
		Type:        PING,
		Jitter:      cm.generateJitter(),
	}
}

// createAdvancedHeartbeatPacket 创建高级心跳包
func (cm *ConnectionManager) createAdvancedHeartbeatPacket(data []byte, heartbeatType uint8) (*Packet, error) {
	packet := &Packet{
		Header: &Header{
			Type:  Heartbeat,
			Code:  heartbeatType,
			Label: cm.generateLabel(),
		},
		PacketData: &PacketData{
			Data: data,
		},
	}

	// 加密数据包
	if err := packet.EncryptPacket(cm.metadata); err != nil {
		return nil, fmt.Errorf("加密心跳包失败: %v", err)
	}

	return packet, nil
}

// getClientID 获取客户端ID
func (cm *ConnectionManager) getClientID() string {
	// 使用主机名和时间戳生成唯一ID
	hostname, _ := os.Hostname()
	return fmt.Sprintf("Darwin_%s_%d", hostname, time.Now().Unix())
}

// generateSequenceNumber 生成序列号
func (cm *ConnectionManager) generateSequenceNumber() uint64 {
	return uint64(time.Now().UnixNano())
}

// generateJitter 生成随机抖动值
func (cm *ConnectionManager) generateJitter() int {
	return rand.Intn(5000)
}

// generateLabel 生成标签
func (cm *ConnectionManager) generateLabel() uint32 {
	return uint32(time.Now().Unix())
}

// getDarwinSystemStatus 获取Darwin系统状态（异步优化）
func (cm *ConnectionManager) getDarwinSystemStatus() ClientSystemStatus {
	var wg sync.WaitGroup
	var cpuUsage, memoryUsage, diskUsage, loadAvg float64
	var uptime int64

	// 异步获取各项系统信息
	wg.Add(5)

	// 获取CPU使用率
	go func() {
		defer wg.Done()
		cpuUsage = cm.getDarwinCPUUsage()
	}()

	// 获取内存使用率
	go func() {
		defer wg.Done()
		memoryUsage = cm.getDarwinMemoryUsage()
	}()

	// 获取磁盘使用率
	go func() {
		defer wg.Done()
		diskUsage = cm.getDarwinDiskUsage()
	}()

	// 获取系统运行时间
	go func() {
		defer wg.Done()
		uptime = cm.getDarwinUptime()
	}()

	// 获取系统负载
	go func() {
		defer wg.Done()
		loadAvg = cm.getDarwinLoadAvg()
	}()

	// 等待所有goroutine完成
	wg.Wait()

	return ClientSystemStatus{
		CPUUsage:    cpuUsage,
		MemoryUsage: memoryUsage,
		DiskUsage:   diskUsage,
		Uptime:      uptime,
		LoadAvg:     loadAvg,
		OS:          runtime.GOOS,
		Arch:        runtime.GOARCH,
	}
}

// Darwin网络测试缓存
var (
	lastDarwinNetworkTest     time.Time
	cachedDarwinNetworkStatus ClientNetworkStatus
	DarwinNetworkTestMutex    sync.Mutex
)

// getDarwinNetworkStatus 获取Darwin网络状态（异步优化 + 缓存）
func (cm *ConnectionManager) getDarwinNetworkStatus() ClientNetworkStatus {
	DarwinNetworkTestMutex.Lock()
	defer DarwinNetworkTestMutex.Unlock()

	// 如果距离上次网络测试不到5分钟，返回缓存结果
	if time.Since(lastDarwinNetworkTest) < 5*time.Minute && cachedDarwinNetworkStatus.LocalIP != "" {
		return cachedDarwinNetworkStatus
	}

	var wg sync.WaitGroup
	var localIP, publicIP string
	var latency int64
	var packetLoss float64
	var bandwidth int64

	// 异步获取网络信息
	wg.Add(4) // 减少并发数，移除频繁的网络测试

	// 获取本地IP
	go func() {
		defer wg.Done()
		localIP = cm.getDarwinLocalIP()
	}()

	// 获取公网IP（仅在缓存为空时）
	go func() {
		defer wg.Done()
		if cachedDarwinNetworkStatus.PublicIP == "" {
			publicIP = cm.getPublicIP()
		} else {
			publicIP = cachedDarwinNetworkStatus.PublicIP
		}
	}()

	// 获取到服务器的延迟和丢包率（降低频率）
	go func() {
		defer wg.Done()
		latency, packetLoss = cm.pingDarwinServerWithStats()
	}()

	go func() {
		defer wg.Done()
		bandwidth = cm.measureDarwinBandwidth()
	}()

	// 等待所有goroutine完成
	wg.Wait()

	// 更新缓存
	lastDarwinNetworkTest = time.Now()
	cachedDarwinNetworkStatus = ClientNetworkStatus{
		LocalIP:    localIP,
		PublicIP:   publicIP,
		Latency:    latency,
		PacketLoss: packetLoss,
		Bandwidth:  bandwidth,
	}

	return cachedDarwinNetworkStatus
}

// getDarwinCPUUsage 获取真实Darwin CPU使用率
func (cm *ConnectionManager) getDarwinCPUUsage() float64 {
	// 使用gopsutil获取CPU使用率
	percent, err := cpu.Percent(time.Second, false)
	if err != nil || len(percent) == 0 {
		log.Printf("获取CPU使用率失败: %v", err)
		return 0.0
	}
	return percent[0]
}

// getDarwinMemoryUsage 获取真实Darwin内存使用率
func (cm *ConnectionManager) getDarwinMemoryUsage() float64 {
	// 使用gopsutil获取内存信息
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		log.Printf("获取内存信息失败: %v", err)
		return 0.0
	}
	return memInfo.UsedPercent
}

// getDarwinDiskUsage 获取真实Darwin磁盘使用率
func (cm *ConnectionManager) getDarwinDiskUsage() float64 {
	// 使用gopsutil获取根分区使用率
	diskInfo, err := disk.Usage("/")
	if err != nil {
		log.Printf("获取磁盘信息失败: %v", err)
		return 0.0
	}
	return diskInfo.UsedPercent
}

// getDarwinUptime 获取真实Darwin系统运行时间
func (cm *ConnectionManager) getDarwinUptime() int64 {
	// 使用gopsutil获取系统启动时间
	bootTime, err := host.BootTime()
	if err != nil {
		log.Printf("获取系统启动时间失败: %v", err)
		return 0
	}
	// 返回运行时间（秒）
	return int64(time.Now().Unix() - int64(bootTime))
}

// getDarwinLoadAvg 获取真实Darwin系统负载
func (cm *ConnectionManager) getDarwinLoadAvg() float64 {
	// 使用gopsutil获取系统负载
	loadInfo, err := load.Avg()
	if err != nil {
		log.Printf("获取系统负载失败: %v", err)
		return 0.0
	}
	return loadInfo.Load1 // 返回1分钟平均负载
}

// getDarwinLocalIP 获取真实的Darwin本地IP地址
func (cm *ConnectionManager) getDarwinLocalIP() string {
	// 获取本机的外网IP地址（连接到服务器时使用的IP）
	if cm.conn != nil {
		if localAddr := cm.conn.LocalAddr(); localAddr != nil {
			if tcpAddr, ok := localAddr.(*net.TCPAddr); ok {
				return tcpAddr.IP.String()
			}
		}
	}

	// 备用方法：获取默认网卡的IP
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()

	if localAddr := conn.LocalAddr(); localAddr != nil {
		if udpAddr, ok := localAddr.(*net.UDPAddr); ok {
			return udpAddr.IP.String()
		}
	}

	return "127.0.0.1"
}

// getPublicIP 获取公网IP地址，使用多个备用API，逐个尝试直到成功
func (cm *ConnectionManager) getPublicIP() string {
	// 定义多个公网 IP 获取服务（按优先级排序）
	apiURLs := []string{
		"http://www.bt.cn/api/getipaddress", // 备用，HTTP
		"http://ipv4.ping0.cc/",
		"https://api.ipify.org",          // 高可用，国际
		"https://ident.me",               // 快速，纯文本
		"https://ipv4.icanhazip.com",     // 简洁可靠
		"https://ip.qq.com/cgi-bin/myip", // 腾讯云，国内友好

		"https://ifconfig.me/ip", // 经典服务
	}

	// 整体上下文超时：3秒内必须完成
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// 使用同一个 client（可复用连接）
	client := &http.Client{
		Timeout: 3 * time.Second, // 单个请求也设超时
	}

	for _, url := range apiURLs {
		select {
		case <-ctx.Done():
			// 整体超时，不再尝试
			return "unknown"
		default:
		}

		ip := cm.tryGetIP(ctx, client, url)
		if ip != "" && net.ParseIP(ip) != nil {
			return ip // 成功获取且是合法 IP
		}
		// 失败则尝试下一个
	}

	return "unknown"
}

// tryGetIP 尝试从指定 URL 获取 IP
func (cm *ConnectionManager) tryGetIP(ctx context.Context, client *http.Client, url string) string {
	log.Printf("tryGetIP: %s", url)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return ""
	}

	resp, err := client.Do(req)
	if err != nil {
		return ""
	}
	defer resp.Body.Close()

	// 只接受 200 OK
	if resp.StatusCode != http.StatusOK {
		return ""
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return ""
	}

	ip := strings.TrimSpace(string(body))
	// 做基本清理：去除换行、空格等
	ip = strings.Split(ip, "\n")[0]
	ip = strings.Split(ip, "\r")[0]
	ip = strings.TrimSpace(ip)
	log.Printf("ip is: %s", ip)
	return ip
}

// pingDarwinServerWithStats 使用ICMP ping获取到服务器的延迟和丢包率
func (cm *ConnectionManager) pingDarwinServerWithStats() (int64, float64) {
	if cm.conn == nil {
		return 0, 0.0
	}

	// 获取服务器地址
	serverAddr := cm.conn.RemoteAddr()
	if serverAddr == nil {
		return 0, 0.0
	}

	// 解析服务器IP
	tcpAddr, ok := serverAddr.(*net.TCPAddr)
	if !ok {
		return 0, 0.0
	}

	return cm.icmpPingDarwin(tcpAddr.IP.String())
}

// icmpPingDarwin 优先使用系统ping命令，失败则回退到TCP ping
func (cm *ConnectionManager) icmpPingDarwin(host string) (int64, float64) {
	// 首先尝试使用系统ping命令
	latency, packetLoss := cm.systemPingDarwin(host)
	if latency > 0 || packetLoss < 100.0 {
		log.Printf("系统ping成功: %s, 延迟=%dms, 丢包率=%.1f%%", host, latency, packetLoss)
		return latency, packetLoss
	}

	// 如果系统ping失败，回退到TCP连接测试
	log.Printf("系统ping失败，使用TCP连接测试: %s", host)
	return cm.tcpPingDarwinFallback(host)
}

// systemPingDarwin 使用Darwin系统ping命令
func (cm *ConnectionManager) systemPingDarwin(host string) (int64, float64) {
	// Darwin ping命令：ping -c 3 -W 2 host
	// -c 3: 发送3个包
	// -W 2: 超时2秒
	cmd := exec.Command("ping", "-c", "3", "-W", "2", host)
	output, err := cmd.Output()
	if err != nil {
		log.Printf("执行ping命令失败: %v", err)
		return 0, 100.0
	}

	return cm.parseDarwinPingOutput(string(output))
}

// parseDarwinPingOutput 解析Darwin ping命令的输出
func (cm *ConnectionManager) parseDarwinPingOutput(output string) (int64, float64) {
	lines := strings.Split(output, "\n")
	var totalLatency int64
	var successCount int
	var totalCount int

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 查找包含延迟信息的行，例如：
		// "64 bytes from ***********: icmp_seq=1 ttl=64 time=1.23 ms"
		if strings.Contains(line, "bytes from") && strings.Contains(line, "time=") {
			successCount++
			totalCount++

			// 提取延迟时间
			if timeIndex := strings.Index(line, "time="); timeIndex != -1 {
				timeStr := line[timeIndex+5:]
				if msIndex := strings.Index(timeStr, " ms"); msIndex != -1 {
					timeStr = timeStr[:msIndex]
					if latency, err := strconv.ParseFloat(timeStr, 64); err == nil {
						totalLatency += int64(latency)
					}
				}
			}
		}
	}

	// 从统计信息中获取总包数
	for _, line := range lines {
		if strings.Contains(line, "packets transmitted") {
			// 解析统计行，例如：
			// "3 packets transmitted, 2 received, 33% packet loss"
			parts := strings.Fields(line)
			if len(parts) >= 4 {
				if transmitted, err := strconv.Atoi(parts[0]); err == nil {
					totalCount = transmitted
				}
				if received, err := strconv.Atoi(parts[3]); err == nil {
					successCount = received
				}
			}
			break
		}
	}

	if totalCount == 0 {
		totalCount = 3 // 默认发送3个包
	}

	var avgLatency int64
	if successCount > 0 {
		avgLatency = totalLatency / int64(successCount)
	}

	packetLoss := float64(totalCount-successCount) / float64(totalCount) * 100.0

	return avgLatency, packetLoss
}

// tcpPingDarwinFallback TCP连接测试作为ICMP的回退方案
func (cm *ConnectionManager) tcpPingDarwinFallback(host string) (int64, float64) {
	// 获取服务器端口
	serverAddr := cm.conn.RemoteAddr()
	if serverAddr == nil {
		return 0, 100.0
	}

	tcpAddr, ok := serverAddr.(*net.TCPAddr)
	if !ok {
		return 0, 100.0
	}

	const pingCount = 3
	var totalLatency int64
	var successCount int

	for i := 0; i < pingCount; i++ {
		start := time.Now()
		conn, err := net.DialTimeout("tcp", host+":"+fmt.Sprintf("%d", tcpAddr.Port), 2*time.Second)
		if err != nil {
			continue
		}
		conn.Close()

		latency := time.Since(start).Milliseconds()
		totalLatency += latency
		successCount++

		time.Sleep(200 * time.Millisecond)
	}

	if successCount == 0 {
		return 0, 100.0
	}

	avgLatency := totalLatency / int64(successCount)
	packetLoss := float64(pingCount-successCount) / float64(pingCount) * 100.0

	return avgLatency, packetLoss
}

// measureDarwinBandwidth 获取网卡流量信息（当前带宽使用情况）
func (cm *ConnectionManager) measureDarwinBandwidth() int64 {
	// 获取网络接口统计信息
	interfaces, err := psnet.IOCounters(true)
	if err != nil {
		log.Printf("获取网络接口信息失败: %v", err)
		return 0
	}

	var totalBytesRecv, totalBytesSent uint64
	var activeInterfaces int

	// 遍历所有网络接口
	for _, iface := range interfaces {
		// 跳过回环接口和无流量的接口
		if iface.Name == "lo" || strings.Contains(strings.ToLower(iface.Name), "loopback") {
			continue
		}

		// 只统计有流量的接口
		if iface.BytesRecv > 0 || iface.BytesSent > 0 {
			totalBytesRecv += iface.BytesRecv
			totalBytesSent += iface.BytesSent
			activeInterfaces++

			log.Printf("网卡 %s: 接收=%d bytes, 发送=%d bytes",
				iface.Name, iface.BytesRecv, iface.BytesSent)
		}
	}

	if activeInterfaces == 0 {
		return 0
	}

	// 🚀 不再虚构网络带宽数据，返回真实的0值表示心跳检测不提供带宽信息
	// 真实的网络速度应该通过专门的网络监控API获取

	log.Printf("网络流量统计: 总接收=%d bytes, 总发送=%d bytes, 活跃接口=%d",
		totalBytesRecv, totalBytesSent, activeInterfaces)

	// 返回0表示心跳检测不提供带宽数据，避免虚构数据
	return 0
}

// parseServerHeartbeatResponse 解析服务器心跳响应
func (cm *ConnectionManager) parseServerHeartbeatResponse(data []byte) {
	// 定义服务器响应结构体（简化版）
	type ServerHeartbeatResponse struct {
		ServerID    string    `json:"server_id"`
		Timestamp   time.Time `json:"timestamp"`
		SequenceNum uint64    `json:"sequence_num"`
		ServerInfo  struct {
			Status    uint8  `json:"status"`
			Timestamp int64  `json:"timestamp"`
			Version   string `json:"version"`
		} `json:"server_info"`
		ClientInfo struct {
			ShouldReconnect bool     `json:"should_reconnect"`
			NewServerAddr   string   `json:"new_server_addr"`
			ConfigUpdate    bool     `json:"config_update"`
			Commands        []string `json:"commands"`
		} `json:"client_info"`
		Config struct {
			Interval    int `json:"interval"`
			Timeout     int `json:"timeout"`
			MaxRetries  int `json:"max_retries"`
			JitterRange int `json:"jitter_range"`
		} `json:"config"`
		Type   uint8 `json:"type"`
		Jitter int   `json:"jitter"`
	}

	var serverResp ServerHeartbeatResponse
	if err := cm.serializer.Deserialize(data, &serverResp); err != nil {
		log.Printf("解析服务器心跳响应失败: %v", err)
		return
	}

	log.Printf("Darwin客户端收到服务器心跳响应: ServerID=%s, Status=%d, Version=%s",
		serverResp.ServerID, serverResp.ServerInfo.Status, serverResp.ServerInfo.Version)

	// 处理服务器指令
	if serverResp.ClientInfo.ShouldReconnect {
		log.Printf("服务器要求Darwin客户端重连到: %s", serverResp.ClientInfo.NewServerAddr)
		// TODO: 实现重连逻辑
	}

	if serverResp.ClientInfo.ConfigUpdate {
		log.Println("服务器要求Darwin客户端更新配置")
		// TODO: 实现配置更新逻辑
	}

	if len(serverResp.ClientInfo.Commands) > 0 {
		log.Printf("服务器向Darwin客户端发送了%d个命令", len(serverResp.ClientInfo.Commands))
		// TODO: 实现命令执行逻辑
	}
}

// HandleHeartbeatResponse 处理服务器的心跳响应（由包处理器调用）
func (cm *ConnectionManager) HandleHeartbeatResponse() {
	// 这个方法可以被包处理器调用，用于处理服务器的PONG响应
	log.Println("Darwin客户端心跳响应处理完成")
}
