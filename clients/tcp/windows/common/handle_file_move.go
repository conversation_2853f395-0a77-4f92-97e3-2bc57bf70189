//go:build windows
// +build windows

package common

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
)

// FileMoveRequest 文件移动请求
type FileMoveRequest struct {
	TaskID          uint64 `json:"task_id"`          // 任务ID
	Source          string `json:"source"`           // 源文件路径
	Destination     string `json:"destination"`      // 目标文件路径
	Force           bool   `json:"force"`            // 是否强制覆盖
	CreateDirs      bool   `json:"create_dirs"`      // 是否创建目标目录
	PreserveAttrs   bool   `json:"preserve_attrs"`   // 是否保留文件属性
	VerifyIntegrity bool   `json:"verify_integrity"` // 是否验证完整性
}

// FileMoveResponse 文件移动响应
type FileMoveResponse struct {
	TaskID            uint64 `json:"task_id"`            // 任务ID
	Success           bool   `json:"success"`            // 操作是否成功
	SourceExists      bool   `json:"source_exists"`      // 源文件是否存在
	DestinationExists bool   `json:"destination_exists"` // 目标文件是否已存在
	NotAllow          bool   `json:"not_allow"`          // 是否权限不足
	Error             string `json:"error"`              // 错误信息
	ActualSource      string `json:"actual_source"`      // 实际源路径
	ActualDestination string `json:"actual_destination"` // 实际目标路径
	BytesMoved        int64  `json:"bytes_moved"`        // 移动的字节数
}

// handleFileMove 处理文件移动请求
func (cm *ConnectionManager) handleFileMove(packet *Packet) {
	var req FileMoveRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := &FileMoveResponse{
		TaskID:            0,
		Success:           false,
		SourceExists:      false,
		NotAllow:          false,
		DestinationExists: false,
		Error:             "请求格式错误",
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("反序列化FileMoveRequest失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		cm.sendResp(File, FileMove, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	// 使用写锁保护文件移动
	fileMutex.Lock()
	resp := moveFile(&req)
	fileMutex.Unlock()

	cm.sendResp(File, FileMove, resp)
}

// moveFile 执行文件移动的核心逻辑
func moveFile(req *FileMoveRequest) *FileMoveResponse {
	// 参数验证
	if req.Source == "" || req.Destination == "" {
		return &FileMoveResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "源路径或目标路径不能为空",
		}
	}

	// 获取绝对路径
	absSource, err := getAbsolutePath(req.Source)
	if err != nil {
		return &FileMoveResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   fmt.Sprintf("源路径解析失败: %v", err),
		}
	}

	absDestination, err := getAbsolutePath(req.Destination)
	if err != nil {
		return &FileMoveResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   fmt.Sprintf("目标路径解析失败: %v", err),
		}
	}

	// 初始化响应
	resp := &FileMoveResponse{
		TaskID:            req.TaskID,
		Success:           false,
		SourceExists:      false,
		DestinationExists: false,
		NotAllow:          false,
		ActualSource:      absSource,
		ActualDestination: absDestination,
	}

	// 检查源文件是否存在
	sourceExists, err := PathExists(absSource)
	if err != nil {
		log.Printf("检查源文件出错: %v", err)
		resp.Error = fmt.Sprintf("检查源文件失败: %v", err)
		handleFileMoveError(err, resp)
		return resp
	}

	if !sourceExists {
		resp.SourceExists = false
		resp.Error = "源文件不存在"
		return resp
	}
	resp.SourceExists = true

	// 获取源文件信息
	srcInfo, err := os.Stat(absSource)
	if err != nil {
		resp.Error = fmt.Sprintf("获取源文件信息失败: %v", err)
		return resp
	}

	// 检查目标文件是否存在
	destExists, err := PathExists(absDestination)
	if err != nil {
		log.Printf("检查目标路径出错: %v", err)
		resp.Error = fmt.Sprintf("检查目标路径失败: %v", err)
		handleFileMoveError(err, resp)
		return resp
	}
	resp.DestinationExists = destExists

	// 处理目标文件已存在的情况
	if destExists && !req.Force {
		log.Printf("目标文件已存在且未启用强制覆盖: %s", absDestination)
		resp.Error = "目标文件已存在"
		return resp
	}

	// 如果需要创建目录
	if req.CreateDirs {
		destDir := filepath.Dir(absDestination)
		if err := os.MkdirAll(destDir, 0755); err != nil {
			log.Printf("创建目标目录失败: %v", err)
			resp.Error = fmt.Sprintf("创建目标目录失败: %v", err)
			handleFileMoveError(err, resp)
			return resp
		}
	}

	// 尝试直接移动（重命名）
	moveErr := MoveFile(absSource, absDestination)
	if moveErr != nil {
		// 如果直接移动失败，尝试复制后删除
		log.Printf("直接移动失败，尝试复制后删除: %v", moveErr)

		// 复制文件
		var copyErr error
		if req.PreserveAttrs {
			copyErr = copyFileWithAttributes(absSource, absDestination, true)
		} else {
			copyErr = copyFileContents(absSource, absDestination)
		}

		if copyErr != nil {
			log.Printf("文件复制失败: %v", copyErr)
			resp.Error = fmt.Sprintf("移动失败: %v", copyErr)
			handleFileMoveError(copyErr, resp)
			return resp
		}

		// 验证复制完整性
		if req.VerifyIntegrity {
			if !verifyFileCopy(absSource, absDestination) {
				resp.Error = "文件移动完整性验证失败"
				_ = os.Remove(absDestination) // 清理失败的复制
				return resp
			}
		}

		// 删除源文件
		if err := DelFile(absSource); err != nil {
			log.Printf("删除源文件失败: %v", err)
			resp.Error = fmt.Sprintf("复制成功但删除源文件失败: %v", err)
			return resp
		}
	}

	// 验证移动结果
	if moved, _ := PathExists(absDestination); moved {
		if stillAtSource, _ := PathExists(absSource); !stillAtSource {
			resp.Success = true
			resp.BytesMoved = srcInfo.Size()
			log.Printf("文件移动成功: %s -> %s (大小: %d 字节)", absSource, absDestination, srcInfo.Size())
		} else {
			resp.Error = "目标文件已创建但源文件仍然存在"
		}
	} else {
		resp.Error = "移动操作完成但目标文件不存在"
	}

	return resp
}
