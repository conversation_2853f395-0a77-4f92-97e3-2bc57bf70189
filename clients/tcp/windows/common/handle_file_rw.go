//go:build windows
// +build windows

package common

import (
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/minio/sha256-simd"
)

// FileReadRequest 文件内容读取请求
type FileReadRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
	Path   string `json:"path"`    // 文件路径
}

// FileReadResponse 文件内容读取响应
type FileReadResponse struct {
	TaskID     uint64 `json:"task_id"`     // 任务ID
	Success    bool   `json:"success"`     // 操作是否成功
	NotExist   bool   `json:"not_exist"`   // 文件是否不存在
	NotAllow   bool   `json:"not_allow"`   // 是否权限不足
	TooLarge   bool   `json:"too_large"`   // 文件是否过大
	IsBinary   bool   `json:"is_binary"`   // 是否为二进制文件
	Content    string `json:"content"`     // 文件内容
	FileSize   int64  `json:"file_size"`   // 文件总大小
	ReadSize   int64  `json:"read_size"`   // 实际读取大小
	Encoding   string `json:"encoding"`    // 检测到的文件编码
	Error      string `json:"error"`       // 错误信息
	ActualPath string `json:"actual_path"` // 实际文件路径
}

// FileWriteRequest 文件内容写入请求
type FileWriteRequest struct {
	TaskID     uint64 `json:"task_id"`     // 任务ID
	Path       string `json:"path"`        // 文件路径
	Content    string `json:"content"`     // 文件内容
	CreateDirs bool   `json:"create_dirs"` // 是否创建目录
	Backup     bool   `json:"backup"`      // 是否创建备份
}

// FileWriteResponse 文件内容写入响应
type FileWriteResponse struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Success      bool   `json:"success"`       // 操作是否成功
	NotAllow     bool   `json:"not_allow"`     // 是否权限不足
	IsDir        bool   `json:"is_dir"`        // 目标是否为目录
	BackupPath   string `json:"backup_path"`   // 备份文件路径
	BytesWritten int64  `json:"bytes_written"` // 写入的字节数
	Error        string `json:"error"`         // 错误信息
	ActualPath   string `json:"actual_path"`   // 实际文件路径
}

// handleFileRead 处理文件内容读取请求
func (cm *ConnectionManager) handleFileRead(packet *Packet) {
	var req FileReadRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := &FileReadResponse{
		TaskID:  0,
		Success: false,
		Error:   "",
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("反序列化FileReadRequest失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "请求格式错误"
		cm.sendResp(File, FileRead, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	// 使用读锁保护文件读取
	fileMutex.RLock()
	resp := readFileContent(&req)
	fileMutex.RUnlock()

	cm.sendResp(File, FileRead, resp)
}

// handleFileWrite 处理文件内容写入请求
func (cm *ConnectionManager) handleFileWrite(packet *Packet) {
	var req FileWriteRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := &FileWriteResponse{
		TaskID:  0,
		Success: false,
		Error:   "",
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("反序列化FileWriteRequest失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "请求格式错误"
		cm.sendResp(File, FileWrite, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	// 使用写锁保护文件写入
	fileMutex.Lock()
	resp := writeFileContent(&req)
	fileMutex.Unlock()

	cm.sendResp(File, FileWrite, resp)
}

// readFileContent 读取文件内容
func readFileContent(req *FileReadRequest) *FileReadResponse {
	resp := &FileReadResponse{
		TaskID:     req.TaskID,
		ActualPath: req.Path,
	}

	// 获取绝对路径
	absPath, err := filepath.Abs(req.Path)
	if err != nil {
		resp.Error = fmt.Sprintf("获取绝对路径失败: %v", err)
		return resp
	}
	resp.ActualPath = absPath

	// 检查文件是否存在
	fileInfo, err := os.Stat(absPath)
	if err != nil {
		if os.IsNotExist(err) {
			resp.NotExist = true
			resp.Error = "文件不存在"
		} else {
			resp.Error = fmt.Sprintf("获取文件信息失败: %v", err)
		}
		return resp
	}

	// 检查是否为目录
	if fileInfo.IsDir() {
		resp.Error = "目标是目录，不能读取内容"
		return resp
	}

	// 智能文件大小限制
	resp.FileSize = fileInfo.Size()
	maxSize := getMaxFileSizeForFile(absPath, resp.FileSize)
	if resp.FileSize > maxSize {
		resp.TooLarge = true
		resp.Error = fmt.Sprintf("文件过大，超过%s限制", formatFileSize(maxSize))
		return resp
	}

	// 读取文件内容
	content, err := os.ReadFile(absPath)
	if err != nil {
		resp.Error = fmt.Sprintf("读取文件失败: %v", err)
		return resp
	}

	// 检查文件是否为二进制文件
	resp.IsBinary = isBinaryFile(content, absPath)

	resp.Success = true
	if resp.IsBinary {
		// 对于二进制文件，使用Base64编码
		resp.Content = base64.StdEncoding.EncodeToString(content)
		resp.Encoding = "base64"
		log.Printf("二进制文件读取成功: %s (大小: %d 字节, Base64编码)", absPath, len(content))
	} else {
		// 对于文本文件，直接转换为字符串
		resp.Content = string(content)
		resp.Encoding = "utf-8"
		log.Printf("文本文件读取成功: %s (大小: %d 字节)", absPath, len(content))
	}
	resp.ReadSize = int64(len(content))
	return resp
}

// writeFileContent 写入文件内容
func writeFileContent(req *FileWriteRequest) *FileWriteResponse {
	resp := &FileWriteResponse{
		TaskID:     req.TaskID,
		ActualPath: req.Path,
	}

	// 获取绝对路径
	absPath, err := filepath.Abs(req.Path)
	if err != nil {
		resp.Error = fmt.Sprintf("获取绝对路径失败: %v", err)
		return resp
	}
	resp.ActualPath = absPath

	// 检查目标是否为目录
	if fileInfo, err := os.Stat(absPath); err == nil && fileInfo.IsDir() {
		resp.IsDir = true
		resp.Error = "目标是目录，不能写入内容"
		return resp
	}

	// 创建目录（如果需要）
	if req.CreateDirs {
		dir := filepath.Dir(absPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			resp.Error = fmt.Sprintf("创建目录失败: %v", err)
			return resp
		}
	}

	// 计算新内容的SHA256哈希
	newContent := []byte(req.Content)
	newHash := sha256.Sum256(newContent)

	// 检查文件是否存在，如果存在则比较哈希值
	fileExists := false
	if _, err := os.Stat(absPath); err == nil {
		fileExists = true
		// 读取现有文件内容并计算哈希
		existingContent, err := os.ReadFile(absPath)
		if err == nil {
			existingHash := sha256.Sum256(existingContent)
			// 如果哈希值相同，说明内容没有变化，直接返回成功
			if newHash == existingHash {
				resp.Success = true
				resp.BytesWritten = int64(len(newContent))
				log.Printf("文件内容未变化，跳过写入: %s", absPath)
				return resp
			}
		}
	}

	// 创建备份（如果需要且文件存在且内容有变化）
	if req.Backup && fileExists {
		backupPath := absPath + ".bak"
		if err := copyFileContents(absPath, backupPath); err != nil {
			log.Printf("创建备份失败: %v", err)
		} else {
			resp.BackupPath = backupPath
			log.Printf("创建备份: %s", backupPath)
		}
	}

	// 写入文件内容
	err = os.WriteFile(absPath, newContent, 0644)
	if err != nil {
		if os.IsPermission(err) {
			resp.NotAllow = true
		}
		resp.Error = fmt.Sprintf("写入文件失败: %v", err)
		return resp
	}

	resp.Success = true
	resp.BytesWritten = int64(len(newContent))
	log.Printf("文件写入成功: %s (大小: %d 字节)", absPath, len(newContent))
	return resp
}

// isBinaryFile 检查文件是否为二进制文件
func isBinaryFile(content []byte, filePath string) bool {
	// 首先根据文件扩展名判断
	ext := strings.ToLower(filepath.Ext(filePath))

	// 常见的二进制文件扩展名
	binaryExtensions := map[string]bool{
		".jpg": true, ".jpeg": true, ".png": true, ".gif": true, ".bmp": true,
		".ico": true, ".webp": true, ".tiff": true, ".tif": true,
		".mp4": true, ".avi": true, ".mov": true, ".wmv": true, ".flv": true,
		".mkv": true, ".webm": true, ".3gp": true,
		".mp3": true, ".wav": true, ".flac": true, ".aac": true, ".ogg": true,
		".wma": true, ".m4a": true,
		".pdf": true, ".doc": true, ".docx": true, ".xls": true, ".xlsx": true,
		".ppt": true, ".pptx": true,
		".zip": true, ".rar": true, ".7z": true, ".tar": true, ".gz": true,
		".exe": true, ".dll": true, ".so": true, ".dylib": true,
		".bin": true, ".dat": true, ".db": true, ".sqlite": true,
	}

	if binaryExtensions[ext] {
		return true
	}

	// 常见的文本文件扩展名
	textExtensions := map[string]bool{
		".txt": true, ".md": true, ".json": true, ".xml": true, ".html": true,
		".css": true, ".js": true, ".ts": true, ".jsx": true, ".tsx": true,
		".vue": true, ".py": true, ".go": true, ".java": true, ".c": true,
		".cpp": true, ".h": true, ".hpp": true, ".cs": true, ".php": true,
		".rb": true, ".sh": true, ".bat": true, ".ps1": true, ".yaml": true,
		".yml": true, ".toml": true, ".ini": true, ".cfg": true, ".conf": true,
		".log": true, ".csv": true, ".sql": true,
	}

	if textExtensions[ext] {
		return false
	}

	// 如果扩展名不在已知列表中，检查文件内容
	// 检查前512字节是否包含null字节或其他二进制特征
	checkSize := 512
	if len(content) < checkSize {
		checkSize = len(content)
	}

	for i := 0; i < checkSize; i++ {
		// 检查是否包含null字节
		if content[i] == 0 {
			return true
		}
		// 检查是否包含过多的控制字符（除了常见的换行、制表符等）
		if content[i] < 32 && content[i] != 9 && content[i] != 10 && content[i] != 13 {
			return true
		}
	}

	return false
}
