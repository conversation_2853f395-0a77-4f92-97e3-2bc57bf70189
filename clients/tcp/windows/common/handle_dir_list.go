//go:build windows
// +build windows

package common

import (
	"fmt"
	"io/fs"
	"log"
	"math"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

// DirListRequest 目录列表请求
type DirListRequest struct {
	TaskID     uint64 `json:"task_id"`     // 任务ID
	Path       string `json:"path"`        // 目录路径
	Recursive  bool   `json:"recursive"`   // 是否递归列出子目录
	ShowHidden bool   `json:"show_hidden"` // 是否显示隐藏文件
	MaxDepth   int    `json:"max_depth"`   // 最大递归深度
	// 分页参数
	Page     int `json:"page"`      // 页码，从1开始
	PageSize int `json:"page_size"` // 每页大小，默认50
	// 排序参数
	SortBy    string `json:"sort_by"`    // 排序字段：name, size, mod_time, type
	SortOrder string `json:"sort_order"` // 排序顺序：asc, desc
}

// DirListResponse 目录列表响应
type DirListResponse struct {
	TaskID            uint64             `json:"task_id"`             // 任务ID
	Path              string             `json:"path"`                // 请求的路径
	ActualPath        string             `json:"actual_path"`         // 实际访问的路径
	NotExists         bool               `json:"not_exists"`          // 目录是否不存在
	NotAllow          bool               `json:"not_allow"`           // 是否权限不足
	FileInfoResponses []FileInfoResponse `json:"file_info_responses"` // 文件信息列表
	TotalCount        int                `json:"total_count"`         // 总文件数量
	Error             string             `json:"error"`               // 错误信息
	// 分页信息
	Page       int  `json:"page"`        // 当前页码
	PageSize   int  `json:"page_size"`   // 每页大小
	TotalPages int  `json:"total_pages"` // 总页数
	HasMore    bool `json:"has_more"`    // 是否还有更多数据
}

// handleDirList 处理目录列表请求
func (cm *ConnectionManager) handleDirList(packet *Packet) {
	// 创建错误响应结构体，TaskID初始化为0
	respErr := &DirListResponse{
		TaskID: 0,
		Error:  "",
		Path:   "",
	}

	var req DirListRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("目录列表请求反序列化失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		respErr.Error = fmt.Sprintf("目录列表请求反序列化失败: %v", err)
		cm.sendResp(Dir, DirList, respErr)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID和Path
	respErr.TaskID = req.TaskID
	respErr.Path = req.Path
	// 参数验证和标准化
	path := strings.TrimSpace(req.Path)
	if path == "" {
		if workDir, err := os.Getwd(); err == nil {
			path = workDir
		} else {
			log.Printf("获取当前工作目录失败: %v", err)
			respErr.Error = fmt.Sprintf("获取当前工作目录失败: %v", err)
			cm.sendResp(Dir, DirList, respErr)
			return
		}
	}

	// 路径安全检查
	if !isValidPath(path) {
		log.Printf("无效的路径: %s", path)
		respErr.Error = fmt.Sprintf("无效的路径: %s", path)
		cm.sendResp(Dir, DirList, respErr)
		return
	}

	// 获取绝对路径
	absPath, err := getAbsolutePath(path)
	if err != nil {
		log.Printf("获取绝对路径失败: %v", err)
		respErr.Error = fmt.Sprintf("获取绝对路径失败: %v", err)
		cm.sendResp(Dir, DirList, respErr)
		return
	}

	resp := getDirListResp(absPath, &req)
	cm.sendResp(Dir, DirList, resp)
}

// getDirListResp 获取目录列表响应的增强版本
func getDirListResp(root string, req *DirListRequest) *DirListResponse {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 50
	}
	// 限制最大页面大小，防止内存溢出
	if req.PageSize > 1000 {
		req.PageSize = 1000
	}
	// 设置默认排序参数
	if req.SortBy == "" {
		req.SortBy = "name"
	}
	if req.SortOrder == "" {
		req.SortOrder = "asc"
	}

	resp := &DirListResponse{
		TaskID:            req.TaskID,
		Path:              req.Path,
		ActualPath:        root,
		NotExists:         false,
		NotAllow:          false,
		FileInfoResponses: []FileInfoResponse{},
		TotalCount:        0,
		Error:             "",
		Page:              req.Page,
		PageSize:          req.PageSize,
		TotalPages:        0,
		HasMore:           false,
	}

	// 检查路径是否存在
	exist, err := PathExists(root)
	if err != nil {
		log.Printf("查找文件夹是否存在出错: %v", err)
		if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "权限不足"
		} else {
			resp.Error = fmt.Sprintf("路径检查失败: %v", err)
		}
		return resp
	}

	if !exist {
		// 如果路径不存在，返回当前工作目录
		workDir, err := os.Getwd()
		if err != nil {
			resp.NotExists = true
			resp.Error = "获取当前目录失败"
			return resp
		}
		resp.NotExists = true
		resp.ActualPath = workDir
		resp.FileInfoResponses = getMultipleFileInfo(workDir, req)
		resp.TotalCount = len(resp.FileInfoResponses)
		return resp
	}

	// 解析符号链接目录
	actualPath := root
	if linkInfo, err := os.Lstat(root); err == nil {
		if linkInfo.Mode()&os.ModeSymlink != 0 {
			// 这是一个符号链接，解析实际路径
			if resolved, err := filepath.EvalSymlinks(root); err == nil {
				actualPath = resolved
				resp.ActualPath = actualPath
				log.Printf("符号链接 %s 解析为 %s", root, actualPath)
			} else {
				log.Printf("解析符号链接失败 %s: %v", root, err)
				resp.Error = fmt.Sprintf("解析符号链接失败: %v", err)
				return resp
			}
		}
	}

	// 确保解析后的路径是目录
	if stat, err := os.Stat(actualPath); err != nil {
		log.Printf("访问解析后的路径失败 %s: %v", actualPath, err)
		if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "权限不足"
		} else {
			resp.Error = fmt.Sprintf("访问路径失败: %v", err)
		}
		return resp
	} else if !stat.IsDir() {
		resp.Error = "路径不是目录"
		return resp
	}

	// 获取所有文件信息列表
	allFiles := getMultipleFileInfo(actualPath, req)
	resp.TotalCount = len(allFiles)

	// 排序文件列表
	sortFileList(allFiles, req.SortBy, req.SortOrder)

	// 计算分页信息
	totalPages := int(math.Ceil(float64(resp.TotalCount) / float64(req.PageSize)))
	resp.TotalPages = totalPages
	resp.HasMore = req.Page < totalPages

	// 应用分页
	startIndex := (req.Page - 1) * req.PageSize
	endIndex := startIndex + req.PageSize

	if startIndex >= len(allFiles) {
		// 页码超出范围，返回空列表
		resp.FileInfoResponses = []FileInfoResponse{}
	} else {
		if endIndex > len(allFiles) {
			endIndex = len(allFiles)
		}
		resp.FileInfoResponses = allFiles[startIndex:endIndex]
	}

	log.Printf("目录列表分页信息: 总数=%d, 页码=%d/%d, 当前页大小=%d",
		resp.TotalCount, req.Page, totalPages, len(resp.FileInfoResponses))

	return resp
}

// getMultipleFileInfo 获取多个文件信息的增强版本
func getMultipleFileInfo(path string, req *DirListRequest) []FileInfoResponse {
	var fileInfoResponses []FileInfoResponse
	maxDepth := req.MaxDepth
	if maxDepth <= 0 {
		maxDepth = 1 // 默认只列出当前目录
	}

	walkFunc := func(currentPath string, d fs.DirEntry, err error) error {
		// 跳过当前目录本身，只列出其内容
		if currentPath == path {
			return nil
		}

		// 计算当前深度
		relPath, _ := filepath.Rel(path, currentPath)
		depth := strings.Count(relPath, string(filepath.Separator))
		if relPath == "." {
			depth = 0
		}

		// 检查深度限制
		if !req.Recursive && depth > 0 {
			return filepath.SkipDir
		}
		if req.Recursive && depth >= maxDepth {
			if d.IsDir() {
				return filepath.SkipDir
			}
		}

		if err != nil {
			log.Printf("访问 %q 失败: %v", currentPath, err)
			if os.IsPermission(err) {
				// 尝试获取基本信息
				if fileInfoResponse, infoErr := getWindowsFileInfo(currentPath, req.TaskID); infoErr == nil {
					fileInfoResponses = append(fileInfoResponses, *fileInfoResponse)
				}
			}
			return nil // 继续处理其他文件
		}

		// 跳过隐藏文件（如果配置要求）
		if !req.ShowHidden && strings.HasPrefix(d.Name(), ".") && currentPath != path {
			if d.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		fileInfoResponse, err := getWindowsFileInfo(currentPath, req.TaskID)
		if err != nil {
			log.Printf("获取文件信息失败 %s: %v", currentPath, err)
			return nil // 继续处理其他文件
		}
		fileInfoResponses = append(fileInfoResponses, *fileInfoResponse)
		return nil
	}

	if err := filepath.WalkDir(path, walkFunc); err != nil {
		log.Printf("遍历目录发生错误: %v", err)
	}

	return fileInfoResponses
}

// sortFileList 对文件列表进行排序
func sortFileList(files []FileInfoResponse, sortBy, sortOrder string) {
	sort.Slice(files, func(i, j int) bool {
		var less bool

		// 目录优先排序（目录总是排在文件前面）
		if files[i].IsDir != files[j].IsDir {
			return files[i].IsDir
		}

		switch sortBy {
		case "name":
			less = strings.ToLower(files[i].Name) < strings.ToLower(files[j].Name)
		case "size":
			less = files[i].Size < files[j].Size
		case "mod_time":
			// 直接比较 time.Time 类型
			less = files[i].ModTime.Before(files[j].ModTime)
		case "type":
			// 按文件扩展名排序
			ext1 := filepath.Ext(files[i].Name)
			ext2 := filepath.Ext(files[j].Name)
			if ext1 == ext2 {
				// 扩展名相同时按名称排序
				less = strings.ToLower(files[i].Name) < strings.ToLower(files[j].Name)
			} else {
				less = strings.ToLower(ext1) < strings.ToLower(ext2)
			}
		default:
			// 默认按名称排序
			less = strings.ToLower(files[i].Name) < strings.ToLower(files[j].Name)
		}

		// 根据排序顺序决定返回值
		if sortOrder == "desc" {
			return !less
		}
		return less
	})
}
