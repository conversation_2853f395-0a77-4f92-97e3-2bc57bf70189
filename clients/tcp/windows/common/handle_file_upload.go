//go:build windows
// +build windows

package common

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"
)

type FileUploadRequest struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Destination  string `json:"destination"`   // 目标文件路径
	StartChunk   int64  `json:"start_chunk"`   // 起始分块编号（-1表示取消信令）
	CurrentChunk int64  `json:"current_chunk"` // 当前分块编号（从0开始）
	TotalChunk   int64  `json:"total_chunk"`   // 总分块数
	ChunkContent []byte `json:"chunk_content"` // 当前分块内容
	Force        bool   `json:"force"`         // 是否强制覆盖
	FileSize     int64  `json:"file_size"`     // 文件总大小
	FileHash     string `json:"file_hash"`     // 文件SHA256哈希（用于完整性验证）
}

type FileUploadResponse struct {
	TaskID            uint64 `json:"task_id"`            // 任务ID
	Success           bool   `json:"success"`            // 操作是否成功
	NotAllow          bool   `json:"not_allow"`          // 是否权限不足
	DestinationExists bool   `json:"destination_exists"` // 目标文件是否已存在
	CurrentChunk      int64  `json:"current_chunk"`      // 已接收的分块编号
	TotalChunk        int64  `json:"total_chunk"`        // 总分块数
	Completed         bool   `json:"completed"`          // 是否传输完成
	Error             string `json:"error"`              // 错误信息
	ReceivedSize      int64  `json:"received_size"`      // 已接收的字节数
}

// handleFileUpload 处理文件上传请求
func (cm *ConnectionManager) handleFileUpload(packet *Packet) {
	var req FileUploadRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := &FileUploadResponse{
		TaskID:       0,
		Success:      false,
		Error:        "请求格式错误",
		CurrentChunk: 0,
		TotalChunk:   0,
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("反序列化FileUploadRequest失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		cm.sendResp(File, FileUpload, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	// 检查是否为取消信令
	if req.StartChunk == -1 {
		log.Printf("收到上传取消信令: 任务ID=%d", req.TaskID)
		uploadTaskTrackers.Lock()
		if tracker, exists := uploadTaskTrackers.tasks[req.TaskID]; exists {
			if tracker.cancelFunc != nil {
				tracker.cancelFunc()
			}
			delete(uploadTaskTrackers.tasks, req.TaskID)
		}
		uploadTaskTrackers.Unlock()
		return
	}

	// 使用写锁保护文件上传
	fileMutex.Lock()
	resp := uploadFile(&req)
	fileMutex.Unlock()

	// 更新任务跟踪器
	uploadTaskTrackers.Lock()
	if tracker, exists := uploadTaskTrackers.tasks[req.TaskID]; exists {
		tracker.LastChunk = int64(req.CurrentChunk)
		tracker.LastUpdated = time.Now()
	} else if resp.Success && !resp.Completed {
		// 创建新的任务跟踪器
		ctx, cancel := context.WithCancel(context.Background())
		uploadTaskTrackers.tasks[req.TaskID] = &UploadTaskTracker{
			LastChunk:   int64(req.CurrentChunk),
			LastUpdated: time.Now(),
			cancelFunc:  cancel,
		}
		// 启动重试机制
		go startUploadRetryMechanism(req.TaskID, req.Destination, int64(req.CurrentChunk+1), ctx)
	}
	uploadTaskTrackers.Unlock()

	// 如果上传完成或失败，清理任务跟踪器
	if resp.Completed || !resp.Success {
		uploadTaskTrackers.Lock()
		if tracker, exists := uploadTaskTrackers.tasks[req.TaskID]; exists {
			if tracker.cancelFunc != nil {
				tracker.cancelFunc()
			}
			delete(uploadTaskTrackers.tasks, req.TaskID)
		}
		uploadTaskTrackers.Unlock()
	}

	cm.sendResp(File, FileUpload, resp)
}

// uploadFile 执行文件上传的核心逻辑
func uploadFile(req *FileUploadRequest) *FileUploadResponse {
	// 参数验证
	if req.Destination == "" {
		return &FileUploadResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "目标路径不能为空",
		}
	}

	if req.CurrentChunk >= req.TotalChunk {
		return &FileUploadResponse{
			TaskID:       req.TaskID,
			Success:      false,
			CurrentChunk: req.CurrentChunk,
			TotalChunk:   req.TotalChunk,
			Error:        "无效的分块序号",
		}
	}

	// 获取绝对路径
	absDestination, err := getAbsolutePath(req.Destination)
	if err != nil {
		return &FileUploadResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   fmt.Sprintf("目标路径解析失败: %v", err),
		}
	}

	// 初始化响应
	resp := &FileUploadResponse{
		TaskID:       req.TaskID,
		Success:      false,
		CurrentChunk: req.CurrentChunk,
		TotalChunk:   req.TotalChunk,
		Completed:    false,
	}

	// 第一个分块时的初始化检查
	if req.CurrentChunk == 0 {
		// 检查目标文件是否存在
		destExists, err := PathExists(absDestination)
		if err != nil {
			log.Printf("检查目标路径出错: %v", err)
			resp.Error = fmt.Sprintf("检查目标路径失败: %v", err)
			handleUploadError(err, resp)
			return resp
		}
		resp.DestinationExists = destExists

		// 处理目标文件已存在的情况
		if destExists && !req.Force {
			log.Printf("目标文件已存在且未启用强制覆盖: %s", absDestination)
			resp.Error = "目标文件已存在"
			return resp
		}

		// 确保目标目录存在
		destDir := filepath.Dir(absDestination)
		if err := os.MkdirAll(destDir, 0755); err != nil {
			log.Printf("创建目标目录失败: %v", err)
			resp.Error = fmt.Sprintf("创建目标目录失败: %v", err)
			handleUploadError(err, resp)
			return resp
		}

		// 清理可能存在的临时文件
		tempFile := absDestination + ".upload"
		_ = os.Remove(tempFile)
	}

	// 写入当前分块到临时文件
	tempFile := absDestination + ".upload"
	// 计算分块大小，使用实际分块内容长度作为基准
	chunkSize := int64(len(req.ChunkContent))
	if req.FileSize > 0 && req.TotalChunk > 0 {
		// 使用标准分块大小计算（向上取整，与服务器端保持一致）
		standardChunkSize := (req.FileSize + int64(req.TotalChunk) - 1) / int64(req.TotalChunk)
		if standardChunkSize > 0 {
			chunkSize = standardChunkSize
		}
	}
	if err := appendChunkToFile(tempFile, req.ChunkContent, req.CurrentChunk); err != nil {
		log.Printf("写入分块失败: %v", err)
		resp.Error = fmt.Sprintf("写入分块失败: %v", err)
		// 清理临时文件
		_ = os.Remove(tempFile)
		return resp
	}

	// 更新已接收大小
	// 计算已接收的字节数：前面的分块数 * 标准分块大小 + 当前分块大小
	if req.CurrentChunk == 0 {
		resp.ReceivedSize = int64(len(req.ChunkContent))
	} else {
		// 获取临时文件的实际大小作为已接收大小
		if fileInfo, err := os.Stat(tempFile); err == nil {
			resp.ReceivedSize = fileInfo.Size()
		} else {
			// 如果无法获取文件大小，使用计算值
			resp.ReceivedSize = int64(req.CurrentChunk)*chunkSize + int64(len(req.ChunkContent))
		}
	}
	if resp.ReceivedSize > req.FileSize {
		resp.ReceivedSize = req.FileSize
	}

	// 检查是否为最后一个分块
	if req.CurrentChunk == req.TotalChunk-1 {
		// 验证文件完整性
		if req.FileHash != "" {
			if !verifyFileHash(tempFile, req.FileHash) {
				log.Printf("文件哈希验证失败: %s", absDestination)
				log.Printf("reqHash: %v", req.FileHash)
				hash, _ := calculateFileHash(tempFile)
				log.Printf("tempFileHash: %v", hash)
				resp.Error = "文件完整性验证失败"
				_ = os.Remove(tempFile)
				return resp
			} else {
				log.Printf("文件哈希验证成功: %s", absDestination)
			}
		}

		// 原子性移动临时文件到目标位置
		if err = os.Rename(tempFile, absDestination); err != nil {
			log.Printf("移动文件失败: %v", err)
			resp.Error = fmt.Sprintf("移动文件失败: %v", err)
			_ = os.Remove(tempFile)
			return resp
		}

		// 上传完成
		resp.Success = true
		resp.Completed = true
		resp.ReceivedSize = req.FileSize
		log.Printf("文件上传完成: %s (大小: %d 字节)", absDestination, req.FileSize)
	} else {
		// 中间分块，返回继续接收状态
		resp.Success = true
	}

	return resp

}
