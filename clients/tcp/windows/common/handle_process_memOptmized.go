//go:build windows
// +build windows

package common

import (
	"fmt"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/shirou/gopsutil/v3/process"
	"log"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// 性能优化相关的全局变量和缓存
var (
	// 系统总内存缓存（启动时获取一次）
	totalMemoryCache uint64
	totalMemoryOnce  sync.Once

	// 用户名缓存（SID->用户名映射）
	usernameCache = make(map[string]string)
	usernameMutex sync.RWMutex
	// 用户名缓存访问时间记录
	usernameAccessTimes = make(map[string]time.Time)

	// 进程基础信息缓存
	processInfoCache  = make(map[int32]*ProcessFullInfo)
	processCacheMutex sync.RWMutex
	cacheExpiry       = 5 * time.Second

	// PPID缓存 - 性能优化：缓存父进程ID避免重复API调用
	ppidCache       = make(map[uint32]int32)
	ppidCacheMutex  sync.RWMutex
	ppidCacheExpiry = 10 * time.Second // PPID缓存10秒
	// PPID缓存访问时间记录
	ppidAccessTimes = make(map[uint32]time.Time)

	// 挂起进程缓存 - 性能优化：只对最近被挂起的进程进行详细状态检查
	suspendedProcesses = make(map[int32]time.Time)
	suspendedMutex     sync.RWMutex
	suspendCacheExpiry = 30 * time.Second // 挂起状态缓存30秒

	// 对象池 - 性能优化：复用ProcessFullInfo对象，减少GC压力
	processInfoPool = sync.Pool{
		New: func() interface{} {
			return &ProcessFullInfo{}
		},
	}

	memoryStats struct {
		allocatedObjects int64 // 已分配对象数
		releasedObjects  int64 // 已释放对象数
		cacheHits        int64 // 缓存命中数
		cacheMisses      int64 // 缓存未命中数
		gcTriggerCount   int64 // GC触发次数
		lastMemoryCheck  time.Time
		memoryCheckMutex sync.RWMutex
	}

	// 内存监控配置
	memoryThresholds = struct {
		warningPercent   float64 // 内存使用警告阈值
		criticalPercent  float64 // 内存使用危险阈值
		gcTriggerPercent float64 // 触发GC的内存阈值
	}{
		warningPercent:   75.0,
		criticalPercent:  85.0,
		gcTriggerPercent: 80.0,
	}

	adaptiveCacheConfig = struct {
		maxCacheSize     int
		minCacheSize     int
		currentCacheSize int
		mutex            sync.RWMutex
	}{
		maxCacheSize:     2000,
		minCacheSize:     100,
		currentCacheSize: 500,
	}
)

func init() {
	log.Printf("🚀 Windows内存优化模块初始化")
	warmupCaches()
	go startMemoryMonitor()
	go startAdaptiveCacheManager()
}

// adjustCacheSize 自适应调整缓存大小
func adjustCacheSize() {
	cacheHits := atomic.LoadInt64(&memoryStats.cacheHits)
	cacheMisses := atomic.LoadInt64(&memoryStats.cacheMisses)

	if cacheHits+cacheMisses == 0 {
		return // 没有缓存活动
	}

	hitRate := float64(cacheHits) / float64(cacheHits+cacheMisses) * 100

	adaptiveCacheConfig.mutex.Lock()
	defer adaptiveCacheConfig.mutex.Unlock()

	oldSize := adaptiveCacheConfig.currentCacheSize

	// 根据命中率调整缓存大小
	if hitRate > 90 {
		// 命中率很高，可以适当增加缓存
		adaptiveCacheConfig.currentCacheSize = int(float64(adaptiveCacheConfig.currentCacheSize) * 1.1)
		if adaptiveCacheConfig.currentCacheSize > adaptiveCacheConfig.maxCacheSize {
			adaptiveCacheConfig.currentCacheSize = adaptiveCacheConfig.maxCacheSize
		}
	} else if hitRate < 60 {
		// 命中率较低，减少缓存大小
		adaptiveCacheConfig.currentCacheSize = int(float64(adaptiveCacheConfig.currentCacheSize) * 0.9)
		if adaptiveCacheConfig.currentCacheSize < adaptiveCacheConfig.minCacheSize {
			adaptiveCacheConfig.currentCacheSize = adaptiveCacheConfig.minCacheSize
		}
	}

	if oldSize != adaptiveCacheConfig.currentCacheSize {
		log.Printf("🔧 自适应缓存调整: %d -> %d (命中率: %.1f%%)",
			oldSize, adaptiveCacheConfig.currentCacheSize, hitRate)
	}
}

// startMemoryMonitor 启动内存监控
func startMemoryMonitor() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次内存
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			go checkMemoryUsage()
		}
	}
}

// checkMemoryUsage 检查内存使用情况
func checkMemoryUsage() {
	memoryStats.memoryCheckMutex.Lock()
	defer memoryStats.memoryCheckMutex.Unlock()

	// 获取系统内存信息
	vmem, err := mem.VirtualMemory()
	if err != nil {
		log.Printf("⚠️  获取内存信息失败: %v", err)
		return
	}

	usedPercent := vmem.UsedPercent
	memoryStats.lastMemoryCheck = time.Now()

	// 记录内存使用统计
	allocated := atomic.LoadInt64(&memoryStats.allocatedObjects)
	released := atomic.LoadInt64(&memoryStats.releasedObjects)
	cacheHits := atomic.LoadInt64(&memoryStats.cacheHits)
	cacheMisses := atomic.LoadInt64(&memoryStats.cacheMisses)

	log.Printf("📊 内存监控: 使用率=%.1f%%, 对象池=[分配:%d,释放:%d], 缓存=[命中:%d,未命中:%d]",
		usedPercent, allocated, released, cacheHits, cacheMisses)

	// 内存使用率检查和优化
	if usedPercent > memoryThresholds.criticalPercent {
		log.Printf("🚨 内存使用率危险 (%.1f%% > %.1f%%)，执行紧急清理", usedPercent, memoryThresholds.criticalPercent)
		go triggerEmergencyCleanup()
	} else if usedPercent > memoryThresholds.warningPercent {
		log.Printf("⚠️  内存使用率警告 (%.1f%% > %.1f%%)，执行预防性清理", usedPercent, memoryThresholds.warningPercent)
		go triggerPreventiveCleanup()
	} else if usedPercent > memoryThresholds.gcTriggerPercent {
		log.Printf("🧹 内存使用率较高 (%.1f%% > %.1f%%)，触发GC", usedPercent, memoryThresholds.gcTriggerPercent)
		go triggerGC()
	}
}

// triggerPreventiveCleanup 触发预防性清理
func triggerPreventiveCleanup() {
	// 清理过期缓存
	go asyncCleanupCache()

	// 适度减少缓存大小
	adaptiveCacheConfig.mutex.Lock()
	if adaptiveCacheConfig.currentCacheSize > adaptiveCacheConfig.minCacheSize {
		adaptiveCacheConfig.currentCacheSize = int(float64(adaptiveCacheConfig.currentCacheSize) * 0.8)
		if adaptiveCacheConfig.currentCacheSize < adaptiveCacheConfig.minCacheSize {
			adaptiveCacheConfig.currentCacheSize = adaptiveCacheConfig.minCacheSize
		}
	}
	adaptiveCacheConfig.mutex.Unlock()

	// 触发GC
	go triggerGC()

	log.Printf("⚠️  预防性清理完成，缓存大小调整为: %d", adaptiveCacheConfig.currentCacheSize)
}

// triggerGC 触发垃圾回收
func triggerGC() {
	atomic.AddInt64(&memoryStats.gcTriggerCount, 1)
	runtime.GC()
	log.Printf("🧹 GC执行完成，累计触发次数: %d", atomic.LoadInt64(&memoryStats.gcTriggerCount))
}

// warmupCaches 缓存预热 - 在后台预先加载常用信息
func warmupCaches() {
	// 延迟启动，避免影响程序初始化
	time.Sleep(2 * time.Second)

	log.Printf("🔥 开始缓存预热...")
	start := time.Now()

	// 预热系统进程的用户名缓存
	systemPids := []int32{0, 4, 8} // 常见的系统进程PID
	for _, pid := range systemPids {
		if p, err := process.NewProcess(pid); err == nil {
			if username, err := p.Username(); err == nil {
				sidKey := fmt.Sprintf("pid_%d", pid)
				setCachedUsername(sidKey, username)
			}
		}
	}

	// 创建临时ConnectionManager实例用于预热
	tempCM := &ConnectionManager{}

	// 预热PPID缓存 - 获取一些常见进程的父进程信息
	if pids, err := process.Pids(); err == nil && len(pids) > 0 {
		// 只预热前20个进程的PPID，避免启动时间过长
		for i, pid := range pids {
			if i >= 20 {
				break
			}
			tempCM.getWindowsPPIDOptimized(uint32(pid)) // 这会自动缓存结果
		}
	}

	// 🚀 性能优化：预热进程信息缓存 - 缓存常见系统进程的详细信息
	log.Printf("🔥 开始预热进程信息缓存...")
	processWarmupStart := time.Now()

	// 预热系统关键进程的详细信息
	systemProcessPids := []int32{0, 4, 8} // System, Registry, smss等
	for _, pid := range systemProcessPids {
		if p, err := process.NewProcess(pid); err == nil {
			if info, err := tempCM.getProcessFullInfoOptimized(p, true); err == nil {
				// 手动添加到缓存
				processCacheMutex.Lock()
				cachedInfo := *info
				cachedInfo.StartTime = time.Now() // 使用当前时间作为缓存时间戳
				processInfoCache[pid] = &cachedInfo
				processCacheMutex.Unlock()
				// 释放临时对象
				releaseProcessInfo(info)
			}
		}
		// 避免过于频繁的API调用
		time.Sleep(10 * time.Millisecond)
	}
	processWarmupTime := time.Since(processWarmupStart)
	log.Printf("✅ 进程信息缓存预热完成，耗时: %v", processWarmupTime)

	elapsed := time.Since(start)
	log.Printf("✅ 缓存预热完成，总耗时: %v", elapsed)
}

// startAdaptiveCacheManager 启动缓存清理任务
func startAdaptiveCacheManager() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			go adjustCacheSize()
		}
	}
}

// 修改原有的清理调用
func triggerEmergencyCleanup() {
	atomic.AddInt64(&memoryStats.gcTriggerCount, 1)

	// 异步清理所有缓存，避免阻塞
	asyncCleanupCache()

	// 调整缓存大小到最小值
	adaptiveCacheConfig.mutex.Lock()
	adaptiveCacheConfig.currentCacheSize = adaptiveCacheConfig.minCacheSize
	adaptiveCacheConfig.mutex.Unlock()

	// 异步执行GC
	go func() {
		runtime.GC()
		runtime.GC()
		log.Printf("🚨 异步紧急清理完成，缓存大小调整为: %d", adaptiveCacheConfig.minCacheSize)
	}()
}

func asyncCleanupCache() {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("缓存清理过程中发生panic: %v", r)
			}
		}()

		log.Printf("🧹 开始异步清理进程信息缓存")
		cleanupStart := time.Now()

		// 执行清理操作
		cleanupCache()

		cleanupDuration := time.Since(cleanupStart)
		log.Printf("✅ 异步进程信息缓存清理完成，耗时: %v", cleanupDuration)
	}()
}

// cleanupCache 智能缓存清理 - 基于LRU策略
func cleanupCache() {
	// 清理用户名缓存 - 使用LRU策略
	usernameMutex.Lock()
	if len(usernameCache) > 1000 {
		log.Printf("🧹 开始LRU清理用户名缓存，当前大小: %d", len(usernameCache))

		// 收集访问时间信息
		type accessInfo struct {
			key        string
			lastAccess time.Time
		}

		accessList := make([]accessInfo, 0, len(usernameCache))
		now := time.Now()

		for key := range usernameCache {
			lastAccess, exists := usernameAccessTimes[key]
			if !exists {
				lastAccess = now.Add(-24 * time.Hour) // 没有访问记录的设为24小时前
			}
			accessList = append(accessList, accessInfo{key: key, lastAccess: lastAccess})
		}

		// 按访问时间排序（最旧的在前）
		for i := 0; i < len(accessList)-1; i++ {
			for j := i + 1; j < len(accessList); j++ {
				if accessList[i].lastAccess.After(accessList[j].lastAccess) {
					accessList[i], accessList[j] = accessList[j], accessList[i]
				}
			}
		}

		// 删除最旧的30%缓存项
		deleteCount := len(accessList) * 30 / 100
		if deleteCount < 100 {
			deleteCount = 100 // 至少删除100个
		}

		for i := 0; i < deleteCount && i < len(accessList); i++ {
			key := accessList[i].key
			delete(usernameCache, key)
			delete(usernameAccessTimes, key)
		}

		log.Printf("✅ 用户名缓存LRU清理完成，删除: %d, 剩余: %d", deleteCount, len(usernameCache))
	}
	usernameMutex.Unlock()

	// 清理PPID缓存 - 使用LRU策略
	ppidCacheMutex.Lock()
	if len(ppidCache) > 2000 {
		log.Printf("🧹 开始LRU清理PPID缓存，当前大小: %d", len(ppidCache))

		// 收集PPID访问时间信息
		type ppidAccessInfo struct {
			key        uint32
			lastAccess time.Time
		}

		ppidAccessList := make([]ppidAccessInfo, 0, len(ppidCache))
		now := time.Now()

		for key := range ppidCache {
			lastAccess, exists := ppidAccessTimes[key]
			if !exists {
				lastAccess = now.Add(-24 * time.Hour) // 没有访问记录的设为24小时前
			}
			ppidAccessList = append(ppidAccessList, ppidAccessInfo{key: key, lastAccess: lastAccess})
		}

		// 按访问时间排序（最旧的在前）
		for i := 0; i < len(ppidAccessList)-1; i++ {
			for j := i + 1; j < len(ppidAccessList); j++ {
				if ppidAccessList[i].lastAccess.After(ppidAccessList[j].lastAccess) {
					ppidAccessList[i], ppidAccessList[j] = ppidAccessList[j], ppidAccessList[i]
				}
			}
		}

		// 删除最旧的30%缓存项
		deleteCount := len(ppidAccessList) * 30 / 100
		if deleteCount < 200 {
			deleteCount = 200 // 至少删除200个
		}

		for i := 0; i < deleteCount && i < len(ppidAccessList); i++ {
			key := ppidAccessList[i].key
			delete(ppidCache, key)
			delete(ppidAccessTimes, key)
		}

		log.Printf("✅ PPID缓存LRU清理完成，删除: %d, 剩余: %d", deleteCount, len(ppidCache))
	}
	ppidCacheMutex.Unlock()

	// 🚀 性能优化：清理进程信息缓存 - 删除过期条目
	processCacheMutex.Lock()
	if len(processInfoCache) > 0 {
		log.Printf("🧹 开始清理进程信息缓存，当前大小: %d", len(processInfoCache))
		now := time.Now()
		expiredCount := 0

		for pid, info := range processInfoCache {
			// 检查缓存是否过期
			if now.Sub(info.StartTime) > cacheExpiry {
				delete(processInfoCache, pid)
				expiredCount++
			}
		}

		if expiredCount > 0 {
			log.Printf("✅ 进程信息缓存清理完成，删除了 %d 个过期条目，剩余: %d", expiredCount, len(processInfoCache))
		}
	}
	processCacheMutex.Unlock()
}

// initTotalMemoryCache 初始化系统总内存缓存
func initTotalMemoryCache() {
	totalMemoryOnce.Do(func() {
		if memInfo, err := mem.VirtualMemory(); err == nil {
			totalMemoryCache = memInfo.Total
			log.Printf("系统总内存缓存初始化成功: %d MB", totalMemoryCache/1024/1024)
		} else {
			log.Printf("初始化系统总内存缓存失败: %v", err)
			// 🚀 不设置虚构的默认值，保持0表示无法获取内存信息
			totalMemoryCache = 0
		}
	})
}

// getTotalMemory 获取系统总内存（使用缓存）
func getTotalMemory() uint64 {
	if totalMemoryCache == 0 {
		initTotalMemoryCache()
	}
	return totalMemoryCache
}

// getCachedUsername 获取缓存的用户名 - 带LRU访问时间记录
func getCachedUsername(sid string) (string, bool) {
	usernameMutex.Lock() // 使用写锁，因为需要更新访问时间
	defer usernameMutex.Unlock()
	username, exists := usernameCache[sid]
	if exists {
		// 更新访问时间
		usernameAccessTimes[sid] = time.Now()
	}
	return username, exists
}

// setCachedUsername 设置缓存的用户名 - 带LRU访问时间记录
func setCachedUsername(sid, username string) {
	usernameMutex.Lock()
	defer usernameMutex.Unlock()
	usernameCache[sid] = username
	// 记录访问时间
	usernameAccessTimes[sid] = time.Now()
}
