package c2

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type ProxyRoute struct{}

func (p *ProxyRoute) InitProxyRoute(Router *gin.RouterGroup) {
	proxyRouter := Router.Group("proxy").Use(middleware.OperationRecord())
	{
		proxyRouter.POST("/check-server-port", proxyApi.CheckServerPortAvailability)
		proxyRouter.POST("/check-client-port", proxyApi.CheckClientPortAvailability)
		proxyRouter.GET("/:id", proxyApi.GetProxyInstance)
		proxyRouter.POST("/list", proxyApi.ListProxy)
		proxyRouter.POST("/create", proxyApi.CreateProxy)
		proxyRouter.POST("/control", proxyApi.ControlProxy)
		proxyRouter.DELETE("/delete/:id", proxyApi.DeleteProxy)
		proxyRouter.PUT("/update", proxyApi.UpdateProxy)
	}
}
