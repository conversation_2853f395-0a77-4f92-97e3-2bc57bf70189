package c2

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type HeartbeatConfigRoute struct{}

// InitHeartbeatConfigRoute 初始化心跳配置路由
func (h *HeartbeatConfigRoute) InitHeartbeatConfigRoute(Router *gin.RouterGroup) (IR gin.IRoutes) {
	heartbeatConfigRouter := Router.Group("/heartbeat").Use(middleware.OperationRecord())
	{
		// 心跳配置管理
		heartbeatConfigRouter.GET("/config", heartbeatConfigApi.GetHeartbeatConfig)           // 获取心跳配置
		heartbeatConfigRouter.PUT("/config", heartbeatConfigApi.UpdateHeartbeatConfig)        // 更新心跳配置
		heartbeatConfigRouter.POST("/config", heartbeatConfigApi.CreateHeartbeatConfig)       // 创建心跳配置
		heartbeatConfigRouter.DELETE("/config/:id", heartbeatConfigApi.DeleteHeartbeatConfig) // 删除心跳配置
		heartbeatConfigRouter.GET("/configs", heartbeatConfigApi.GetHeartbeatConfigList)      // 获取心跳配置列表
	}
	return heartbeatConfigRouter
}
