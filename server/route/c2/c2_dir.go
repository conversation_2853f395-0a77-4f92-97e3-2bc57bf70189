package c2

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type DirRoute struct{}

// InitDirRoute 初始化目录管理路由
func (d *DirRoute) InitDirRoute(Router *gin.RouterGroup) {
	dirRouter := Router.Group("dir").Use(middleware.OperationRecord())
	{
		// 目录操作
		dirRouter.POST("/:clientId/create", dirApi.CreateDir)
		dirRouter.POST("/:clientId/list", dirApi.ListDir)
		dirRouter.POST("/:clientId/move", dirApi.MoveDir)
		dirRouter.POST("/:clientId/delete", dirApi.DeleteDir)
		dirRouter.POST("/:clientId/copy", dirApi.CopyDir)
		// 磁盘操作
		dirRouter.POST("/:clientId/disks", dirApi.ListDisks)
	}
}
