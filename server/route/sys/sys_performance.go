package sys

import (
	"server/api/sys"
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type PerformanceRouter struct{}

// InitPerformanceRouter 初始化性能监控路由
func (p *PerformanceRouter) InitPerformanceRouter(Router *gin.RouterGroup) {
	performanceRouter := Router.Group("performance").Use(middleware.OperationRecord())
	performanceApi := sys.PerformanceApi{}
	{
		// 监听器性能监控
		performanceRouter.GET("listener-stats", performanceApi.GetListenerPerformanceStats)      // 获取监听器性能统计
		performanceRouter.GET("listener-connections", performanceApi.GetListenerConnectionStats) // 获取监听器连接统计
		performanceRouter.GET("listener-traffic", performanceApi.GetListenerTrafficStats)        // 获取监听器流量统计

		// 客户端性能监控
		performanceRouter.GET("client-connections", performanceApi.GetClientConnectionStats) // 获取客户端连接统计
		performanceRouter.GET("client-tasks", performanceApi.GetClientTaskStats)             // 获取客户端任务统计

		// 任务性能监控
		performanceRouter.GET("task-execution", performanceApi.GetTaskExecutionStats) // 获取任务执行统计
		performanceRouter.GET("task-queue", performanceApi.GetTaskQueueStats)         // 获取任务队列统计

		// 内存池性能监控
		performanceRouter.GET("memory-pools", performanceApi.GetAllMemoryPoolStats) // 获取所有内存池统计

		// Goroutine和工作池性能监控
		performanceRouter.GET("goroutine-stats", performanceApi.GetGoroutineStats)           // 获取Goroutine统计信息
		performanceRouter.GET("worker-pool-stats", performanceApi.GetWorkerPoolStats)        // 获取工作池统计信息
		performanceRouter.GET("leak-detector-stats", performanceApi.GetLeakDetectorStats)    // 获取泄漏检测器统计信息
		performanceRouter.GET("system-stats", performanceApi.GetSystemStats)                 // 获取系统统计信息
		performanceRouter.GET("performance-overview", performanceApi.GetPerformanceOverview) // 获取性能概览

		// 数据库连接池性能监控
		performanceRouter.GET("database-stats", performanceApi.GetDatabaseStats)                 // 获取数据库统计信息
		performanceRouter.GET("database-health", performanceApi.GetDatabaseHealth)               // 获取数据库健康状态
		performanceRouter.GET("database-connections", performanceApi.GetDatabaseConnectionStats) // 获取数据库连接统计
		performanceRouter.GET("database-queries", performanceApi.GetDatabaseQueryStats)          // 获取数据库查询统计

		// 缓存性能监控
		performanceRouter.GET("cache-stats", performanceApi.GetCacheStats)   // 获取缓存统计信息
		performanceRouter.GET("cache-health", performanceApi.GetCacheHealth) // 获取缓存健康状态
	}
}
