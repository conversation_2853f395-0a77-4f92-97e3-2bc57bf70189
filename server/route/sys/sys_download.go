package sys

import (
	"server/api/sys"
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type DownloadRouter struct{}

func (d *DownloadRouter) InitDownloadRouter(Router *gin.RouterGroup) {
	downloadRouter := Router.Group("download").Use(middleware.JWTAuth(), middleware.OperationRecord())
	downloadApi := sys.DownloadApi{}
	{
		// 从服务器下载文件，支持断点续传
		downloadRouter.GET("/server-file", downloadApi.DownloadServerFile)
		// 支持HEAD请求用于文件信息检查
		downloadRouter.HEAD("/server-file", downloadApi.DownloadServerFile)
		// 下载截图文件
		downloadRouter.GET("/screenshot", downloadApi.DownloadScreenshotFile)
	}
}
