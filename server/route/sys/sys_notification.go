package sys

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type NotificationRoute struct{}

// InitNotificationRoute 初始化通知路由
func (r *NotificationRoute) InitNotificationRoute(Router *gin.RouterGroup) (IR gin.IRoutes) {
	notificationRouter := Router.Group("/notification").Use(middleware.OperationRecord())
	{
		// 通知列表和统计
		notificationRouter.GET("/list", notificationApi.GetNotificationList)   // 获取通知列表
		notificationRouter.GET("/stats", notificationApi.GetNotificationStats) // 获取通知统计

		// 通知操作
		notificationRouter.POST("/read", notificationApi.MarkAsRead)              // 标记已读
		notificationRouter.POST("/read-all", notificationApi.MarkAllAsRead)       // 标记全部已读
		notificationRouter.DELETE("/delete", notificationApi.DeleteNotifications) // 删除通知
		notificationRouter.POST("/cleanup", notificationApi.CleanupNotifications) // 清理旧通知

		// SSE连接
		notificationRouter.GET("/sse", notificationApi.SSEConnect)        // SSE连接端点
		notificationRouter.GET("/sse/stats", notificationApi.GetSSEStats) // SSE统计信息
	}

	return notificationRouter
}
