package sys

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type UserManageRoute struct{}

func (r *UserManageRoute) InitUserManageRoute(Router *gin.RouterGroup) (IR gin.IRoutes) {
	userManageRouter := Router.Group("/user-manage").Use(middleware.AdminAuth(), middleware.OperationRecord())
	{
		userManageRouter.POST("/list", userManageApi.GetUserList)       // 获取用户列表
		userManageRouter.POST("/create", userManageApi.CreateUser)      // 创建用户
		userManageRouter.PUT("/update", userManageApi.UpdateUser)       // 更新用户
		userManageRouter.DELETE("/:id", userManageApi.DeleteUser)       // 删除用户
		userManageRouter.PUT("/status", userManageApi.ChangeUserStatus) // 修改用户状态
	}

	return userManageRouter
}
