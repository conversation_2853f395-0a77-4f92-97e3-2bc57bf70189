package static

import (
	"embed"
	"io"
	"io/fs"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"server/global"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 使用 //go:embed 指令嵌入前端构建后的 dist 目录
// 注意：这里只是声明，在开发环境中可能不存在这个目录，但不会影响编译
//
//go:embed dist
var distFS embed.FS

// 提供静态文件服务
// 优先使用本地文件系统中的文件，如果不存在则使用嵌入的文件
func ServeStatic(router *gin.Engine, localDistPath string) {
	// 检查本地 dist 目录是否存在
	_, err := os.Stat(localDistPath)
	hasLocalDist := err == nil

	// 注册 NoRoute 处理函数，处理静态文件请求，但避开API路径
	router.NoRoute(func(c *gin.Context) {
		// 如果是API请求，不处理
		if isAPIRequest(c.Request.URL.Path) {
			return
		}

		// 只处理GET请求
		if c.Request.Method != http.MethodGet {
			return
		}

		// 构建文件路径
		filePath := c.Request.URL.Path
		if filePath == "/" {
			filePath = "/index.html"
		}

		// 优先使用本地文件
		if hasLocalDist {
			localFilePath := filepath.Join(localDistPath, filePath)
			// 检查文件是否存在
			if _, err := os.Stat(localFilePath); err == nil {
				zap.L().Debug("提供本地静态文件", zap.String("path", localFilePath))
				c.File(localFilePath)
				return
			}
		}

		// 如果本地文件不存在或没有本地目录，使用嵌入的文件
		distSubFS, err := fs.Sub(distFS, "dist")
		if err != nil {
			zap.L().Error("无法获取嵌入的dist子文件系统", zap.Error(err))
			c.Status(http.StatusNotFound)
			return
		}

		// 尝试从嵌入的文件系统中打开文件
		file, err := distSubFS.Open(filePath[1:]) // 去掉前导斜杠
		if err != nil {
			// 如果文件不存在，对于SPA应用，返回index.html
			if os.IsNotExist(err) {
				indexFile, err := distSubFS.Open("index.html")
				if err != nil {
					zap.L().Error("无法打开嵌入的index.html文件", zap.Error(err))
					c.Status(http.StatusNotFound)
					return
				}
				defer indexFile.Close()

				// 设置正确的内容类型
				c.Header("Content-Type", "text/html")

				// 将文件内容复制到响应
				http.ServeContent(c.Writer, c.Request, "index.html", time.Time{}, indexFile.(io.ReadSeeker))
				return
			}

			zap.L().Error("无法打开嵌入的文件", zap.String("path", filePath), zap.Error(err))
			c.Status(http.StatusNotFound)
			return
		}
		defer file.Close()

		// 设置适当的内容类型
		contentType := mime.TypeByExtension(filepath.Ext(filePath))
		if contentType != "" {
			c.Header("Content-Type", contentType)
		}

		// 将文件内容复制到响应
		http.ServeContent(c.Writer, c.Request, filepath.Base(filePath), time.Time{}, file.(io.ReadSeeker))
	})

	// 记录日志
	if hasLocalDist {
		zap.L().Info("配置了本地静态文件目录", zap.String("path", localDistPath))
	} else {
		zap.L().Info("使用嵌入的静态文件")
	}
}

// 判断是否是API请求
func isAPIRequest(path string) bool {
	// 获取API前缀
	apiPrefix := global.CONFIG.Server.RouterPrefix
	// 检查路径是否以API前缀开头
	return len(path) >= len(apiPrefix) && path[0:len(apiPrefix)] == apiPrefix
}
