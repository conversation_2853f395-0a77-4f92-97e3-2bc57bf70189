package workerpool

import (
	"fmt"
	"runtime"
	"sync"
	"time"

	"go.uber.org/zap"
	"server/global"
)

// PoolManager 工作池管理器
type PoolManager struct {
	pools map[string]*WorkerPool
	mu    sync.RWMutex
}

// GlobalPoolManager 全局工作池管理器
var GlobalPoolManager *PoolManager

// PoolType 工作池类型常量
const (
	PoolTypeDatabase    = "database"    // 数据库操作池
	PoolTypeNetwork     = "network"     // 网络操作池
	PoolTypeFileIO      = "fileio"      // 文件IO操作池
	PoolTypeProcessing  = "processing"  // 数据处理池
	PoolTypeHeartbeat   = "heartbeat"   // 心跳处理池
	PoolTypeGeneral     = "general"     // 通用任务池
)

// init 初始化全局工作池管理器
func init() {
	GlobalPoolManager = NewPoolManager()
}

// NewPoolManager 创建新的工作池管理器
func NewPoolManager() *PoolManager {
	return &PoolManager{
		pools: make(map[string]*WorkerPool),
	}
}

// InitDefaultPools 初始化默认工作池
func (pm *PoolManager) InitDefaultPools() {
	startTime := time.Now()
	cpuCount := runtime.NumCPU()

	// 🚀 优化：根据系统资源动态调整工作池配置
	configs := calculateOptimalPoolConfigs(cpuCount)

	// 数据库操作池 - 较少的worker，因为数据库连接有限
	pm.CreatePool(PoolTypeDatabase, configs.Database)

	// 网络操作池 - 中等数量的worker
	pm.CreatePool(PoolTypeNetwork, configs.Network)

	// 文件IO操作池 - 中等数量的worker
	pm.CreatePool(PoolTypeFileIO, configs.FileIO)

	// 数据处理池 - 较多的worker，CPU密集型
	pm.CreatePool(PoolTypeProcessing, configs.Processing)

	// 心跳处理池 - 专门处理心跳相关任务
	pm.CreatePool(PoolTypeHeartbeat, configs.Heartbeat)

	// 通用任务池 - 处理其他杂项任务
	pm.CreatePool(PoolTypeGeneral, configs.General)

	global.LOG.Info("✅ 默认工作池初始化完成",
		zap.Int("CPU核心数", cpuCount),
		zap.Int("工作池数量", len(pm.pools)),
		zap.Duration("耗时", time.Since(startTime)))
}

// PoolConfigs 工作池配置集合
type PoolConfigs struct {
	Database   WorkerPoolConfig
	Network    WorkerPoolConfig
	FileIO     WorkerPoolConfig
	Processing WorkerPoolConfig
	Heartbeat  WorkerPoolConfig
	General    WorkerPoolConfig
}

// calculateOptimalPoolConfigs 根据系统资源计算最优工作池配置
func calculateOptimalPoolConfigs(cpuCount int) PoolConfigs {
	// 🚀 优化：根据CPU核心数动态调整配置
	return PoolConfigs{
		Database: WorkerPoolConfig{
			Name:              "数据库操作池",
			MinWorkers:        max(2, cpuCount/4),
			MaxWorkers:        max(10, cpuCount*2),
			QueueSize:         500,
			WorkerIdleTimeout: 60 * time.Second,
			ScaleUpThreshold:  0.7,
			ScaleDownThreshold: 0.3,
			CheckInterval:     15 * time.Second,
		},
		Network: WorkerPoolConfig{
			Name:              "网络操作池",
			MinWorkers:        cpuCount,
			MaxWorkers:        cpuCount * 3,
			QueueSize:         1000,
			WorkerIdleTimeout: 30 * time.Second,
			ScaleUpThreshold:  0.8,
			ScaleDownThreshold: 0.2,
			CheckInterval:     10 * time.Second,
		},
		FileIO: WorkerPoolConfig{
			Name:              "文件IO操作池",
			MinWorkers:        max(2, cpuCount/2),
			MaxWorkers:        cpuCount * 2,
			QueueSize:         300,
			WorkerIdleTimeout: 45 * time.Second,
			ScaleUpThreshold:  0.7,
			ScaleDownThreshold: 0.3,
			CheckInterval:     20 * time.Second,
		},
		Processing: WorkerPoolConfig{
			Name:              "数据处理池",
			MinWorkers:        cpuCount,
			MaxWorkers:        cpuCount * 4,
			QueueSize:         2000,
			WorkerIdleTimeout: 30 * time.Second,
			ScaleUpThreshold:  0.8,
			ScaleDownThreshold: 0.2,
			CheckInterval:     10 * time.Second,
		},
		Heartbeat: WorkerPoolConfig{
			Name:              "心跳处理池",
			MinWorkers:        2,
			MaxWorkers:        max(8, cpuCount),
			QueueSize:         1000,
			WorkerIdleTimeout: 60 * time.Second,
			ScaleUpThreshold:  0.7,
			ScaleDownThreshold: 0.3,
			CheckInterval:     15 * time.Second,
		},
		General: WorkerPoolConfig{
			Name:              "通用任务池",
			MinWorkers:        max(2, cpuCount/2),
			MaxWorkers:        cpuCount * 2,
			QueueSize:         500,
			WorkerIdleTimeout: 45 * time.Second,
			ScaleUpThreshold:  0.8,
			ScaleDownThreshold: 0.2,
			CheckInterval:     15 * time.Second,
		},
	}
}

// max 返回两个整数中的较大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// CreatePool 创建工作池
func (pm *PoolManager) CreatePool(poolType string, config WorkerPoolConfig) *WorkerPool {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	if _, exists := pm.pools[poolType]; exists {
		global.LOG.Warn("工作池已存在，跳过创建", zap.String("poolType", poolType))
		return pm.pools[poolType]
	}
	
	pool := NewWorkerPool(config)
	pm.pools[poolType] = pool
	
	global.LOG.Info("工作池创建成功",
		zap.String("poolType", poolType),
		zap.String("name", config.Name))
	
	return pool
}

// GetPool 获取工作池
func (pm *PoolManager) GetPool(poolType string) *WorkerPool {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	return pm.pools[poolType]
}

// SubmitTask 提交任务到指定工作池
func (pm *PoolManager) SubmitTask(poolType string, task Task) error {
	pool := pm.GetPool(poolType)
	if pool == nil {
		global.LOG.Error("工作池不存在", zap.String("poolType", poolType))
		return fmt.Errorf("工作池不存在: %s", poolType)
	}
	
	return pool.Submit(task)
}

// GetAllStats 获取所有工作池的统计信息
func (pm *PoolManager) GetAllStats() map[string]*PoolStats {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	stats := make(map[string]*PoolStats)
	for poolType, pool := range pm.pools {
		stats[poolType] = pool.GetStats()
	}

	return stats
}

// GetAllStatsCompatible 获取所有工作池的兼容格式统计信息
func (pm *PoolManager) GetAllStatsCompatible() map[string]interface{} {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	stats := make(map[string]interface{})
	for poolType, pool := range pm.pools {
		poolStats := pool.GetStats()
		stats[poolType] = poolStats.ToCompatibleStats()
	}

	return stats
}

// GetGoroutineStats 获取Goroutine统计信息
func (pm *PoolManager) GetGoroutineStats() *GoroutineStats {
	numGoroutine := runtime.NumGoroutine()
	numCPU := runtime.NumCPU()
	
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// 计算所有工作池的统计
	allStats := pm.GetAllStats()
	var totalWorkers, totalTasks, totalQueued int64
	
	for _, stats := range allStats {
		totalWorkers += int64(stats.TotalWorkers)
		totalTasks += stats.TotalTasks
		totalQueued += stats.QueuedTasks
	}
	
	return &GoroutineStats{
		NumGoroutine:    numGoroutine,
		NumCPU:          numCPU,
		TotalWorkers:    totalWorkers,
		TotalTasks:      totalTasks,
		TotalQueued:     totalQueued,
		MemoryUsage:     m.Alloc,
		MemoryTotal:     m.TotalAlloc,
		GCCount:         m.NumGC,
		LastGCTime:      time.Unix(0, int64(m.LastGC)),
		HeapInUse:       m.HeapInuse,
		StackInUse:      m.StackInuse,
	}
}

// Shutdown 关闭所有工作池
func (pm *PoolManager) Shutdown() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	global.LOG.Info("开始关闭所有工作池")
	
	for poolType, pool := range pm.pools {
		global.LOG.Info("关闭工作池", zap.String("poolType", poolType))
		pool.Shutdown()
	}
	
	pm.pools = make(map[string]*WorkerPool)
	global.LOG.Info("所有工作池已关闭")
}

// GoroutineStats Goroutine统计信息
type GoroutineStats struct {
	NumGoroutine int       `json:"num_goroutine"`
	NumCPU       int       `json:"num_cpu"`
	TotalWorkers int64     `json:"total_workers"`
	TotalTasks   int64     `json:"total_tasks"`
	TotalQueued  int64     `json:"total_queued"`
	MemoryUsage  uint64    `json:"memory_usage"`
	MemoryTotal  uint64    `json:"memory_total"`
	GCCount      uint32    `json:"gc_count"`
	LastGCTime   time.Time `json:"last_gc_time"`
	HeapInUse    uint64    `json:"heap_in_use"`
	StackInUse   uint64    `json:"stack_in_use"`
}

// 便捷函数
func SubmitDatabaseTask(task Task) error {
	return GlobalPoolManager.SubmitTask(PoolTypeDatabase, task)
}

func SubmitNetworkTask(task Task) error {
	return GlobalPoolManager.SubmitTask(PoolTypeNetwork, task)
}

func SubmitFileIOTask(task Task) error {
	return GlobalPoolManager.SubmitTask(PoolTypeFileIO, task)
}

func SubmitProcessingTask(task Task) error {
	return GlobalPoolManager.SubmitTask(PoolTypeProcessing, task)
}

func SubmitHeartbeatTask(task Task) error {
	return GlobalPoolManager.SubmitTask(PoolTypeHeartbeat, task)
}

func SubmitGeneralTask(task Task) error {
	return GlobalPoolManager.SubmitTask(PoolTypeGeneral, task)
}

func GetGoroutineStats() *GoroutineStats {
	return GlobalPoolManager.GetGoroutineStats()
}

func GetAllPoolStats() map[string]*PoolStats {
	return GlobalPoolManager.GetAllStats()
}

func GetAllPoolStatsCompatible() map[string]interface{} {
	return GlobalPoolManager.GetAllStatsCompatible()
}
