package workerpool

import (
	"fmt"
	"time"

	"server/global"

	"go.uber.org/zap"
)

// DatabaseTask 数据库任务
type DatabaseTask struct {
	ID       string
	Type     string
	Function func() error
	Context  map[string]interface{}
}

func (t *DatabaseTask) Execute() error {
	return t.Function()
}

func (t *DatabaseTask) GetID() string {
	return t.ID
}

func (t *DatabaseTask) GetType() string {
	return t.Type
}

// NetworkTask 网络任务
type NetworkTask struct {
	ID       string
	Type     string
	Function func() error
	Context  map[string]interface{}
}

func (t *NetworkTask) Execute() error {
	return t.Function()
}

func (t *NetworkTask) GetID() string {
	return t.ID
}

func (t *NetworkTask) GetType() string {
	return t.Type
}

// ProcessDetectionTask 进程检测任务
type ProcessDetectionTask struct {
	ID         string
	LocalAddr  string
	RemoteAddr string
	IsLocal    bool
	Logger     func(pid, processName string)
}

func (t *ProcessDetectionTask) Execute() error {
	if !t.IsLocal {
		t.Logger("", "")
		return nil
	}

	// 调用进程检测函数
	pid, processName := getProcessByConnection(t.LocalAddr, t.RemoteAddr)
	t.Logger(pid, processName)

	return nil
}

func (t *ProcessDetectionTask) GetID() string {
	return t.ID
}

func (t *ProcessDetectionTask) GetType() string {
	return "process_detection"
}

// HeartbeatTask 心跳任务
type HeartbeatTask struct {
	ID         string
	Type       string
	RemoteAddr string
	Function   func() error
	Context    map[string]interface{}
}

func (t *HeartbeatTask) Execute() error {
	return t.Function()
}

func (t *HeartbeatTask) GetID() string {
	return t.ID
}

func (t *HeartbeatTask) GetType() string {
	return t.Type
}

// FileIOTask 文件IO任务
type FileIOTask struct {
	ID       string
	Type     string
	Function func() error
	Context  map[string]interface{}
}

func (t *FileIOTask) Execute() error {
	return t.Function()
}

func (t *FileIOTask) GetID() string {
	return t.ID
}

func (t *FileIOTask) GetType() string {
	return t.Type
}

// ProcessingTask 数据处理任务
type ProcessingTask struct {
	ID       string
	Type     string
	Function func() error
	Context  map[string]interface{}
}

func (t *ProcessingTask) Execute() error {
	return t.Function()
}

func (t *ProcessingTask) GetID() string {
	return t.ID
}

func (t *ProcessingTask) GetType() string {
	return t.Type
}

// GeneralTask 通用任务
type GeneralTask struct {
	ID       string
	Type     string
	Function func() error
	Context  map[string]interface{}
}

func (t *GeneralTask) Execute() error {
	return t.Function()
}

func (t *GeneralTask) GetID() string {
	return t.ID
}

func (t *GeneralTask) GetType() string {
	return t.Type
}

// 任务创建辅助函数

// NewDatabaseTask 创建数据库任务
func NewDatabaseTask(taskType string, fn func() error) *DatabaseTask {
	return &DatabaseTask{
		ID:       generateTaskID(),
		Type:     taskType,
		Function: fn,
		Context:  make(map[string]interface{}),
	}
}

// NewNetworkTask 创建网络任务
func NewNetworkTask(taskType string, fn func() error) *NetworkTask {
	return &NetworkTask{
		ID:       generateTaskID(),
		Type:     taskType,
		Function: fn,
		Context:  make(map[string]interface{}),
	}
}

// NewProcessDetectionTask 创建进程检测任务
func NewProcessDetectionTask(localAddr, remoteAddr string, isLocal bool, logger func(string, string)) *ProcessDetectionTask {
	return &ProcessDetectionTask{
		ID:         generateTaskID(),
		LocalAddr:  localAddr,
		RemoteAddr: remoteAddr,
		IsLocal:    isLocal,
		Logger:     logger,
	}
}

// NewHeartbeatTask 创建心跳任务
func NewHeartbeatTask(taskType, remoteAddr string, fn func() error) *HeartbeatTask {
	return &HeartbeatTask{
		ID:         generateTaskID(),
		Type:       taskType,
		RemoteAddr: remoteAddr,
		Function:   fn,
		Context:    make(map[string]interface{}),
	}
}

// NewFileIOTask 创建文件IO任务
func NewFileIOTask(taskType string, fn func() error) *FileIOTask {
	return &FileIOTask{
		ID:       generateTaskID(),
		Type:     taskType,
		Function: fn,
		Context:  make(map[string]interface{}),
	}
}

// NewProcessingTask 创建数据处理任务
func NewProcessingTask(taskType string, fn func() error) *ProcessingTask {
	return &ProcessingTask{
		ID:       generateTaskID(),
		Type:     taskType,
		Function: fn,
		Context:  make(map[string]interface{}),
	}
}

// NewGeneralTask 创建通用任务
func NewGeneralTask(taskType string, fn func() error) *GeneralTask {
	return &GeneralTask{
		ID:       generateTaskID(),
		Type:     taskType,
		Function: fn,
		Context:  make(map[string]interface{}),
	}
}

// generateTaskID 生成任务ID
func generateTaskID() string {
	return fmt.Sprintf("task_%d", time.Now().UnixNano())
}

// 便捷的任务提交函数

// SubmitDatabaseUpdate 提交数据库更新任务
func SubmitDatabaseUpdate(description string, updateFunc func() error) error {
	task := NewDatabaseTask("database_update", updateFunc)

	global.LOG.Debug("提交数据库更新任务",
		zap.String("taskID", task.GetID()),
		zap.String("description", description))

	return SubmitDatabaseTask(task)
}

// SubmitProcessDetection 提交进程检测任务
func SubmitProcessDetection(localAddr, remoteAddr string, isLocal bool,
	logFunc func(pid, processName string)) error {

	task := NewProcessDetectionTask(localAddr, remoteAddr, isLocal, logFunc)

	global.LOG.Debug("提交进程检测任务",
		zap.String("taskID", task.GetID()),
		zap.String("remoteAddr", remoteAddr),
		zap.Bool("isLocal", isLocal))

	// 🚀 优化：进程检测是CPU密集型任务，使用处理池而非通用池
	return SubmitProcessingTask(task)
}

// SubmitHeartbeatUpdate 提交心跳更新任务
func SubmitHeartbeatUpdate(remoteAddr string, updateFunc func() error) error {
	task := NewHeartbeatTask("heartbeat_update", remoteAddr, updateFunc)

	global.LOG.Debug("提交心跳更新任务",
		zap.String("taskID", task.GetID()),
		zap.String("remoteAddr", remoteAddr))

	return SubmitHeartbeatTask(task)
}

// 🚀 新增：CPU密集型任务的便捷提交函数

// SubmitDataProcessing 提交数据处理任务 - 用于CPU密集型数据处理
func SubmitDataProcessing(description string, processingFunc func() error) error {
	task := NewProcessingTask("data_processing", processingFunc)

	global.LOG.Debug("提交数据处理任务",
		zap.String("taskID", task.GetID()),
		zap.String("description", description))

	return SubmitProcessingTask(task)
}

// SubmitSerializationTask 提交序列化任务 - 用于大数据序列化/反序列化
func SubmitSerializationTask(description string, serializeFunc func() error) error {
	task := NewProcessingTask("serialization", serializeFunc)

	global.LOG.Debug("提交序列化任务",
		zap.String("taskID", task.GetID()),
		zap.String("description", description))

	return SubmitProcessingTask(task)
}

// SubmitImageProcessingTask 提交图像处理任务 - 用于图像编码/解码
func SubmitImageProcessingTask(description string, imageFunc func() error) error {
	task := NewProcessingTask("image_processing", imageFunc)

	global.LOG.Debug("提交图像处理任务",
		zap.String("taskID", task.GetID()),
		zap.String("description", description))

	return SubmitProcessingTask(task)
}

// SubmitEncryptionTask 提交加密任务 - 用于加密/解密操作
func SubmitEncryptionTask(description string, encryptFunc func() error) error {
	task := NewProcessingTask("encryption", encryptFunc)

	global.LOG.Debug("提交加密任务",
		zap.String("taskID", task.GetID()),
		zap.String("description", description))

	return SubmitProcessingTask(task)
}

// SubmitComputationTask 提交计算任务 - 用于复杂计算操作
func SubmitComputationTask(description string, computeFunc func() error) error {
	task := NewProcessingTask("computation", computeFunc)

	global.LOG.Debug("提交计算任务",
		zap.String("taskID", task.GetID()),
		zap.String("description", description))

	return SubmitProcessingTask(task)
}
