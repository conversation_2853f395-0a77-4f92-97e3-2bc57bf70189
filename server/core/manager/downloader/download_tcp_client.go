package downloader

import (
	"os"
	"server/global"
	"server/utils"
	"sync"
	"time"

	"go.uber.org/zap"
)

func init() {
	go cleanupDownloadTasks()
}

func cleanupDownloadTasks() {
	ticker := time.NewTicker(5 * time.Minute)
	for range ticker.C {
		DownloadManager.mutex.Lock()
		now := time.Now()
		for taskID, state := range DownloadManager.tasks {
			if now.Sub(state.LastUpdated) > 1*time.Minute {
				global.LOG.Warn("清理过期下载任务", zap.Uint64("taskID", taskID))
				state.File.Close()
				utils.UpdateTaskStatus(taskID, "failed", "任务超时")
				delete(DownloadManager.tasks, taskID)
			}
		}
		DownloadManager.mutex.Unlock()
	}
}

type DownloadTaskState struct {
	TaskID      uint64
	ClientID    uint
	File        *os.File
	Path        string
	DestPath    string
	NextChunk   int64
	TotalChunks int64
	ChunkSize   int64
	FileSize    int64
	Transferred int64
	LastUpdated time.Time
	Mutex       sync.Mutex
}

type DownloadTaskManager struct {
	tasks map[uint64]*DownloadTaskState // taskID -> state
	mutex sync.RWMutex
}

var DownloadManager = &DownloadTaskManager{
	tasks: make(map[uint64]*DownloadTaskState),
}

func (m *DownloadTaskManager) AddTask(state *DownloadTaskState) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	global.LOG.Info("添加下载任务", zap.Uint64("taskID", state.TaskID))
	m.tasks[state.TaskID] = state
}

func (m *DownloadTaskManager) GetTask(taskID uint64) (*DownloadTaskState, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	state, exists := m.tasks[taskID]
	return state, exists
}

func (m *DownloadTaskManager) RemoveTask(taskID uint64) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	if state, exists := m.tasks[taskID]; exists {
		if state.File != nil {
			state.File.Close()
		}
		delete(m.tasks, taskID)
	}
}
