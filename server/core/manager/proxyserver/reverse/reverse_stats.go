package reverse

import (
	"errors"
	"server/global"
	"server/model/basic"
	"sync/atomic"
	"time"
)

// GetStats 获取统计信息
func (s *ReverseProxyServer) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_connections":  atomic.LoadInt64(&s.stats.totalConnections),
		"active_connections": atomic.LoadInt64(&s.stats.activeConnections),
		"bytes_transferred":  atomic.LoadInt64(&s.stats.bytesTransferred),
	}
}

// UpdateDatabase 更新数据库统计
func (s *ReverseProxyServer) UpdateDatabase() error {
	if global.DB == nil {
		return errors.New("数据库连接不可用")
	}

	stats := s.GetStats()

	return global.DB.Model(&basic.Proxy{}).
		Where("proxy_id = ?", s.proxyID).
		Updates(map[string]interface{}{
			"total_connections":  stats["total_connections"],
			"active_connections": stats["active_connections"],
			"bytes_transferred":  stats["bytes_transferred"],
			"last_activity":      time.Now(),
		}).Error
}
