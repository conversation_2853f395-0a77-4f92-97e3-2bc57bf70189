package reverse

import (
	"fmt"
	"server/model/basic"
	"sync"
)

// ReverseProxyManager 反向代理管理器
type ReverseProxyManager struct {
	servers map[string]*ReverseProxyServer
	mu      sync.RWMutex
}

// GlobalReverseManager 全局反向代理管理器
var GlobalReverseManager = &ReverseProxyManager{
	servers: make(map[string]*ReverseProxyServer),
}

// StartServer 启动反向代理服务器
func (rm *ReverseProxyManager) StartServer(proxy *basic.Proxy) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if proxy.ProxyID == "" {
		return fmt.Errorf("ProxyID不能为空")
	}

	server := NewReverseProxyServer(proxy.ProxyID, proxy.Name, int(proxy.UserPort), int(proxy.ClientPort))

	if err := server.Start(); err != nil {
		return err
	}

	rm.servers[proxy.ProxyID] = server

	return nil
}

// StopServer 停止反向代理服务器
func (rm *ReverseProxyManager) StopServer(proxy *basic.Proxy) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if proxy.ProxyID == "" {
		return fmt.Errorf("ProxyID不能为空")
	}

	server, exists := rm.servers[proxy.ProxyID]
	if !exists {
		return fmt.Errorf("反向代理服务器不存在: %s", proxy.ProxyID)
	}

	if err := server.Stop(); err != nil {
		return err
	}

	delete(rm.servers, proxy.ProxyID)

	return nil
}
