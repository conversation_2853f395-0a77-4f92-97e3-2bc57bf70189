// reverse_proxy.go - 基于iox-master的反向代理实现
// 使用smux多路复用，避免连接管理问题
package reverse

import (
	"context"
	"errors"
	"fmt"
	"net"
	"server/global"
	"sync"

	"github.com/xtaci/smux"
	"go.uber.org/zap"
)

// ReverseProxyServer 反向代理服务器
type ReverseProxyServer struct {
	proxyID    string
	name       string
	userPort   int
	clientPort int

	userListener   net.Listener
	clientListener net.Listener

	session   *smux.Session
	ctlStream *smux.Stream

	userConnBuffer chan net.Conn

	ctx    context.Context
	cancel context.CancelFunc

	stats struct {
		totalConnections  int64
		activeConnections int64
		bytesTransferred  int64
	}

	mu sync.RWMutex
}

func readUntilEnd(conn net.Conn) ([]byte, error) {
	buf := make([]byte, 1)
	output := make([]byte, 0, 4)

	for {
		n, err := conn.Read(buf)
		if err != nil {
			return nil, err
		}

		if n != 1 || len(output) > 4 {
			return nil, errors.New("transmission error")
		}

		output = append(output, buf[0])

		if len(output) == 4 && bytesEq(PROTO_END, output[len(output)-2:]) {
			break
		}
	}

	return output[:2], nil
}

// NewReverseProxyServer 创建反向代理服务器
func NewReverseProxyServer(proxyID, name string, userPort, clientPort int) *ReverseProxyServer {
	ctx, cancel := context.WithCancel(context.Background())

	return &ReverseProxyServer{
		proxyID:        proxyID,
		name:           name,
		userPort:       userPort,
		clientPort:     clientPort,
		userConnBuffer: make(chan net.Conn, MAX_CONNECTION),
		ctx:            ctx,
		cancel:         cancel,
	}
}

// Start 启动反向代理服务器
func (s *ReverseProxyServer) Start() error {
	// 启动客户端监听器
	clientListener, err := net.Listen("tcp", fmt.Sprintf(":%d", s.clientPort))
	if err != nil {
		return fmt.Errorf("启动客户端监听器失败: %v", err)
	}
	s.clientListener = clientListener

	if global.LOG != nil {
		global.LOG.Info("🌐 [REVERSE-SERVER] 启动客户端监听器",
			zap.String("clientAddr", fmt.Sprintf(":%d", s.clientPort)),
			zap.Int("clientPort", s.clientPort))
	}

	// 启动用户监听器
	userListener, err := net.Listen("tcp", fmt.Sprintf(":%d", s.userPort))
	if err != nil {
		clientListener.Close()
		return fmt.Errorf("启动用户监听器失败: %v", err)
	}
	s.userListener = userListener

	if global.LOG != nil {
		global.LOG.Info("🌐 [REVERSE-SERVER] 启动用户监听器",
			zap.String("userAddr", fmt.Sprintf(":%d", s.userPort)),
			zap.Int("userPort", s.userPort))
	}

	// 等待客户端连接并建立session
	go s.handleClientConnection()

	// 处理用户连接
	go s.handleUserConnections()

	if global.LOG != nil {
		global.LOG.Info("✅ [REVERSE-SERVER] 反向代理启动成功",
			zap.String("proxyID", s.proxyID),
			zap.String("name", s.name),
			zap.Int("userPort", s.userPort),
			zap.Int("clientPort", s.clientPort))
	}

	return nil
}

// Stop 停止反向代理服务器
func (s *ReverseProxyServer) Stop() error {
	s.cancel()

	if s.userListener != nil {
		s.userListener.Close()
	}

	if s.clientListener != nil {
		s.clientListener.Close()
	}

	if s.ctlStream != nil {
		// 发送清理信号
		s.ctlStream.Write(marshal(Protocol{
			CMD: CTL_CLEANUP,
			N:   0,
		}))
		s.ctlStream.Close()
	}

	if s.session != nil {
		s.session.Close()
	}

	close(s.userConnBuffer)

	if global.LOG != nil {
		global.LOG.Info("🔴 [REVERSE-SERVER] 反向代理已停止", zap.String("proxyID", s.proxyID))
	}

	return nil
}
