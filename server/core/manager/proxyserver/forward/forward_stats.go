package forward

import (
	"server/core/manager/dbpool"
	"server/global"
	"server/model/basic"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ForwardProxyStats 正向代理统计信息
type ForwardProxyStats struct {
	totalConnections  int64
	activeConnections int64
	bytesTransferred  int64
	bytesReceived     int64
	lastActivity      time.Time
	mutex             sync.RWMutex

	// 定期更新相关
	updateTicker *time.Ticker
	updateStop   chan struct{}
	proxy        *basic.Proxy
}

// NewForwardProxyStats 创建统计对象
func NewForwardProxyStats(proxy *basic.Proxy) *ForwardProxyStats {
	stats := &ForwardProxyStats{
		lastActivity: time.Now(),
		updateStop:   make(chan struct{}),
		proxy:        proxy,
	}

	// 启动定期更新
	stats.startPeriodicUpdate()

	return stats
}

// AddConnection 增加连接计数
func (ps *ForwardProxyStats) AddConnection() {
	atomic.AddInt64(&ps.totalConnections, 1)
	atomic.AddInt64(&ps.activeConnections, 1)
	ps.updateLastActivity()
}

// RemoveConnection 减少连接计数
func (ps *ForwardProxyStats) RemoveConnection() {
	atomic.AddInt64(&ps.activeConnections, -1)
}

// AddBytes 增加传输字节数
func (ps *ForwardProxyStats) AddBytes(sent, received int64) {
	atomic.AddInt64(&ps.bytesTransferred, sent)
	atomic.AddInt64(&ps.bytesReceived, received)
	ps.updateLastActivity()
}

// updateLastActivity 更新最后活动时间
func (ps *ForwardProxyStats) updateLastActivity() {
	ps.mutex.Lock()
	ps.lastActivity = time.Now()
	ps.mutex.Unlock()
}

// GetStats 获取统计信息
func (ps *ForwardProxyStats) GetStats() (int64, int64, int64, int64, time.Time) {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	return atomic.LoadInt64(&ps.totalConnections),
		atomic.LoadInt64(&ps.activeConnections),
		atomic.LoadInt64(&ps.bytesTransferred),
		atomic.LoadInt64(&ps.bytesReceived),
		ps.lastActivity
}

// startPeriodicUpdate 启动定期更新统计信息到数据库
func (ps *ForwardProxyStats) startPeriodicUpdate() {
	ps.updateTicker = time.NewTicker(10 * time.Second) // 每10秒更新一次
	go func() {
		for {
			select {
			case <-ps.updateTicker.C:
				ps.updateToDatabase()
			case <-ps.updateStop:
				ps.updateTicker.Stop()
				return
			}
		}
	}()
}

// stopPeriodicUpdate 停止定期更新
func (ps *ForwardProxyStats) stopPeriodicUpdate() {
	if ps.updateStop != nil {
		close(ps.updateStop)
	}
}

// updateToDatabase 更新统计信息到数据库
func (ps *ForwardProxyStats) updateToDatabase() {
	if ps.proxy == nil {
		return
	}

	totalConn, activeConn, bytesSent, bytesReceived, lastActivity := ps.GetStats()

	// 直接更新数据库中的统计字段
	updates := map[string]interface{}{
		"total_connections":  totalConn,
		"active_connections": int(activeConn),
		"bytes_transferred":  bytesSent,
		"bytes_received":     bytesReceived,
		"last_activity":      lastActivity,
		"version":            ps.proxy.Version + 1,
	}

	// 🚀 使用数据库连接池异步更新正向代理统计信息
	if err := dbpool.ExecuteDBOperationAsyncAndWait("forward_proxy_stats_update", func(db *gorm.DB) error {
		return db.Model(ps.proxy).Updates(updates).Error
	}); err != nil {
		global.LOG.Error("更新正向代理统计信息失败",
			zap.String("proxyID", ps.proxy.ProxyID),
			zap.Error(err))
	} else {
		// 更新内存中的版本号
		ps.proxy.Version++
		ps.proxy.TotalConnections = totalConn
		ps.proxy.ActiveConnections = int(activeConn)
		ps.proxy.BytesTransferred = bytesSent
		ps.proxy.BytesReceived = bytesReceived
		ps.proxy.LastActivity = lastActivity
	}
}
