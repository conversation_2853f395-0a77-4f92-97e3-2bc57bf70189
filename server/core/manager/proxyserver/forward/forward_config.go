package forward

import (
	"server/model/basic"
	"strings"
)

// ForwardProxyConfig 正向代理配置
type ForwardProxyConfig struct {
	*basic.Proxy
	allowedIPs []string
	blockedIPs []string
}

// NewForwardProxyConfig 创建正向代理配置
func NewForwardProxyConfig(proxy *basic.Proxy) *ForwardProxyConfig {
	config := &ForwardProxyConfig{Proxy: proxy}

	// 解析IP列表
	if proxy.AllowedIPs != "" {
		config.allowedIPs = parseIPList(proxy.AllowedIPs)
	}
	if proxy.BlockedIPs != "" {
		config.blockedIPs = parseIPList(proxy.BlockedIPs)
	}

	return config
}

// parseIPList 解析逗号分隔的IP列表
func parseIPList(ipStr string) []string {
	if ipStr == "" {
		return nil
	}
	ips := strings.Split(ipStr, ",")
	result := make([]string, 0, len(ips))
	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if ip != "" {
			result = append(result, ip)
		}
	}
	return result
}
