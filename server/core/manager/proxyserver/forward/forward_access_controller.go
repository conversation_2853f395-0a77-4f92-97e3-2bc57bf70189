package forward

import "strings"

// ForwardAccessController 正向代理访问控制器
type ForwardAccessController struct {
	config *ForwardProxyConfig
}

// NewForwardAccessController 创建访问控制器
func NewForwardAccessController(config *ForwardProxyConfig) *ForwardAccessController {
	return &ForwardAccessController{config: config}
}

// IsAllowed 检查IP是否被允许访问
func (ac *ForwardAccessController) IsAllowed(clientIP string) bool {
	// 首先检查黑名单
	for _, blockedIP := range ac.config.blockedIPs {
		if matchIP(clientIP, blockedIP) {
			return false
		}
	}

	// 如果没有白名单，则允许（除非在黑名单中）
	if len(ac.config.allowedIPs) == 0 {
		return true
	}

	// 检查白名单
	for _, allowedIP := range ac.config.allowedIPs {
		if matchIP(clientIP, allowedIP) {
			return true
		}
	}

	return false
}

// matchIP 简单的IP匹配（支持通配符*）
func matchIP(clientIP, pattern string) bool {
	if pattern == "*" {
		return true
	}
	if pattern == clientIP {
		return true
	}
	// 简单的前缀匹配，如 192.168.1.*
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(clientIP, prefix)
	}
	return false
}
