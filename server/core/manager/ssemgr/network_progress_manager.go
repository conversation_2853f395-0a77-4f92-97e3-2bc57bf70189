package ssemgr

import (
	"sync"
	"time"

	"server/global"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// NetworkProgressManager 网络接口进度管理器
type NetworkProgressManager struct {
	mutex       sync.RWMutex
	progressMap map[string]*InterfaceProgress // key: clientAddr, value: progress
	sseClients  map[string][]*gin.Context     // key: clientAddr, value: SSE连接列表
}

// InterfaceProgress 接口处理进度
type InterfaceProgress struct {
	ClientAddr string                 `json:"client_addr"`
	Current    int                    `json:"current"`
	Total      int                    `json:"total"`
	Progress   float64                `json:"progress"`
	Interface  map[string]interface{} `json:"interface"`
	Message    string                 `json:"message"`
	Timestamp  int64                  `json:"timestamp"`
}

var (
	progressManager *NetworkProgressManager
	progressOnce    sync.Once
)

// GetProgressManager 获取进度管理器单例
func GetProgressManager() *NetworkProgressManager {
	progressOnce.Do(func() {
		progressManager = &NetworkProgressManager{
			progressMap: make(map[string]*InterfaceProgress),
			sseClients:  make(map[string][]*gin.Context),
		}
	})
	return progressManager
}

// AddSSEClient 添加SSE客户端连接
func (pm *NetworkProgressManager) AddSSEClient(clientAddr string, ctx *gin.Context) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if pm.sseClients[clientAddr] == nil {
		pm.sseClients[clientAddr] = make([]*gin.Context, 0)
	}
	pm.sseClients[clientAddr] = append(pm.sseClients[clientAddr], ctx)

	global.LOG.Info("添加SSE客户端连接",
		zap.String("clientAddr", clientAddr),
		zap.Int("totalConnections", len(pm.sseClients[clientAddr])))
}

// RemoveSSEClient 移除SSE客户端连接
func (pm *NetworkProgressManager) RemoveSSEClient(clientAddr string, ctx *gin.Context) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	clients := pm.sseClients[clientAddr]
	for i, client := range clients {
		if client == ctx {
			// 移除该连接
			pm.sseClients[clientAddr] = append(clients[:i], clients[i+1:]...)
			break
		}
	}

	// 如果没有连接了，清理map
	if len(pm.sseClients[clientAddr]) == 0 {
		delete(pm.sseClients, clientAddr)
	}

	global.LOG.Info("移除SSE客户端连接",
		zap.String("clientAddr", clientAddr),
		zap.Int("remainingConnections", len(pm.sseClients[clientAddr])))
}

// UpdateProgress 更新进度并广播
func (pm *NetworkProgressManager) UpdateProgress(clientAddr string, progressData map[string]interface{}) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 解析进度数据
	progress := &InterfaceProgress{
		ClientAddr: clientAddr,
		Timestamp:  time.Now().Unix(),
	}

	if current, ok := progressData["current"].(int); ok {
		progress.Current = current
	}
	if total, ok := progressData["total"].(int); ok {
		progress.Total = total
	}
	if progressVal, ok := progressData["progress"].(float64); ok {
		progress.Progress = progressVal
	}
	if message, ok := progressData["message"].(string); ok {
		progress.Message = message
	}
	if iface, ok := progressData["interface"].(map[string]interface{}); ok {
		progress.Interface = iface
	}

	// 存储进度
	pm.progressMap[clientAddr] = progress

	// 广播给所有SSE客户端
	pm.broadcastProgress(clientAddr, progress)

	global.LOG.Info("更新网络接口处理进度",
		zap.String("clientAddr", clientAddr),
		zap.Int("current", progress.Current),
		zap.Int("total", progress.Total),
		zap.Float64("progress", progress.Progress))
}

// broadcastProgress 广播进度给SSE客户端
func (pm *NetworkProgressManager) broadcastProgress(clientAddr string, progress *InterfaceProgress) {
	clients := pm.sseClients[clientAddr]
	if len(clients) == 0 {
		return
	}

	// 准备SSE数据
	sseData := gin.H{
		"type":      "interface_progress",
		"data":      progress,
		"timestamp": time.Now().Unix(),
	}

	// 发送给所有连接的客户端
	validClients := make([]*gin.Context, 0)
	for _, client := range clients {
		// 检查连接是否还有效
		select {
		case <-client.Request.Context().Done():
			// 连接已断开，跳过
			continue
		default:
			// 连接有效，发送数据
			client.SSEvent("interface_progress", sseData)
			if flusher, ok := client.Writer.(gin.ResponseWriter); ok {
				if f, ok := flusher.(interface{ Flush() }); ok {
					f.Flush()
				}
			}
			validClients = append(validClients, client)
		}
	}

	// 更新有效连接列表
	pm.sseClients[clientAddr] = validClients

	global.LOG.Debug("广播进度数据",
		zap.String("clientAddr", clientAddr),
		zap.Int("sentTo", len(validClients)))
}

// GetProgress 获取当前进度
func (pm *NetworkProgressManager) GetProgress(clientAddr string) *InterfaceProgress {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	return pm.progressMap[clientAddr]
}

// ClearProgress 清理进度数据
func (pm *NetworkProgressManager) ClearProgress(clientAddr string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	delete(pm.progressMap, clientAddr)
	global.LOG.Info("清理进度数据", zap.String("clientAddr", clientAddr))
}

// GetAllProgress 获取所有进度数据
func (pm *NetworkProgressManager) GetAllProgress() map[string]*InterfaceProgress {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	result := make(map[string]*InterfaceProgress)
	for k, v := range pm.progressMap {
		result[k] = v
	}
	return result
}
