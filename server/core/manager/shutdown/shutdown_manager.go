package shutdown

import (
	"context"
	"server/core/manager/dbpool"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ShutdownManager 全局关闭信号管理器
type ShutdownManager struct {
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	shutdownCh chan struct{}
	once       sync.Once
}

// 全局实例
var GlobalShutdownManager *ShutdownManager

// init 初始化全局关闭管理器
func init() {
	ctx, cancel := context.WithCancel(context.Background())
	GlobalShutdownManager = &ShutdownManager{
		ctx:        ctx,
		cancel:     cancel,
		shutdownCh: make(chan struct{}),
	}
}

// GetContext 获取关闭上下文
func (sm *ShutdownManager) GetContext() context.Context {
	return sm.ctx
}

// GetShutdownChannel 获取关闭信号通道
func (sm *ShutdownManager) GetShutdownChannel() <-chan struct{} {
	return sm.shutdownCh
}

// RegisterGoroutine 注册需要优雅关闭的goroutine
func (sm *ShutdownManager) RegisterGoroutine() {
	sm.wg.Add(1)
}

// UnregisterGoroutine 注销goroutine
func (sm *ShutdownManager) UnregisterGoroutine() {
	sm.wg.Done()
}

// Shutdown 触发关闭信号
func (sm *ShutdownManager) Shutdown() {
	sm.once.Do(func() {
		zap.L().Info("触发全局关闭信号")
		
		// 取消所有上下文
		sm.cancel()

		// 🚀 停止数据库连接池管理器
		dbpool.StopGlobalDBPoolManager()

		// 关闭信号通道
		close(sm.shutdownCh)
		
		// 等待所有goroutine完成，最多等待5秒
		done := make(chan struct{})
		go func() {
			sm.wg.Wait()
			close(done)
		}()
		
		select {
		case <-done:
			zap.L().Info("所有goroutine已优雅关闭")
		case <-time.After(5 * time.Second):
			zap.L().Warn("等待goroutine关闭超时，强制退出")
		}
	})
}

// IsShuttingDown 检查是否正在关闭
func (sm *ShutdownManager) IsShuttingDown() bool {
	select {
	case <-sm.ctx.Done():
		return true
	default:
		return false
	}
}

// 便捷方法
func GetContext() context.Context {
	return GlobalShutdownManager.GetContext()
}

func GetShutdownChannel() <-chan struct{} {
	return GlobalShutdownManager.GetShutdownChannel()
}

func RegisterGoroutine() {
	GlobalShutdownManager.RegisterGoroutine()
}

func UnregisterGoroutine() {
	GlobalShutdownManager.UnregisterGoroutine()
}

func Shutdown() {
	GlobalShutdownManager.Shutdown()
}

func IsShuttingDown() bool {
	return GlobalShutdownManager.IsShuttingDown()
}
