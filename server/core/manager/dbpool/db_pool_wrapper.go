package dbpool

import (
	"fmt"
	"server/core/manager/workerpool"
	"server/global"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DBOperationWrapper 数据库操作包装器
type DBOperationWrapper struct {
	db *gorm.DB
}

// NewDBOperationWrapper 创建数据库操作包装器
func NewDBOperationWrapper(db *gorm.DB) *DBOperationWrapper {
	return &DBOperationWrapper{db: db}
}

// GetDBWrapper 获取全局数据库操作包装器
func GetDBWrapper() *DBOperationWrapper {
	return NewDBOperationWrapper(global.DB)
}

// WithMonitoring 使用监控执行数据库操作
func (w *DBOperationWrapper) WithMonitoring(operation string, fn func(*gorm.DB) error) error {
	return ExecuteWithMonitoring(operation, func() error {
		return fn(w.db)
	})
}

// WithRetry 使用重试机制执行数据库操作
func (w *DBOperationWrapper) WithRetry(operation string, fn func(*gorm.DB) error, maxRetries int) error {
	return ExecuteWithRetry(operation, func() error {
		return fn(w.db)
	}, maxRetries, time.Second)
}

// WithWorkerpool 使用工作池执行数据库操作
func (w *DBOperationWrapper) WithWorkerpool(operation string, fn func(*gorm.DB) error) error {
	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation, func() error {
		return w.WithMonitoring(operation, fn)
	})

	// 提交到数据库工作池
	return workerpool.SubmitDatabaseTask(task)
}

// WithWorkerPoolAndCallback 使用工作池执行数据库操作，支持回调
func (w *DBOperationWrapper) WithWorkerPoolAndCallback(operation string, fn func(*gorm.DB) error, callback func(error)) {
	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation, func() error {
		err := w.WithMonitoring(operation, fn)
		if callback != nil {
			callback(err)
		}
		return err
	})

	// 提交到数据库工作池
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交数据库任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		if callback != nil {
			callback(err)
		}
	}
}

// 🚀 新增：同步等待版本的工作池方法

// WithWorkerpoolAndWait 使用工作池执行数据库操作并等待完成
func (w *DBOperationWrapper) WithWorkerpoolAndWait(operation string, fn func(*gorm.DB) error, timeout time.Duration) error {
	if timeout <= 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	// 创建结果通道
	resultChan := make(chan error, 1)

	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation, func() error {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("数据库操作发生panic: %v", r)
				global.LOG.Error("数据库操作panic",
					zap.String("operation", operation),
					zap.Any("panic", r))
				select {
				case resultChan <- err:
				default:
				}
			}
		}()

		err := w.WithMonitoring(operation, fn)
		select {
		case resultChan <- err:
		default:
		}
		return err
	})

	// 提交到数据库工作池
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交数据库任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		return err
	}

	// 等待结果或超时
	select {
	case err := <-resultChan:
		return err
	case <-time.After(timeout):
		return fmt.Errorf("数据库操作超时: %s (超时时间: %v)", operation, timeout)
	}
}

// WithWorkerPoolCallbackAndWait 使用工作池执行数据库操作，支持回调并等待完成
func (w *DBOperationWrapper) WithWorkerPoolCallbackAndWait(operation string, fn func(*gorm.DB) error, callback func(error), timeout time.Duration) error {
	if timeout <= 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	// 创建结果通道
	resultChan := make(chan error, 1)

	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation, func() error {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("数据库操作发生panic: %v", r)
				global.LOG.Error("数据库操作panic",
					zap.String("operation", operation),
					zap.Any("panic", r))
				if callback != nil {
					callback(err)
				}
				select {
				case resultChan <- err:
				default:
				}
			}
		}()

		err := w.WithMonitoring(operation, fn)
		if callback != nil {
			callback(err)
		}
		select {
		case resultChan <- err:
		default:
		}
		return err
	})

	// 提交到数据库工作池
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交数据库任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		if callback != nil {
			callback(err)
		}
		return err
	}

	// 等待结果或超时
	select {
	case err := <-resultChan:
		return err
	case <-time.After(timeout):
		err := fmt.Errorf("数据库操作超时: %s (超时时间: %v)", operation, timeout)
		if callback != nil {
			callback(err)
		}
		return err
	}
}

// Transaction 执行事务操作
func (w *DBOperationWrapper) Transaction(operation string, fn func(*gorm.DB) error) error {
	return ExecuteWithMonitoring(operation+"_transaction", func() error {
		return w.db.Transaction(fn)
	})
}

// TransactionWithWorkerpool 使用工作池执行事务操作
func (w *DBOperationWrapper) TransactionWithWorkerpool(operation string, fn func(*gorm.DB) error) error {
	task := workerpool.NewDatabaseTask(operation+"_transaction", func() error {
		return w.Transaction(operation, fn)
	})

	return workerpool.SubmitDatabaseTask(task)
}

// TransactionWithWorkerpoolAndWait 使用工作池执行事务操作并等待完成
func (w *DBOperationWrapper) TransactionWithWorkerpoolAndWait(operation string, fn func(*gorm.DB) error, timeout time.Duration) error {
	if timeout <= 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	// 创建结果通道
	resultChan := make(chan error, 1)

	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation+"_transaction", func() error {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("事务操作发生panic: %v", r)
				global.LOG.Error("事务操作panic",
					zap.String("operation", operation),
					zap.Any("panic", r))
				select {
				case resultChan <- err:
				default:
				}
			}
		}()

		err := w.Transaction(operation, fn)
		select {
		case resultChan <- err:
		default:
		}
		return err
	})

	// 提交到数据库工作池
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交事务任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		return err
	}

	// 等待结果或超时
	select {
	case err := <-resultChan:
		return err
	case <-time.After(timeout):
		return fmt.Errorf("事务操作超时: %s (超时时间: %v)", operation, timeout)
	}
}

// BatchOperation 批量操作
func (w *DBOperationWrapper) BatchOperation(operation string, batchSize int, items interface{}, fn func(*gorm.DB, interface{}) error) error {
	return ExecuteWithMonitoring(operation+"_batch", func() error {
		return w.db.CreateInBatches(items, batchSize).Error
	})
}

// AsyncQuery 异步查询操作
func (w *DBOperationWrapper) AsyncQuery(operation string, fn func(*gorm.DB) (interface{}, error), callback func(interface{}, error)) {
	task := workerpool.NewDatabaseTask(operation+"_async_query", func() error {
		result, err := fn(w.db)
		if callback != nil {
			callback(result, err)
		}
		return err
	})

	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交异步查询任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		if callback != nil {
			callback(nil, err)
		}
	}
}

// AsyncQueryAndWait 异步查询操作并等待完成
func (w *DBOperationWrapper) AsyncQueryAndWait(operation string, fn func(*gorm.DB) (interface{}, error), callback func(interface{}, error), timeout time.Duration) (interface{}, error) {
	if timeout <= 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	// 创建结果通道
	type queryResult struct {
		data interface{}
		err  error
	}
	resultChan := make(chan queryResult, 1)

	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation+"_async_query", func() error {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("查询操作发生panic: %v", r)
				global.LOG.Error("查询操作panic",
					zap.String("operation", operation),
					zap.Any("panic", r))
				if callback != nil {
					callback(nil, err)
				}
				select {
				case resultChan <- queryResult{nil, err}:
				default:
				}
			}
		}()

		result, err := fn(w.db)
		if callback != nil {
			callback(result, err)
		}
		select {
		case resultChan <- queryResult{result, err}:
		default:
		}
		return err
	})

	// 提交到数据库工作池
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交异步查询任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		if callback != nil {
			callback(nil, err)
		}
		return nil, err
	}

	// 等待结果或超时
	select {
	case result := <-resultChan:
		return result.data, result.err
	case <-time.After(timeout):
		err := fmt.Errorf("查询操作超时: %s (超时时间: %v)", operation, timeout)
		if callback != nil {
			callback(nil, err)
		}
		return nil, err
	}
}
