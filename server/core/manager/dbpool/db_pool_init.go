package dbpool

import (
	"runtime"
	"server/global"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InitGlobalDBPoolManager 初始化全局数据库连接池管理器
func InitGlobalDBPoolManager(db *gorm.DB) error {
	// 根据CPU核心数和系统配置计算连接池参数
	cpuCount := runtime.NumCPU()
	
	// 🚀 阶段1优化：更激进的数据库连接池配置
	config := DBPoolConfig{
		MaxOpenConns:        calculateMaxOpenConns(cpuCount),
		MaxIdleConns:        calculateMaxIdleConns(cpuCount),
		ConnMaxLifetime:     30 * time.Minute,  // 缩短连接生命周期
		ConnMaxIdleTime:     5 * time.Minute,   // 缩短空闲时间
		HealthCheckInterval: 15 * time.Second,  // 更频繁的健康检查
		HealthCheckTimeout:  3 * time.Second,   // 更短的超时时间
		QueryTimeout:        10 * time.Second,  // 缩短查询超时
		SlowQueryThreshold:  50 * time.Millisecond, // 更严格的慢查询阈值
	}
	
	// 从配置文件读取自定义配置（如果存在）
	if global.CONFIG.Sqlite.MaxOpenConns > 0 {
		config.MaxOpenConns = global.CONFIG.Sqlite.MaxOpenConns
	}
	if global.CONFIG.Sqlite.MaxIdleConns > 0 {
		config.MaxIdleConns = global.CONFIG.Sqlite.MaxIdleConns
	}
	
	var err error
	GlobalDBPoolManager, err = NewDBPoolManager(db, config)
	if err != nil {
		return err
	}
	
	// 启动数据库连接池管理器
	GlobalDBPoolManager.Start()
	
	global.LOG.Info("全局数据库连接池管理器初始化完成",
		zap.Int("maxOpenConns", config.MaxOpenConns),
		zap.Int("maxIdleConns", config.MaxIdleConns),
		zap.Duration("healthCheckInterval", config.HealthCheckInterval))
	
	return nil
}

// calculateMaxOpenConns 计算最大打开连接数
func calculateMaxOpenConns(cpuCount int) int {
	// 🚀 阶段1优化：更激进的连接池配置
	maxConns := cpuCount * 8  // 提升至8倍CPU核心数
	if maxConns > 100 {       // 提高上限至100
		maxConns = 100
	}
	if maxConns < 20 {        // 提高下限至20
		maxConns = 20
	}
	return maxConns
}

// calculateMaxIdleConns 计算最大空闲连接数
func calculateMaxIdleConns(cpuCount int) int {
	// 🚀 阶段1优化：保持更多空闲连接以减少连接创建开销
	idleConns := cpuCount * 2  // 提升至2倍CPU核心数
	if idleConns > 25 {        // 提高上限至25
		idleConns = 25
	}
	if idleConns < 5 {         // 提高下限至5
		idleConns = 5
	}
	return idleConns
}

// ExecuteWithMonitoring 全局数据库操作监控函数
func ExecuteWithMonitoring(operation string, fn func() error) error {
	if GlobalDBPoolManager == nil {
		global.LOG.Warn("数据库连接池管理器未初始化，直接执行操作")
		return fn()
	}
	return GlobalDBPoolManager.ExecuteWithMonitoring(operation, fn)
}

// ExecuteWithRetry 全局数据库操作重试函数
func ExecuteWithRetry(operation string, fn func() error, maxRetries int, retryDelay time.Duration) error {
	if GlobalDBPoolManager == nil {
		global.LOG.Warn("数据库连接池管理器未初始化，直接执行操作")
		return fn()
	}
	return GlobalDBPoolManager.ExecuteWithRetry(operation, fn, maxRetries, retryDelay)
}

// GetGlobalDBPoolStats 获取全局数据库连接池统计信息
func GetGlobalDBPoolStats() *DBPoolStats {
	if GlobalDBPoolManager == nil {
		return &DBPoolStats{}
	}
	return GlobalDBPoolManager.GetStats()
}

// GetGlobalDBPoolStatsCompatible 获取全局数据库连接池兼容格式统计信息
func GetGlobalDBPoolStatsCompatible() map[string]interface{} {
	if GlobalDBPoolManager == nil {
		return map[string]interface{}{
			"max_open_conns":        0,
			"max_idle_conns":        0,
			"open_conns":            0,
			"in_use_conns":          0,
			"idle_conns":            0,
			"total_queries":         int64(0),
			"success_queries":       int64(0),
			"failed_queries":        int64(0),
			"slow_queries":          int64(0),
			"health_status":         "未初始化",
		}
	}
	return GlobalDBPoolManager.GetCompatibleStats()
}

// StopGlobalDBPoolManager 停止全局数据库连接池管理器
func StopGlobalDBPoolManager() {
	if GlobalDBPoolManager != nil {
		GlobalDBPoolManager.Stop()
		global.LOG.Info("全局数据库连接池管理器已停止")
	}
}
