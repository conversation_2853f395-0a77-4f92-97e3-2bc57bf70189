package filetransferpool

import (
	"sync"
	"time"
)

// FileTransferPool 文件传输专用内存池
type FileTransferPool struct {
	// 分块缓冲区池 - 不同大小的缓冲区用于不同场景
	chunk32KPool   sync.Pool // 32KB - 小文件和网络较慢时
	chunk64KPool   sync.Pool // 64KB - 标准分块大小
	chunk128KPool  sync.Pool // 128KB - 大文件和网络较快时
	chunk256KPool  sync.Pool // 256KB - 超大文件和高速网络
	chunk512KPool  sync.Pool // 512KB - 特大文件传输
	chunk1MPool    sync.Pool // 1MB - 超大文件传输
	
	// 文件读写缓冲区池
	readBufferPool  sync.Pool // 文件读取缓冲区
	writeBufferPool sync.Pool // 文件写入缓冲区
	
	// 临时数据处理池
	tempDataPool sync.Pool // 临时数据处理
	
	// 统计信息
	stats FileTransferPoolStats
	mutex sync.RWMutex
}

// FileTransferPoolStats 内存池统计信息
type FileTransferPoolStats struct {
	// 分配统计
	Chunk32KAllocated   int64 `json:"chunk_32k_allocated"`
	Chunk64KAllocated   int64 `json:"chunk_64k_allocated"`
	Chunk128KAllocated  int64 `json:"chunk_128k_allocated"`
	Chunk256KAllocated  int64 `json:"chunk_256k_allocated"`
	Chunk512KAllocated  int64 `json:"chunk_512k_allocated"`
	Chunk1MAllocated    int64 `json:"chunk_1m_allocated"`
	
	// 复用统计
	Chunk32KReused      int64 `json:"chunk_32k_reused"`
	Chunk64KReused      int64 `json:"chunk_64k_reused"`
	Chunk128KReused     int64 `json:"chunk_128k_reused"`
	Chunk256KReused     int64 `json:"chunk_256k_reused"`
	Chunk512KReused     int64 `json:"chunk_512k_reused"`
	Chunk1MReused       int64 `json:"chunk_1m_reused"`
	
	// 性能统计
	TotalBytesTransferred int64     `json:"total_bytes_transferred"`
	ActiveTransfers       int64     `json:"active_transfers"`
	LastResetTime         time.Time `json:"last_reset_time"`
}

// 全局文件传输内存池实例
var GlobalFileTransferPool *FileTransferPool

// init 初始化全局文件传输内存池
func init() {
	GlobalFileTransferPool = NewFileTransferPool()
}

// NewFileTransferPool 创建新的文件传输内存池
func NewFileTransferPool() *FileTransferPool {
	pool := &FileTransferPool{
		// 32KB分块池
		chunk32KPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 32*1024)
			},
		},
		// 64KB分块池
		chunk64KPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 64*1024)
			},
		},
		// 128KB分块池
		chunk128KPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 128*1024)
			},
		},
		// 256KB分块池
		chunk256KPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 256*1024)
			},
		},
		// 512KB分块池
		chunk512KPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 512*1024)
			},
		},
		// 1MB分块池
		chunk1MPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 1024*1024)
			},
		},
		// 文件读取缓冲区池
		readBufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 64*1024) // 64KB读取缓冲区
			},
		},
		// 文件写入缓冲区池
		writeBufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 64*1024) // 64KB写入缓冲区
			},
		},
		// 临时数据处理池
		tempDataPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 32*1024) // 32KB临时缓冲区
			},
		},
		stats: FileTransferPoolStats{
			LastResetTime: time.Now(),
		},
	}
	
	return pool
}

// GetChunkBuffer 根据大小获取合适的分块缓冲区
func (p *FileTransferPool) GetChunkBuffer(size int) []byte {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	switch {
	case size <= 32*1024:
		p.stats.Chunk32KReused++
		return p.chunk32KPool.Get().([]byte)[:size]
	case size <= 64*1024:
		p.stats.Chunk64KReused++
		return p.chunk64KPool.Get().([]byte)[:size]
	case size <= 128*1024:
		p.stats.Chunk128KReused++
		return p.chunk128KPool.Get().([]byte)[:size]
	case size <= 256*1024:
		p.stats.Chunk256KReused++
		return p.chunk256KPool.Get().([]byte)[:size]
	case size <= 512*1024:
		p.stats.Chunk512KReused++
		return p.chunk512KPool.Get().([]byte)[:size]
	case size <= 1024*1024:
		p.stats.Chunk1MReused++
		return p.chunk1MPool.Get().([]byte)[:size]
	default:
		// 对于超大缓冲区，直接分配
		p.stats.Chunk1MAllocated++
		return make([]byte, size)
	}
}

// PutChunkBuffer 归还分块缓冲区
func (p *FileTransferPool) PutChunkBuffer(buf []byte) {
	if buf == nil {
		return
	}
	
	capacity := cap(buf)
	switch capacity {
	case 32*1024:
		p.chunk32KPool.Put(buf[:32*1024])
	case 64*1024:
		p.chunk64KPool.Put(buf[:64*1024])
	case 128*1024:
		p.chunk128KPool.Put(buf[:128*1024])
	case 256*1024:
		p.chunk256KPool.Put(buf[:256*1024])
	case 512*1024:
		p.chunk512KPool.Put(buf[:512*1024])
	case 1024*1024:
		p.chunk1MPool.Put(buf[:1024*1024])
	default:
		// 非标准大小的缓冲区不回收，让GC处理
	}
}

// GetReadBuffer 获取文件读取缓冲区
func (p *FileTransferPool) GetReadBuffer() []byte {
	return p.readBufferPool.Get().([]byte)
}

// PutReadBuffer 归还文件读取缓冲区
func (p *FileTransferPool) PutReadBuffer(buf []byte) {
	if buf != nil && cap(buf) == 64*1024 {
		p.readBufferPool.Put(buf[:64*1024])
	}
}

// GetWriteBuffer 获取文件写入缓冲区
func (p *FileTransferPool) GetWriteBuffer() []byte {
	return p.writeBufferPool.Get().([]byte)
}

// PutWriteBuffer 归还文件写入缓冲区
func (p *FileTransferPool) PutWriteBuffer(buf []byte) {
	if buf != nil && cap(buf) == 64*1024 {
		p.writeBufferPool.Put(buf[:64*1024])
	}
}

// GetTempBuffer 获取临时数据处理缓冲区
func (p *FileTransferPool) GetTempBuffer() []byte {
	return p.tempDataPool.Get().([]byte)
}

// PutTempBuffer 归还临时数据处理缓冲区
func (p *FileTransferPool) PutTempBuffer(buf []byte) {
	if buf != nil && cap(buf) == 32*1024 {
		p.tempDataPool.Put(buf[:32*1024])
	}
}

// GetOptimalChunkSize 根据文件大小获取最优分块大小
func (p *FileTransferPool) GetOptimalChunkSize(fileSize int64) int {
	switch {
	case fileSize < 1024*1024: // < 1MB
		return 32 * 1024 // 32KB
	case fileSize < 10*1024*1024: // < 10MB
		return 64 * 1024 // 64KB
	case fileSize < 100*1024*1024: // < 100MB
		return 128 * 1024 // 128KB
	case fileSize < 1024*1024*1024: // < 1GB
		return 256 * 1024 // 256KB
	default: // >= 1GB
		return 512 * 1024 // 512KB
	}
}

// AddTransferredBytes 添加传输字节数统计
func (p *FileTransferPool) AddTransferredBytes(bytes int64) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.stats.TotalBytesTransferred += bytes
}

// IncrementActiveTransfers 增加活跃传输计数
func (p *FileTransferPool) IncrementActiveTransfers() {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.stats.ActiveTransfers++
}

// DecrementActiveTransfers 减少活跃传输计数
func (p *FileTransferPool) DecrementActiveTransfers() {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	if p.stats.ActiveTransfers > 0 {
		p.stats.ActiveTransfers--
	}
}

// GetStats 获取内存池统计信息
func (p *FileTransferPool) GetStats() FileTransferPoolStats {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.stats
}

// ResetStats 重置统计信息
func (p *FileTransferPool) ResetStats() {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.stats = FileTransferPoolStats{
		LastResetTime: time.Now(),
	}
}

// GetMemoryUsageEstimate 估算当前内存使用量（字节）
func (p *FileTransferPool) GetMemoryUsageEstimate() int64 {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	// 估算各个池的内存使用
	var totalMemory int64
	totalMemory += p.stats.Chunk32KReused * 32 * 1024
	totalMemory += p.stats.Chunk64KReused * 64 * 1024
	totalMemory += p.stats.Chunk128KReused * 128 * 1024
	totalMemory += p.stats.Chunk256KReused * 256 * 1024
	totalMemory += p.stats.Chunk512KReused * 512 * 1024
	totalMemory += p.stats.Chunk1MReused * 1024 * 1024

	return totalMemory
}

// GetEfficiencyRatio 获取内存池效率比率（复用次数/分配次数）
func (p *FileTransferPool) GetEfficiencyRatio() float64 {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	totalReused := p.stats.Chunk32KReused + p.stats.Chunk64KReused +
		p.stats.Chunk128KReused + p.stats.Chunk256KReused +
		p.stats.Chunk512KReused + p.stats.Chunk1MReused

	totalAllocated := p.stats.Chunk32KAllocated + p.stats.Chunk64KAllocated +
		p.stats.Chunk128KAllocated + p.stats.Chunk256KAllocated +
		p.stats.Chunk512KAllocated + p.stats.Chunk1MAllocated

	if totalAllocated == 0 {
		return 0.0
	}

	return float64(totalReused) / float64(totalAllocated)
}

// 便利方法：直接使用全局实例

// GetChunkBuffer 获取分块缓冲区（全局方法）
func GetChunkBuffer(size int) []byte {
	return GlobalFileTransferPool.GetChunkBuffer(size)
}

// PutChunkBuffer 归还分块缓冲区（全局方法）
func PutChunkBuffer(buf []byte) {
	GlobalFileTransferPool.PutChunkBuffer(buf)
}

// GetReadBuffer 获取读取缓冲区（全局方法）
func GetReadBuffer() []byte {
	return GlobalFileTransferPool.GetReadBuffer()
}

// PutReadBuffer 归还读取缓冲区（全局方法）
func PutReadBuffer(buf []byte) {
	GlobalFileTransferPool.PutReadBuffer(buf)
}

// GetWriteBuffer 获取写入缓冲区（全局方法）
func GetWriteBuffer() []byte {
	return GlobalFileTransferPool.GetWriteBuffer()
}

// PutWriteBuffer 归还写入缓冲区（全局方法）
func PutWriteBuffer(buf []byte) {
	GlobalFileTransferPool.PutWriteBuffer(buf)
}

// GetTempBuffer 获取临时缓冲区（全局方法）
func GetTempBuffer() []byte {
	return GlobalFileTransferPool.GetTempBuffer()
}

// PutTempBuffer 归还临时缓冲区（全局方法）
func PutTempBuffer(buf []byte) {
	GlobalFileTransferPool.PutTempBuffer(buf)
}

// GetOptimalChunkSize 获取最优分块大小（全局方法）
func GetOptimalChunkSize(fileSize int64) int {
	return GlobalFileTransferPool.GetOptimalChunkSize(fileSize)
}

// AddTransferredBytes 添加传输字节数统计（全局方法）
func AddTransferredBytes(bytes int64) {
	GlobalFileTransferPool.AddTransferredBytes(bytes)
}

// IncrementActiveTransfers 增加活跃传输计数（全局方法）
func IncrementActiveTransfers() {
	GlobalFileTransferPool.IncrementActiveTransfers()
}

// DecrementActiveTransfers 减少活跃传输计数（全局方法）
func DecrementActiveTransfers() {
	GlobalFileTransferPool.DecrementActiveTransfers()
}

// GetStats 获取统计信息（全局方法）
func GetStats() FileTransferPoolStats {
	return GlobalFileTransferPool.GetStats()
}

// ResetStats 重置统计信息（全局方法）
func ResetStats() {
	GlobalFileTransferPool.ResetStats()
}

// GetMemoryUsageEstimate 获取内存使用估算（全局方法）
func GetMemoryUsageEstimate() int64 {
	return GlobalFileTransferPool.GetMemoryUsageEstimate()
}

// GetEfficiencyRatio 获取效率比率（全局方法）
func GetEfficiencyRatio() float64 {
	return GlobalFileTransferPool.GetEfficiencyRatio()
}
