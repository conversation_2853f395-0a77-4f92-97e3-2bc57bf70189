package internal

import (
	"os"
	"strings"
	"server/global"
	"server/model/sys"
	"server/utils"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

func SysUserInit() {
	// 从配置文件中读取管理员账号密码，如果为空则使用默认值
	adminName := "admin"
	adminPassword := "admin"
	
	// 如果配置文件中设置了管理员账号密码，则使用配置的值
	hasConfigUsername := global.CONFIG.Admin.Username != ""
	hasConfigPassword := global.CONFIG.Admin.Password != ""
	
	if hasConfigUsername {
		adminName = global.CONFIG.Admin.Username
	}
	if hasConfigPassword {
		adminPassword = global.CONFIG.Admin.Password
	}
	
	DB := global.DB
	
	// 查询数据库中是否已存在超级管理员用户（通过角色名查询）
	var superAdmin sys.SysUser
	result := DB.Where("role_name = ?", "superadmin").First(&superAdmin)
	
	if result.Error != nil {
		// 不存在超级管理员，创建新用户
		newAdmin := &sys.SysUser{
			UUID:     uuid.New(),
			Username: adminName,
			Password: utils.BcryptHash(adminPassword),
			Role: sys.Role{
				RoleName: "superadmin",
			},
		}
		
		if err := DB.Create(newAdmin).Error; err != nil {
			global.LOG.Error("初始化管理员用户失败, 系统退出", zap.Error(err))
			os.Exit(0)
		} else {
			global.LOG.Info("创建管理员账号成功: ")
			zap.L().Info("Username: " + adminName)
			zap.L().Info("Password: " + strings.Repeat("*", len(adminPassword)) )
		}
	} else {
		// 已存在超级管理员，检查是否需要更新
		needUpdate := false
		updateFields := make(map[string]interface{})
		
		// 如果配置文件中设置了用户名且与数据库中的不同，则更新用户名
		if hasConfigUsername && superAdmin.Username != adminName {
			updateFields["username"] = adminName
			needUpdate = true
		}
		
		// 如果配置文件中设置了密码，则更新密码
		if hasConfigPassword {
			updateFields["password"] = utils.BcryptHash(adminPassword)
			needUpdate = true
		}
		
		if needUpdate {
			// 更新超级管理员信息
			if err := DB.Model(&superAdmin).Updates(updateFields).Error; err != nil {
				global.LOG.Error("更新管理员用户失败", zap.Error(err))
			} else {
				global.LOG.Info("更新管理员账号成功: ")
				
				// 如果更新了用户名，显示新用户名
				if hasConfigUsername {
					zap.L().Info("Username: " + adminName)
				} else {
					zap.L().Info("Username: " + superAdmin.Username + " (未更改)")
				}
				
				// 如果更新了密码，显示新密码
				if hasConfigPassword {
					zap.L().Info("Password: " + strings.Repeat("*", len(adminPassword)) + " (已更新)")
				} else {
					zap.L().Info("Password: [未更改]")
				}
			}
		} else {
			// 无需更新，显示当前信息
			global.LOG.Info("管理员账号信息: ")
			zap.L().Info("Username: " + superAdmin.Username)
			zap.L().Info("Password: [已加密存储]")
		}
	}
	
	// 由于用户管理功能已被移除，此处仅保留初始化日志
	global.LOG.Info("系统初始化完成")
}
