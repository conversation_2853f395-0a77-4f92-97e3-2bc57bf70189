package initializer

import (
	"time"
	"go.uber.org/zap"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"server/core/manager/dbpool"
	"server/core/initializer/internal"
	"server/global"
)

func initDB() *gorm.DB {
	startTime := time.Now()
	s := global.CONFIG.Sqlite

	// 🚀 优化：使用更高效的数据库配置
	gormConfig := internal.Gorm.Config(s.Prefix, s.Singular)

	// 🚀 优化：设置更合理的GORM配置
	gormConfig.PrepareStmt = true                // 启用预编译语句缓存
	gormConfig.DisableForeignKeyConstraintWhenMigrating = true

	DB, err := gorm.Open(sqlite.Open(s.Dsn()), gormConfig)
	if err != nil {
		zap.L().Fatal("打开sqlite数据库失败: ", zap.Error(err))
	}

	// 🚀 优化：配置数据库连接池参数
	sqlDB, err := DB.DB()
	if err != nil {
		zap.L().Fatal("获取底层数据库连接失败: ", zap.Error(err))
	}

	// 🚀 优化：根据系统负载动态设置连接池参数
	maxIdleConns := s.MaxIdleConns
	maxOpenConns := s.MaxOpenConns

	// 如果配置值为0，使用智能默认值
	if maxIdleConns == 0 {
		maxIdleConns = 10 // SQLite推荐值
	}
	if maxOpenConns == 0 {
		maxOpenConns = 25 // SQLite推荐值
	}

	sqlDB.SetMaxIdleConns(maxIdleConns)           // 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxOpenConns(maxOpenConns)           // 设置打开数据库连接的最大数量
	sqlDB.SetConnMaxLifetime(time.Hour)           // 设置连接可复用的最大时间
	sqlDB.SetConnMaxIdleTime(10 * time.Minute)    // 设置连接空闲超时时间

	// 🚀 优化：执行数据库预热查询
	if err := performDatabaseWarmup(DB); err != nil {
		zap.L().Warn("数据库预热失败", zap.Error(err))
	}

	zap.L().Info("✅ 数据库连接初始化完成",
		zap.Int("maxIdleConns", maxIdleConns),
		zap.Int("maxOpenConns", maxOpenConns),
		zap.Duration("耗时", time.Since(startTime)))

	// 🚀 异步初始化数据库连接池管理器，避免阻塞
	go func() {
		if err := dbpool.InitGlobalDBPoolManager(DB); err != nil {
			zap.L().Error("初始化数据库连接池管理器失败", zap.Error(err))
		} else {
			zap.L().Info("数据库连接池管理器初始化完成")
		}

		// 启动数据库连接池升级监控
		dbpool.StartUpgradeMonitoring()
		zap.L().Info("数据库连接池监控启动完成")
	}()

	return DB
}

// performDatabaseWarmup 执行数据库预热查询
func performDatabaseWarmup(db *gorm.DB) error {
	// 执行简单查询预热连接池
	var count int64
	if err := db.Raw("SELECT COUNT(*) FROM sqlite_master WHERE type='table'").Scan(&count).Error; err != nil {
		return err
	}

	zap.L().Debug("数据库预热完成", zap.Int64("表数量", count))
	return nil
}
