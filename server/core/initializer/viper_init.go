package initializer

import (
	"fmt"
	"path/filepath"
	"server/global"

	"github.com/fsnotify/fsnotify"
	"github.com/google/uuid"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

func initViper() *viper.Viper {
	configPath := filepath.Join("./config.yaml")
	v := viper.New()
	v.SetConfigFile(configPath)
	if err := v.ReadInConfig(); err != nil {
		zap.L().Fatal("读取Config.yaml失败，检查是否在根目录", zap.Error(err))
	}
	v.SetConfigType("yaml")

	v.WatchConfig()

	v.OnConfigChange(func(e fsnotify.Event) {
		zap.L().Info("Config发生更改: " + e.Name)

	})

	if err := v.Unmarshal(&global.CONFIG); err != nil {
		panic(fmt.Errorf("解析Config失败", zap.Error(err)))
	}
	if global.CONFIG.Server.UploadDir == "" {
		global.CONFIG.Server.UploadDir = "./upload"
	}
	if global.CONFIG.Server.DownloadDir == "" {
		global.CONFIG.Server.DownloadDir = "./download"
	}
	if global.CONFIG.Server.ID == "RANDOM" || global.CONFIG.Server.ID == "" {
		global.CONFIG.Server.ID = uuid.New().String()
	}

	if global.CONFIG.Server.ServerStartPort == 0 || global.CONFIG.Server.ServerStartPort > 65535 {
		global.CONFIG.Server.ServerStartPort = 20000
	}
	if global.CONFIG.Server.ServerEndPort == 0 || global.CONFIG.Server.ServerEndPort > 65535 {
		global.CONFIG.Server.ServerEndPort = 65535
	}

	return v
}
