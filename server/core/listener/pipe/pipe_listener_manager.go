package pipe

import (
	"fmt"
	"net"
	"server/core/manager/dbpool"
	"server/global"
	"server/model/sys"
	"sync"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// PipeListenerManager 管理所有的pipe监听器
type PipeListenerManager struct {
	listeners map[uint]*PipeListener
	mutex     sync.Mutex
}

// 全局的pipe监听器管理器
var PipeManager = &PipeListenerManager{
	listeners: make(map[uint]*PipeListener),
}

// StartListener 启动一个pipe监听器
func (m *PipeListenerManager) StartListener(config sys.Listener) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查监听器是否已经存在
	if _, exists := m.listeners[config.ID]; exists {
		global.LOG.Info(fmt.Sprintf("pipe监听器 %s 已经在运行中", config.LocalListenAddr))
		return
	}

	// 创建TCP监听器
	listener, err := net.Listen("tcp", config.LocalListenAddr)
	if err != nil {
		global.LOG.Error(fmt.Sprintf("启动pipe监听器 %s 失败", config.LocalListenAddr), zap.Error(err))
		return
	}

	// 创建pipe监听器实例
	pipeListener := &PipeListener{
		ID:                config.ID,
		LocalListenAddr:   config.LocalListenAddr,
		RemoteConnectAddr: config.RemoteConnectAddr,
		Status:            config.Status,
		Listener:          listener,
		Connections:       make(map[string]net.Conn),
	}

	// 添加到管理器
	m.listeners[config.ID] = pipeListener

	// 注册到统计管理器
	RegisterListener(pipeListener)

	// 启动goroutine处理连接
	go pipeListener.handleConnections()

	global.LOG.Info(fmt.Sprintf("pipe监听器 %s 启动成功", config.LocalListenAddr))
}

// StopListener 停止一个pipe监听器
func (m *PipeListenerManager) StopListener(id uint) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查监听器是否存在
	pipeListener, exists := m.listeners[id]
	if !exists {
		global.LOG.Info(fmt.Sprintf("pipe监听器 ID:%d 不存在或已停止", id))
		return
	}

	// 关闭所有连接
	pipeListener.Mutex.Lock()
	for addr, conn := range pipeListener.Connections {
		conn.Close()
		delete(pipeListener.Connections, addr)
	}
	pipeListener.Mutex.Unlock()

	// 关闭监听器
	pipeListener.Listener.Close()

	// 从统计管理器中注销
	UnregisterListener(id)

	// 从管理器中移除
	delete(m.listeners, id)

	global.LOG.Info(fmt.Sprintf("pipe监听器 %s 已停止", pipeListener.LocalListenAddr))
}

// UpdateListener 更新监听器状态
func (m *PipeListenerManager) UpdatePipeListener(id uint, status int) {
	// 🚀 获取监听器配置
	var listener sys.Listener
	if err := dbpool.ExecuteDBOperationAsyncAndWait("pipe_listener_config", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&listener).Error
	}); err != nil {
		global.LOG.Error("获取监听器配置失败", zap.Error(err))
		return
	}

	// 根据状态启动或停止监听器
	if status == 1 {
		// 启动监听器
		m.StartListener(listener)
	} else {
		// 停止监听器
		m.StopListener(id)
	}
}

// CloseConnection 关闭指定监听器的指定连接
func (m *PipeListenerManager) CloseConnection(listenerID uint, remoteAddr string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查监听器是否存在
	pipeListener, exists := m.listeners[listenerID]
	if !exists {
		return fmt.Errorf("监听器 ID:%d 不存在", listenerID)
	}

	// 关闭连接
	pipeListener.Mutex.Lock()
	defer pipeListener.Mutex.Unlock()

	conn, exists := pipeListener.Connections[remoteAddr]
	if !exists {
		return fmt.Errorf("连接 %s 不存在", remoteAddr)
	}

	// 关闭连接
	conn.Close()
	delete(pipeListener.Connections, remoteAddr)

	global.LOG.Info(fmt.Sprintf("已关闭来自 %s 的连接", remoteAddr))

	return nil
}

// SendCommand 向指定监听器的指定连接发送命令
func (m *PipeListenerManager) SendCommand(listenerID uint, remoteAddr string, command string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查监听器是否存在
	pipeListener, exists := m.listeners[listenerID]
	if !exists {
		return fmt.Errorf("监听器 ID:%d 不存在", listenerID)
	}

	// 发送命令
	return pipeListener.SendCommand(remoteAddr, command)
}
