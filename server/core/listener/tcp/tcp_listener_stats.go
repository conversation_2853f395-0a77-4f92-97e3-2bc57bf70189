package tcp

import (
	"server/core/listener/stats"
	"sync"
	"time"
)

// 全局统计管理器
var (
	globalStats = &GlobalStats{
		listeners: make(map[uint]*TCPListener),
	}
)

// GlobalStats 全局统计管理器
type GlobalStats struct {
	listeners map[uint]*TCPListener
	mutex     sync.RWMutex
}

// RegisterListener 注册监听器到统计管理器
func RegisterListener(listener *TCPListener) {
	globalStats.mutex.Lock()
	defer globalStats.mutex.Unlock()
	globalStats.listeners[listener.ID] = listener
	listener.initStats()
}

// UnregisterListener 从统计管理器中移除监听器
func UnregisterListener(listenerID uint) {
	globalStats.mutex.Lock()
	defer globalStats.mutex.Unlock()
	delete(globalStats.listeners, listenerID)
}

// GetAllListenerStats 获取所有监听器的统计信息
func GetAllListenerStats() []stats.ListenerStats {
	globalStats.mutex.RLock()
	defer globalStats.mutex.RUnlock()

	var stats []stats.ListenerStats
	for _, tcpListener := range globalStats.listeners {
		stats = append(stats, tcpListener.getStats())
	}
	return stats
}

// GetConnectionStats 获取连接统计信息
func GetConnectionStats() stats.ConnectionStats {
	globalStats.mutex.RLock()
	defer globalStats.mutex.RUnlock()
	
	var totalConnections, activeConnections int64
	var totalConnTime float64
	var lastConnTime time.Time
	var disconnections int64
	
	count := 0
	for _, listener := range globalStats.listeners {
		stats := listener.getStats()
		totalConnections += int64(stats.TotalConnections)
		activeConnections += int64(stats.ActiveConnections)
		
		if stats.LastActivity.After(lastConnTime) {
			lastConnTime = stats.LastActivity
		}
		
		// 计算平均连接时间（简化计算）
		if stats.TotalConnections > 0 {
			uptime := time.Since(stats.StartTime).Hours()
			if uptime > 0 {
				totalConnTime += uptime / float64(stats.TotalConnections)
				count++
			}
		}
	}
	
	var avgConnTime float64
	if count > 0 {
		avgConnTime = totalConnTime / float64(count)
	}
	
	var connectionsPerHour float64
	if len(globalStats.listeners) > 0 {
		// 计算每小时连接数（基于最早的监听器启动时间）
		var earliestStart time.Time
		for _, listener := range globalStats.listeners {
			stats := listener.getStats()
			if earliestStart.IsZero() || stats.StartTime.Before(earliestStart) {
				earliestStart = stats.StartTime
			}
		}
		
		if !earliestStart.IsZero() {
			hours := time.Since(earliestStart).Hours()
			if hours > 0 {
				connectionsPerHour = float64(totalConnections) / hours
			}
		}
	}
	
	return stats.ConnectionStats{
		TotalConnections:    totalConnections,
		ActiveConnections:   activeConnections,
		ConnectionsPerHour:  connectionsPerHour,
		AverageConnTime:     avgConnTime,
		LastConnectionTime:  lastConnTime,
		DisconnectionsToday: disconnections, // 这里可以后续实现基于时间的统计
	}
}

// GetTrafficStats 获取流量统计信息
func GetTrafficStats() stats.TrafficStats {
	globalStats.mutex.RLock()
	defer globalStats.mutex.RUnlock()
	
	var bytesReceived, bytesSent, packetsReceived, packetsSent int64
	var totalDataTransferred int64
	var earliestStart time.Time
	
	for _, listener := range globalStats.listeners {
		listener.stats.mutex.RLock()
		bytesReceived += listener.stats.bytesReceived
		bytesSent += listener.stats.bytesSent
		packetsReceived += listener.stats.packetsReceived
		packetsSent += listener.stats.packetsSent
		totalDataTransferred += listener.stats.dataTransferred
		
		if earliestStart.IsZero() || listener.stats.startTime.Before(earliestStart) {
			earliestStart = listener.stats.startTime
		}
		listener.stats.mutex.RUnlock()
	}
	
	// 计算平均速度（MB/s）
	var averageSpeed, peakSpeed float64
	if !earliestStart.IsZero() {
		hours := time.Since(earliestStart).Hours()
		if hours > 0 {
			totalMB := float64(totalDataTransferred) / (1024 * 1024)
			averageSpeed = totalMB / hours / 3600 // MB/s
			peakSpeed = averageSpeed * 2 // 简化的峰值计算
		}
	}
	
	return stats.TrafficStats{
		BytesReceived:   bytesReceived,
		BytesSent:       bytesSent,
		PacketsReceived: packetsReceived,
		PacketsSent:     packetsSent,
		AverageSpeed:    averageSpeed,
		PeakSpeed:       peakSpeed,
		LastResetTime:   earliestStart,
	}
}

// GetListenerByID 根据ID获取监听器统计
func GetListenerByID(id uint) stats.ListenerStats {
	globalStats.mutex.RLock()
	defer globalStats.mutex.RUnlock()

	tcpListener := globalStats.listeners[id]
	if tcpListener == nil {
		return nil
	}
	return tcpListener.getStats()
}

// GetActiveListenerCount 获取活跃监听器数量
func GetActiveListenerCount() int {
	globalStats.mutex.RLock()
	defer globalStats.mutex.RUnlock()
	
	count := 0
	for _, listener := range globalStats.listeners {
		if listener.Status == 1 {
			count++
		}
	}
	return count
}

// ResetStats 重置所有统计信息
func ResetStats() {
	globalStats.mutex.Lock()
	defer globalStats.mutex.Unlock()
	
	for _, listener := range globalStats.listeners {
		listener.stats.mutex.Lock()
		listener.stats.totalConnections = 0
		listener.stats.dataTransferred = 0
		listener.stats.packetsProcessed = 0
		listener.stats.errorCount = 0
		listener.stats.bytesReceived = 0
		listener.stats.bytesSent = 0
		listener.stats.packetsReceived = 0
		listener.stats.packetsSent = 0
		listener.stats.startTime = time.Now()
		listener.stats.lastActivity = time.Now()
		listener.stats.mutex.Unlock()
	}
}

// TCPStatsProvider TCP统计提供者
type TCPStatsProvider struct{}

// GetAllListenerStats 实现 ListenerStatsProvider 接口
func (p *TCPStatsProvider) GetAllListenerStats() []stats.ListenerStats {
	return GetAllListenerStats()
}

// GetConnectionStats 实现 ListenerStatsProvider 接口
func (p *TCPStatsProvider) GetConnectionStats() stats.ConnectionStats {
	return GetConnectionStats()
}

// GetTrafficStats 实现 ListenerStatsProvider 接口
func (p *TCPStatsProvider) GetTrafficStats() stats.TrafficStats {
	return GetTrafficStats()
}

// GetListenerByID 实现 ListenerStatsProvider 接口
func (p *TCPStatsProvider) GetListenerByID(id uint) stats.ListenerStats {
	return GetListenerByID(id)
}

// GetActiveListenerCount 实现 ListenerStatsProvider 接口
func (p *TCPStatsProvider) GetActiveListenerCount() int {
	return GetActiveListenerCount()
}

// ResetStats 实现 ListenerStatsProvider 接口
func (p *TCPStatsProvider) ResetStats() {
	ResetStats()
}

// GetStatsProvider 获取TCP统计提供者实例
func GetStatsProvider() stats.ListenerStatsProvider {
	return &TCPStatsProvider{}
}
