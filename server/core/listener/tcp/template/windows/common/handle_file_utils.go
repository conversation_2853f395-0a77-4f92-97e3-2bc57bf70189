//go:build windows
// +build windows

package common

import (
	"fmt"
	"os"
	"strings"
)

func handleFileMoveError(err error, resp *FileMoveResponse) {
	switch {
	case os.IsPermission(err):
		resp.NotAllow = true
	case os.IsExist(err):
		resp.DestinationExists = true
	default:
		resp.Success = false
	}
}

// 统一错误处理
func handleDeleteError(err error, resp *FileDeleteResponse) {
	switch {
	case os.IsPermission(err):
		resp.NotAllow = true
	case os.IsNotExist(err):
		resp.NotExist = true
	default:
		resp.Success = false
	}
}

// 处理上传错误
func handleUploadError(err error, resp *FileUploadResponse) {
	switch {
	case os.IsPermission(err):
		resp.Error = "权限不足"
	case os.IsExist(err):
		resp.Error = "目标文件已存在"
	case os.IsNotExist(err):
		resp.Error = "路径不存在"
	default:
		resp.Error = "操作失败"
	}
	resp.Success = false
}

// 处理文件操作错误
func handleFileError(err error, resp *FileCopyResponse) {
	switch {
	case os.IsPermission(err):
		resp.NotAllow = true
	case os.IsNotExist(err):
		resp.SourceExists = false
	default:
		resp.Success = false
	}
}

// formatWindowsPermissions 格式化Windows权限为字符串形式
func formatWindowsPermissions(mode os.FileMode) string {
	var result strings.Builder

	// 文件类型
	switch mode & os.ModeType {
	case os.ModeDir:
		result.WriteString("d")
	case os.ModeSymlink:
		result.WriteString("l")
	default:
		result.WriteString("-")
	}

	// Windows权限相对简单，主要基于只读属性
	// 所有者权限
	if mode&0400 != 0 {
		result.WriteString("r")
	} else {
		result.WriteString("-")
	}
	if mode&0200 != 0 {
		result.WriteString("w")
	} else {
		result.WriteString("-")
	}
	if mode&0100 != 0 {
		result.WriteString("x")
	} else {
		result.WriteString("-")
	}

	// 组权限（Windows通常与所有者相同）
	if mode&0040 != 0 {
		result.WriteString("r")
	} else {
		result.WriteString("-")
	}
	if mode&0020 != 0 {
		result.WriteString("w")
	} else {
		result.WriteString("-")
	}
	if mode&0010 != 0 {
		result.WriteString("x")
	} else {
		result.WriteString("-")
	}

	// 其他用户权限（Windows通常与所有者相同）
	if mode&0004 != 0 {
		result.WriteString("r")
	} else {
		result.WriteString("-")
	}
	if mode&0002 != 0 {
		result.WriteString("w")
	} else {
		result.WriteString("-")
	}
	if mode&0001 != 0 {
		result.WriteString("x")
	} else {
		result.WriteString("-")
	}

	return result.String()
}

// getMaxFileSizeForFile 统一文件大小限制为200MB，移除文件类型限制
func getMaxFileSizeForFile(filePath string, fileSize int64) int64 {
	// 移除所有文件类型限制，统一设置为200MB
	// 无论什么类型的文件，只要不超过200MB都可以在编辑器中打开
	return 200 * 1024 * 1024 // 200MB
}

// formatFileSize 格式化文件大小显示
func formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// isBinaryContent 移除二进制文件限制，允许所有文件在编辑器中打开
func isBinaryContent(content []byte) bool {
	// 移除所有二进制文件检查，允许所有文件类型在编辑器中打开
	// 包括二进制文件、图片、可执行文件等都可以以文本形式显示
	return false
}
