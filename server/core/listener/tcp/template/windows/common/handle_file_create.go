//go:build windows
// +build windows

package common

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
)

// FileCreateRequest 文件创建请求
type FileCreateRequest struct {
	TaskID      uint64 `json:"task_id"`      // 任务ID
	Path        string `json:"path"`         // 文件路径
	Content     string `json:"content"`      // 文件内容
	Encoding    string `json:"encoding"`     // 编码格式
	ForceCreate bool   `json:"force_create"` // 强制创建（覆盖已存在文件）
	CreateDirs  bool   `json:"create_dirs"`  // 创建目录
	FileMode    string `json:"file_mode"`    // 文件权限（Unix格式）
}

// FileCreateResponse 文件创建响应
type FileCreateResponse struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Success      bool   `json:"success"`       // 操作是否成功
	NotAllow     bool   `json:"not_allow"`     // 是否权限不足
	FileExists   bool   `json:"file_exists"`   // 文件已存在
	IsDir        bool   `json:"is_dir"`        // 目标是否为目录
	BytesWritten int64  `json:"bytes_written"` // 写入的字节数
	Error        string `json:"error"`         // 错误信息
	ActualPath   string `json:"actual_path"`   // 实际文件路径
	FileMode     string `json:"file_mode"`     // 实际文件权限
}

// handleFileCreate 处理文件创建请求
func (cm *ConnectionManager) handleFileCreate(packet *Packet) {
	var req FileCreateRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := &FileCreateResponse{
		TaskID:  0,
		Success: false,
		Error:   "",
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("反序列化FileCreateRequest失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "请求格式错误"
		cm.sendResp(File, FileCreate, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	// 使用写锁保护文件创建
	fileMutex.Lock()
	resp := createFile(&req)
	fileMutex.Unlock()

	cm.sendResp(File, FileCreate, resp)
}

// createFile 创建文件
func createFile(req *FileCreateRequest) *FileCreateResponse {
	resp := &FileCreateResponse{
		TaskID:     req.TaskID,
		Success:    false,
		Error:      "",
		ActualPath: req.Path,
	}

	// 获取绝对路径
	absPath, err := filepath.Abs(req.Path)
	if err != nil {
		resp.Error = fmt.Sprintf("获取绝对路径失败: %v", err)
		return resp
	}
	resp.ActualPath = absPath

	// 检查文件是否已存在
	if fileInfo, err := os.Stat(absPath); err == nil {
		if fileInfo.IsDir() {
			resp.IsDir = true
			resp.Error = "目标是目录，不能创建文件"
			return resp
		}
		if !req.ForceCreate {
			resp.FileExists = true
			resp.Error = "文件已存在，使用force_create参数强制覆盖"
			return resp
		}
	}

	// 创建目录（如果需要）
	if req.CreateDirs {
		dir := filepath.Dir(absPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			resp.Error = fmt.Sprintf("创建目录失败: %v", err)
			return resp
		}
	}

	// 写入文件内容
	content := []byte(req.Content)
	err = os.WriteFile(absPath, content, 0644)
	if err != nil {
		if os.IsPermission(err) {
			resp.NotAllow = true
		}
		resp.Error = fmt.Sprintf("创建文件失败: %v", err)
		return resp
	}

	// 设置文件权限（Windows下权限设置有限）
	if req.FileMode != "" {
		// Windows下权限设置较为有限，这里主要是为了兼容性
		resp.FileMode = "0644" // Windows默认权限
	} else {
		resp.FileMode = "0644"
	}

	resp.Success = true
	resp.BytesWritten = int64(len(content))
	log.Printf("文件创建成功: %s (大小: %d 字节)", absPath, len(content))
	return resp
}
