//go:build windows
// +build windows

package common

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
)

// DirCopyRequest 目录复制请求
type DirCopyRequest struct {
	TaskID          uint64 `json:"task_id"`          // 任务ID
	SourcePath      string `json:"source_path"`      // 源目录路径
	DestinationPath string `json:"destination_path"` // 目标目录路径
	Overwrite       bool   `json:"overwrite"`        // 是否覆盖已存在的文件
	PreserveAttrs   bool   `json:"preserve_attrs"`   // 是否保留文件属性
	FollowSymlinks  bool   `json:"follow_symlinks"`  // 是否跟随符号链接
}

// DirCopyResponse 目录复制响应
type DirCopyResponse struct {
	TaskID            uint64 `json:"task_id"`            // 任务ID
	Success           bool   `json:"success"`            // 操作是否成功
	SourceExists      bool   `json:"source_exists"`      // 源目录是否存在
	DestinationExists bool   `json:"destination_exists"` // 目标目录是否存在
	NotAllow          bool   `json:"not_allow"`          // 是否权限不足
	CopiedFiles       int    `json:"copied_files"`       // 已复制的文件数量
	CopiedDirs        int    `json:"copied_dirs"`        // 已复制的目录数量
	SkippedFiles      int    `json:"skipped_files"`      // 跳过的文件数量
	ActualSource      string `json:"actual_source"`      // 实际源路径
	ActualDestination string `json:"actual_destination"` // 实际目标路径
	Error             string `json:"error"`              // 错误信息
}

// handleDirCopy 处理目录复制请求
func (cm *ConnectionManager) handleDirCopy(packet *Packet) {
	var req DirCopyRequest
	respErr := &DirCopyResponse{
		TaskID:  0,
		Success: false,
		Error:   "",
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("目录复制请求反序列化失败: %v", err)
		respErr.TaskID = req.TaskID
		respErr.Error = fmt.Sprintf("目录复制请求反序列化失败: %v", err)
		cm.sendResp(Dir, DirCopy, respErr)
		return
	}
	respErr.TaskID = req.TaskID

	// 参数验证
	source := strings.TrimSpace(req.SourcePath)
	destination := strings.TrimSpace(req.DestinationPath)

	log.Printf("开始复制DirCopy: ")
	log.Printf("source: %v", source)
	log.Printf("destination: %v", destination)

	if source == "" || destination == "" {
		log.Printf("源路径或目标路径为空")
		respErr.Error = "源路径或目标路径为空"
		cm.sendResp(Dir, DirCopy, respErr)
		return
	}

	// 路径安全检查
	if !isValidPath(source) || !isValidPath(destination) {
		log.Printf("无效的路径: %s -> %s", source, destination)
		respErr.Error = fmt.Sprintf("无效的路径: %s -> %s", source, destination)
		cm.sendResp(Dir, DirCopy, respErr)
		return
	}

	// 获取绝对路径
	absSource, err := getAbsolutePath(source)
	if err != nil {
		log.Printf("获取源路径绝对路径失败: %v", err)
		respErr.Error = fmt.Sprintf("获取源路径绝对路径失败: %v", err)
		cm.sendResp(Dir, DirCopy, respErr)
		return
	}

	absDestination, err := getAbsolutePath(destination)
	if err != nil {
		log.Printf("获取目标路径绝对路径失败: %v", err)
		respErr.Error = fmt.Sprintf("获取目标路径绝对路径失败: %v", err)
		cm.sendResp(Dir, DirCopy, respErr)
		return
	}

	// 使用读锁，因为复制操作不会修改源目录
	dirOpMutex.RLock()
	defer dirOpMutex.RUnlock()

	resp := cm.copyDirectory(absSource, absDestination, &req)
	cm.sendResp(Dir, DirCopy, resp)
}

// copyDirectory 复制目录的核心逻辑
func (cm *ConnectionManager) copyDirectory(source, destination string, req *DirCopyRequest) *DirCopyResponse {
	resp := &DirCopyResponse{
		TaskID:            req.TaskID,
		Success:           false,
		SourceExists:      false,
		DestinationExists: false,
		NotAllow:          false,
		CopiedFiles:       0,
		CopiedDirs:        0,
		SkippedFiles:      0,
		ActualSource:      source,
		ActualDestination: destination,
		Error:             "",
	}

	// 检查源路径是否存在且是目录
	sourceExists, err := isDirectory(source)
	if err != nil {
		log.Printf("检查源路径出错: %v", err)
		if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "源路径权限不足"
		} else {
			resp.Error = fmt.Sprintf("源路径检查失败: %v", err)
		}
		return resp
	}

	if !sourceExists {
		resp.SourceExists = false
		resp.Error = "源目录不存在"
		return resp
	}
	resp.SourceExists = true

	// 检查目标路径是否存在
	destExists, err := PathExists(destination)
	if err != nil {
		log.Printf("检查目标路径出错: %v", err)
		if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "目标路径权限不足"
		} else {
			resp.Error = fmt.Sprintf("目标路径检查失败: %v", err)
		}
		return resp
	}
	resp.DestinationExists = destExists

	// 如果目标存在且不允许覆盖
	if destExists && !req.Overwrite {
		resp.Error = "目标路径已存在且未启用覆盖"
		return resp
	}

	// 执行目录复制
	stats := &copyStats{}
	if err := copyDirectoryRecursive(source, destination, req, stats); err != nil {
		log.Printf("目录复制失败: %v", err)
		if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "复制权限不足"
		} else {
			resp.Error = fmt.Sprintf("复制失败: %v", err)
		}
		// 即使失败也返回已复制的统计信息
		resp.CopiedFiles = stats.copiedFiles
		resp.CopiedDirs = stats.copiedDirs
		resp.SkippedFiles = stats.skippedFiles
		return resp
	}

	// 验证复制结果
	if copied, _ := isDirectory(destination); copied {
		resp.Success = true
		resp.CopiedFiles = stats.copiedFiles
		resp.CopiedDirs = stats.copiedDirs
		resp.SkippedFiles = stats.skippedFiles
	} else {
		resp.Error = "复制验证失败"
	}

	return resp
}

// copyDirectoryRecursive 递归复制目录的实现
func copyDirectoryRecursive(src, dst string, req *DirCopyRequest, stats *copyStats) error {
	// 创建目标目录
	if err := os.MkdirAll(dst, 0755); err != nil {
		return fmt.Errorf("创建目标目录失败: %v", err)
	}
	stats.copiedDirs++

	// 读取源目录
	entries, err := os.ReadDir(src)
	if err != nil {
		return fmt.Errorf("读取源目录失败: %v", err)
	}

	// 遍历复制每个条目
	for _, entry := range entries {
		// 跳过隐藏文件（如果配置要求）
		if !req.FollowSymlinks && strings.HasPrefix(entry.Name(), ".") {
			stats.skippedFiles++
			continue
		}

		srcPath := filepath.Join(src, entry.Name())
		dstPath := filepath.Join(dst, entry.Name())

		// 处理符号链接
		if entry.Type()&os.ModeSymlink != 0 {
			if !req.FollowSymlinks {
				stats.skippedFiles++
				continue
			}
			// 如果跟随符号链接，获取真实路径
			realPath, err := filepath.EvalSymlinks(srcPath)
			if err != nil {
				log.Printf("解析符号链接失败: %v", err)
				stats.skippedFiles++
				continue
			}
			srcPath = realPath
		}

		if entry.IsDir() {
			if err = copyDirectoryRecursive(srcPath, dstPath, req, stats); err != nil {
				return err
			}
		} else {
			// 检查目标文件是否存在
			if _, err = os.Stat(dstPath); err == nil && !req.Overwrite {
				stats.skippedFiles++
				continue
			}

			if err = copyFileWithAttributes(srcPath, dstPath, req.PreserveAttrs); err != nil {
				log.Printf("复制文件失败 %s -> %s: %v", srcPath, dstPath, err)
				stats.skippedFiles++
				continue
			}
			stats.copiedFiles++
		}
	}
	return nil
}
