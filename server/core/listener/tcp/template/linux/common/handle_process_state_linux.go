//go:build linux
// +build linux

package common

/*
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>

// 快速获取进程完整状态 - C语言实现，模拟ps命令的完整输出
// 包括主状态和所有修饰符，与ps -o stat=输出完全一致
// 返回值需要在Go中使用C.free()释放
char* get_process_status_fast(int pid) {
    char stat_path[64];
    char status_path[64];
    char buffer[1024];
    char status_buffer[2048];
    char *result = NULL;
    int fd;
    ssize_t bytes_read;
    char state_char;
    char full_state[16] = {0}; // 存储完整状态字符串
    int state_idx = 0;

    // 构造路径
    snprintf(stat_path, sizeof(stat_path), "/proc/%d/stat", pid);
    snprintf(status_path, sizeof(status_path), "/proc/%d/status", pid);

    // 首先从/proc/[pid]/stat获取基本状态
    fd = open(stat_path, O_RDONLY);
    if (fd == -1) {
        return NULL;
    }

    bytes_read = read(fd, buffer, sizeof(buffer) - 1);
    close(fd);

    if (bytes_read <= 0) {
        return NULL;
    }

    buffer[bytes_read] = '\0';

    // 解析stat文件 - 找到第3个字段（进程状态）
    char *last_paren = strrchr(buffer, ')');
    if (last_paren == NULL) {
        return NULL;
    }

    // 跳过')' 和后面的空格，读取状态字符
    char *state_pos = last_paren + 1;
    while (*state_pos == ' ' || *state_pos == '\t') {
        state_pos++;
    }

    if (*state_pos == '\0') {
        return NULL;
    }

    state_char = *state_pos;
    full_state[state_idx++] = state_char;

    // 从/proc/[pid]/status获取更详细的状态信息来推断修饰符
    fd = open(status_path, O_RDONLY);
    if (fd != -1) {
        bytes_read = read(fd, status_buffer, sizeof(status_buffer) - 1);
        close(fd);

        if (bytes_read > 0) {
            status_buffer[bytes_read] = '\0';

            // 解析status文件中的各种信息来确定修饰符
            char *line = strtok(status_buffer, "\n");
            int nice_value = 0;
            int num_threads = 1;
            int is_session_leader = 0;
            int vmlock = 0;

            while (line != NULL) {
                // 🔧 优化：更准确的会话领导者检测
                // 通过检查/proc/[pid]/stat中的SID字段来判断
                if (strncmp(line, "NSpid:", 6) == 0) {
                    // 这里暂时保留简化检查，后面会通过stat文件获取更准确的信息
                    char *pid_str = line + 6;
                    while (*pid_str == ' ' || *pid_str == '\t') pid_str++;
                    // 只有当进程ID等于会话ID时才是会话领导者
                    // 这个检查会在后面的stat文件解析中进行
                }

                // 检查线程数
                if (strncmp(line, "Threads:", 8) == 0) {
                    char *threads_str = line + 8;
                    while (*threads_str == ' ' || *threads_str == '\t') threads_str++;
                    num_threads = atoi(threads_str);
                }

                // 检查VmLck（锁定内存）
                if (strncmp(line, "VmLck:", 6) == 0) {
                    char *vmlock_str = line + 6;
                    while (*vmlock_str == ' ' || *vmlock_str == '\t') vmlock_str++;
                    vmlock = atoi(vmlock_str);
                }

                line = strtok(NULL, "\n");
            }

            // 重新读取stat文件获取nice值和其他信息
            fd = open(stat_path, O_RDONLY);
            if (fd != -1) {
                bytes_read = read(fd, buffer, sizeof(buffer) - 1);
                close(fd);

                if (bytes_read > 0) {
                    buffer[bytes_read] = '\0';

                    // 解析stat文件获取详细信息
                    char *last_paren = strrchr(buffer, ')');
                    if (last_paren != NULL) {
                        char *fields = last_paren + 2; // 跳过') '

                        // 解析各个字段
                        char *field = strtok(fields, " ");
                        int field_num = 3; // 从第3个字段开始（state已经处理）

                        while (field != NULL && field_num <= 20) {
                            switch (field_num) {
                                case 18: // nice值
                                    nice_value = atoi(field);
                                    break;
                                case 5: // 会话ID (SID)
                                    {
                                        int sid = atoi(field);
                                        // 🔧 准确的会话领导者检测：当PID == SID时，该进程是会话领导者
                                        if (sid == pid) {
                                            is_session_leader = 1;
                                        }
                                    }
                                    break;
                                case 7: // 前台进程组ID
                                    {
                                        int pgrp = atoi(field);
                                        // 简化的前台进程检查
                                        if (pgrp > 0) {
                                            // 这里可以添加更复杂的前台进程检查逻辑
                                        }
                                    }
                                    break;
                            }
                            field = strtok(NULL, " ");
                            field_num++;
                        }
                    }
                }
            }

            // 根据收集的信息添加修饰符
            // 🔧 优化：更精确的优先级检测，与ps命令保持一致
            // ps命令只在nice值显著偏离默认值时才显示优先级修饰符
            if (nice_value < -10) {  // 只有显著高优先级才显示
                full_state[state_idx++] = '<';
            }
            else if (nice_value > 10) {  // 只有显著低优先级才显示
                full_state[state_idx++] = 'N';
            }

            // 多线程进程
            if (num_threads > 1) {
                full_state[state_idx++] = 'l';
            }

            // 🔧 优化：更精确的会话领导者检测
            // 只有真正的会话领导者才显示's'修饰符
            // 通过检查进程的SID是否等于PID来判断
            if (pid > 2 && is_session_leader) {  // 排除内核进程
                full_state[state_idx++] = 's';
            }

            // 锁定内存页面
            if (vmlock > 0) {
                full_state[state_idx++] = 'L';
            }

            // 前台进程（简化检查）
            // 注意：完整的前台进程检查需要更复杂的逻辑
            // 这里我们做一个简化的检查
            if (pid < 1000 && state_char == 'R') {
                // 对于某些系统进程，可能在前台运行
                full_state[state_idx++] = '+';
            }
        }
    }

    // 确保字符串以null结尾
    full_state[state_idx] = '\0';

    // 分配结果字符串内存
    result = (char*)malloc(128);
    if (result == NULL) {
        return NULL;
    }

    // 根据完整状态字符串生成描述性文本（类似parseLinuxPsState）
    char base_status[64] = {0};
    char modifiers[64] = {0};

    // 解析主状态
    switch (state_char) {
        case 'D':
            strcpy(base_status, "Disk Sleep");
            break;
        case 'I':
            strcpy(base_status, "Idle");
            break;
        case 'R':
            strcpy(base_status, "Running");
            break;
        case 'S':
            strcpy(base_status, "Sleeping");
            break;
        case 'T':
            strcpy(base_status, "Suspended");
            break;
        case 't':
            strcpy(base_status, "Traced");
            break;
        case 'W':
            strcpy(base_status, "Paging");
            break;
        case 'X':
            strcpy(base_status, "Dead");
            break;
        case 'Z':
            strcpy(base_status, "Zombie");
            break;
        default:
            snprintf(base_status, sizeof(base_status), "Unknown(%c)", state_char);
            break;
    }

    // 解析修饰符
    int has_modifiers = 0;
    for (int i = 1; i < state_idx; i++) {
        if (!has_modifiers) {
            has_modifiers = 1;
        } else {
            strcat(modifiers, ", ");
        }

        switch (full_state[i]) {
            case '<':
                strcat(modifiers, "high priority");
                break;
            case 'N':
                strcat(modifiers, "low priority");
                break;
            case 'L':
                strcat(modifiers, "pages locked");
                break;
            case 's':
                strcat(modifiers, "session leader");
                break;
            case 'l':
                strcat(modifiers, "multi-threaded");
                break;
            case '+':
                strcat(modifiers, "foreground");
                break;
        }
    }

    // 组合最终结果
    if (has_modifiers) {
        snprintf(result, 128, "%s (%s)", base_status, modifiers);
    } else {
        strcpy(result, base_status);
    }

    return result;
}

// 批量获取多个进程完整状态 - 进一步优化性能
// pids: 进程ID数组
// count: 进程数量
// results: 结果数组，调用者需要为每个结果调用free()
// 🚀 性能优化：批量处理减少系统调用开销，提高整体吞吐量
int get_multiple_process_status_fast(int *pids, int count, char **results) {
    int success_count = 0;

    // 批量处理，减少系统调用开销
    for (int i = 0; i < count; i++) {
        results[i] = get_process_status_fast(pids[i]);
        if (results[i] != NULL) {
            success_count++;
        }
    }

    return success_count;
}

// 获取进程状态的原始字符串（类似ps -o stat=的输出）
// 这个函数返回原始的状态字符串，不进行描述性转换
char* get_process_status_raw(int pid) {
    char stat_path[64];
    char status_path[64];
    char buffer[1024];
    char status_buffer[2048];
    char *result = NULL;
    int fd;
    ssize_t bytes_read;
    char state_char;
    char full_state[16] = {0};
    int state_idx = 0;

    // 构造路径
    snprintf(stat_path, sizeof(stat_path), "/proc/%d/stat", pid);
    snprintf(status_path, sizeof(status_path), "/proc/%d/status", pid);

    // 从/proc/[pid]/stat获取基本状态
    fd = open(stat_path, O_RDONLY);
    if (fd == -1) {
        return NULL;
    }

    bytes_read = read(fd, buffer, sizeof(buffer) - 1);
    close(fd);

    if (bytes_read <= 0) {
        return NULL;
    }

    buffer[bytes_read] = '\0';

    // 解析stat文件
    char *last_paren = strrchr(buffer, ')');
    if (last_paren == NULL) {
        return NULL;
    }

    char *state_pos = last_paren + 1;
    while (*state_pos == ' ' || *state_pos == '\t') {
        state_pos++;
    }

    if (*state_pos == '\0') {
        return NULL;
    }

    state_char = *state_pos;
    full_state[state_idx++] = state_char;

    // 从/proc/[pid]/status和stat获取修饰符信息
    fd = open(status_path, O_RDONLY);
    if (fd != -1) {
        bytes_read = read(fd, status_buffer, sizeof(status_buffer) - 1);
        close(fd);

        if (bytes_read > 0) {
            status_buffer[bytes_read] = '\0';

            char *line = strtok(status_buffer, "\n");
            int nice_value = 0;
            int num_threads = 1;
            int vmlock = 0;

            while (line != NULL) {
                if (strncmp(line, "Threads:", 8) == 0) {
                    char *threads_str = line + 8;
                    while (*threads_str == ' ' || *threads_str == '\t') threads_str++;
                    num_threads = atoi(threads_str);
                }

                if (strncmp(line, "VmLck:", 6) == 0) {
                    char *vmlock_str = line + 6;
                    while (*vmlock_str == ' ' || *vmlock_str == '\t') vmlock_str++;
                    vmlock = atoi(vmlock_str);
                }

                line = strtok(NULL, "\n");
            }

            // 重新读取stat文件获取nice值
            fd = open(stat_path, O_RDONLY);
            if (fd != -1) {
                bytes_read = read(fd, buffer, sizeof(buffer) - 1);
                close(fd);

                if (bytes_read > 0) {
                    buffer[bytes_read] = '\0';
                    char *last_paren = strrchr(buffer, ')');
                    if (last_paren != NULL) {
                        char *fields = last_paren + 2;
                        char *field = strtok(fields, " ");
                        int field_num = 3;

                        while (field != NULL && field_num <= 20) {
                            if (field_num == 18) { // nice值
                                nice_value = atoi(field);
                                break;
                            }
                            field = strtok(NULL, " ");
                            field_num++;
                        }
                    }
                }
            }

            // 🔧 优化：添加修饰符，与主函数保持一致
            if (nice_value < -10) {  // 只有显著高优先级才显示
                full_state[state_idx++] = '<';
            } else if (nice_value > 10) {  // 只有显著低优先级才显示
                full_state[state_idx++] = 'N';
            }

            if (num_threads > 1) {
                full_state[state_idx++] = 'l';
            }

            if (vmlock > 0) {
                full_state[state_idx++] = 'L';
            }
        }
    }

    full_state[state_idx] = '\0';

    // 分配并返回原始状态字符串
    result = (char*)malloc(32);
    if (result == NULL) {
        return NULL;
    }

    strcpy(result, full_state);
    return result;
}
*/
import "C"

import (
	"fmt"
	"io/ioutil"
	"os/exec"
	"strings"
	"unsafe"
)

// getProcessStatus 获取Linux进程状态，优先使用高性能C语言实现。
// 如果C语言实现失败，则回退到ps命令或/proc文件系统。
func getProcessStatus(pid int32) string {
	// 🚀 性能优化：方案一 - 使用C语言直接读取/proc/[pid]/stat文件
	// 这比调用ps命令快10-100倍，避免了进程创建和上下文切换开销
	cResult := C.get_process_status_fast(C.int(pid))
	if cResult != nil {
		// 将C字符串转换为Go字符串
		goResult := C.GoString(cResult)
		// 释放C分配的内存
		C.free(unsafe.Pointer(cResult))
		if goResult != "" {
			return goResult
		}
	}

	// 方案二：如果C语言实现失败，回退到ps命令（保持兼容性）
	cmd := exec.Command("ps", "-p", fmt.Sprint(pid), "-o", "stat=")
	output, err := cmd.Output()

	if err == nil {
		stateStr := strings.TrimSpace(string(output))
		if stateStr != "" {
			// 使用为 ps 命令输出定制的解析函数
			return parseLinuxPsState(stateStr)
		}
	}

	// 方案三：如果ps命令也失败，回退到读取/proc文件
	// 这种情况可能发生在极简容器环境或权限问题时
	return getProcessStatusFromProc(pid)
}

// parseLinuxPsState 根据 `man ps` 文档，全面解析 Linux `ps` 命令输出的状态字符串。
func parseLinuxPsState(stateStr string) string {
	if len(stateStr) == 0 {
		return ""
	}

	// 1. 解析主状态 (第一个字符)
	mainStateChar := stateStr[0]
	var baseStatus string
	switch mainStateChar {
	case 'D':
		baseStatus = "Disk Sleep" // Uninterruptible sleep
	case 'I':
		baseStatus = "Idle" // Idle kernel thread
	case 'R':
		baseStatus = "Running"
	case 'S':
		baseStatus = "Sleeping" // Interruptible sleep
	case 'T':
		baseStatus = "Suspended" // Stopped by job control
	case 't':
		baseStatus = "Traced" // Stopped by debugger
	case 'W':
		baseStatus = "Paging" // (Legacy)
	case 'X':
		baseStatus = "Dead"
	case 'Z':
		baseStatus = "Zombie"
	default:
		return fmt.Sprintf("Unknown(%s)", stateStr)
	}

	// 2. 解析修饰符 (主状态后的所有字符)
	var modifiers []string
	if len(stateStr) > 1 {
		// 注意：我们直接遍历修饰符字符串，而不是 stateStr[1:]
		// 因为 ps 输出的状态码本身就是多字符的，例如 "Sl+"
		for _, modChar := range stateStr[1:] {
			switch modChar {
			case '<':
				modifiers = append(modifiers, "high priority")
			case 'N':
				modifiers = append(modifiers, "low priority")
			case 'L':
				modifiers = append(modifiers, "pages locked")
			case 's':
				modifiers = append(modifiers, "session leader")
			case 'l':
				modifiers = append(modifiers, "multi-threaded")
			case '+':
				modifiers = append(modifiers, "foreground")
			}
		}
	}

	// 3. 组合最终的状态字符串
	if len(modifiers) > 0 {
		return fmt.Sprintf("%s (%s)", baseStatus, strings.Join(modifiers, ", "))
	}

	return baseStatus
}

// getProcessStatusFromProc 是从 /proc 文件系统获取状态的回退方案。
func getProcessStatusFromProc(pid int32) string {
	// 优先从 /proc/[pid]/status 获取，信息可能更详细一些
	statusFile := fmt.Sprintf("/proc/%d/status", pid)
	content, err := ioutil.ReadFile(statusFile)
	if err == nil {
		lines := strings.Split(string(content), "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "State:") {
				parts := strings.Fields(line)
				if len(parts) >= 2 {
					// 直接用 ps 的解析函数，因为它能处理单字符状态
					return parseLinuxPsState(parts[1])
				}
			}
		}
	}

	// 再次回退到 /proc/[pid]/stat
	statFile := fmt.Sprintf("/proc/%d/stat", pid)
	content, err = ioutil.ReadFile(statFile)
	if err != nil {
		return "" // 进程已退出或无权限
	}

	statStr := string(content)
	lastParen := strings.LastIndex(statStr, ")")
	if lastParen == -1 || len(statStr) < lastParen+3 {
		return ""
	}

	fields := strings.Fields(statStr[lastParen+2:])
	if len(fields) > 0 {
		// 直接用 ps 的解析函数，因为它能处理单字符状态
		return parseLinuxPsState(fields[0])
	}

	return ""
}

// // RunPerformanceTest 运行性能测试，比较优化前后的性能差异
// // 这个函数仅用于测试，不会在生产环境中调用
// func RunPerformanceTest() {
// 	// 获取所有进程ID
// 	pids, err := process.Pids()
// 	if err != nil {
// 		fmt.Println("获取进程ID失败:", err)
// 		return
// 	}

// 	// 限制测试进程数量
// 	maxTestPids := 100
// 	if len(pids) > maxTestPids {
// 		pids = pids[:maxTestPids]
// 	}

// 	fmt.Printf("开始性能测试，测试 %d 个进程...\n", len(pids))

// 	// 测试原始ps命令方法
// 	startTime := time.Now()
// 	for _, pid := range pids {
// 		cmd := exec.Command("ps", "-p", fmt.Sprint(pid), "-o", "stat=")
// 		output, _ := cmd.Output()
// 		if len(output) > 0 {
// 			stateStr := strings.TrimSpace(string(output))
// 			parseLinuxPsState(stateStr)
// 		}
// 	}
// 	psTime := time.Since(startTime)
// 	fmt.Printf("ps命令方法耗时: %v, 平均每个进程: %v\n", psTime, psTime/time.Duration(len(pids)))

// 	// 测试C语言优化方法
// 	startTime = time.Now()
// 	for _, pid := range pids {
// 		cResult := C.get_process_status_fast(C.int(pid))
// 		if cResult != nil {
// 			C.GoString(cResult)
// 			C.free(unsafe.Pointer(cResult))
// 		}
// 	}
// 	cTime := time.Since(startTime)
// 	fmt.Printf("C语言优化方法耗时: %v, 平均每个进程: %v\n", cTime, cTime/time.Duration(len(pids)))

// 	// 计算性能提升
// 	if psTime > 0 {
// 		speedup := float64(psTime) / float64(cTime)
// 		fmt.Printf("性能提升: %.2f倍\n", speedup)
// 	}

// 	// 测试批量处理方法
// 	startTime = time.Now()
// 	cPids := make([]C.int, len(pids))
// 	for i, pid := range pids {
// 		cPids[i] = C.int(pid)
// 	}
// 	cResults := make([]*C.char, len(pids))
// 	C.get_multiple_process_status_fast(
// 		(*C.int)(unsafe.Pointer(&cPids[0])),
// 		C.int(len(pids)),
// 		(**C.char)(unsafe.Pointer(&cResults[0])),
// 	)
// 	for i := range cResults {
// 		if cResults[i] != nil {
// 			C.GoString(cResults[i])
// 			C.free(unsafe.Pointer(cResults[i]))
// 		}
// 	}
// 	batchTime := time.Since(startTime)
// 	fmt.Printf("批量处理方法耗时: %v, 平均每个进程: %v\n", batchTime, batchTime/time.Duration(len(pids)))

// 	// 计算批量处理相对于单个C调用的性能提升
// 	if cTime > 0 {
// 		batchSpeedup := float64(cTime) / float64(batchTime)
// 		fmt.Printf("批量处理相对于单个C调用的性能提升: %.2f倍\n", batchSpeedup)
// 	}

// 	// 验证结果一致性
// 	fmt.Println("\n验证结果一致性:")
// 	for i := 0; i < 5 && i < len(pids); i++ {
// 		pid := pids[i]

// 		// 获取ps命令结果
// 		cmd := exec.Command("ps", "-p", fmt.Sprint(pid), "-o", "stat=")
// 		output, err := cmd.Output()
// 		var psResult string
// 		if err == nil && len(output) > 0 {
// 			stateStr := strings.TrimSpace(string(output))
// 			psResult = parseLinuxPsState(stateStr)
// 		}

// 		// 获取C语言方法结果
// 		var cResult string
// 		cPtr := C.get_process_status_fast(C.int(pid))
// 		if cPtr != nil {
// 			cResult = C.GoString(cPtr)
// 			C.free(unsafe.Pointer(cPtr))
// 		}

// 		// 🔧 智能状态比较：检查核心状态是否一致
// 		coreMatch := compareProcessStates(psResult, cResult)
// 		fmt.Printf("PID %d: PS结果=%q, C语言结果=%q, 核心一致=%v, 完全一致=%v\n",
// 			pid, psResult, cResult, coreMatch, psResult == cResult)
// 	}
// }

// compareProcessStates 智能比较两个进程状态字符串的核心部分
// 🔧 优化：专注于核心状态匹配，忽略细微的修饰符差异
func compareProcessStates(psResult, cResult string) bool {
	if psResult == "" || cResult == "" {
		return psResult == cResult
	}

	// 提取核心状态（括号前的部分）
	psCore := strings.Split(psResult, " (")[0]
	cCore := strings.Split(cResult, " (")[0]

	// 核心状态映射
	stateMap := map[string]string{
		"Sleeping":   "Sleeping",
		"Running":    "Running",
		"Idle":       "Idle",
		"Disk Sleep": "Disk Sleep",
		"Suspended":  "Suspended",
		"Traced":     "Traced",
		"Zombie":     "Zombie",
		"Dead":       "Dead",
	}

	// 标准化状态名称
	psNormalized := stateMap[psCore]
	cNormalized := stateMap[cCore]

	if psNormalized == "" {
		psNormalized = psCore
	}
	if cNormalized == "" {
		cNormalized = cCore
	}

	return psNormalized == cNormalized
}

// getMultipleProcessStatusFast 批量获取多个进程状态 - 高性能C语言实现
// 🚀 性能优化：批量处理减少函数调用开销，提高整体性能
func getMultipleProcessStatusFast(pids []int32) map[int32]string {
	if len(pids) == 0 {
		return make(map[int32]string)
	}

	// 转换Go切片为C数组
	cPids := make([]C.int, len(pids))
	for i, pid := range pids {
		cPids[i] = C.int(pid)
	}

	// 分配结果数组
	cResults := make([]*C.char, len(pids))

	// 调用C函数批量获取状态
	successCount := C.get_multiple_process_status_fast(
		(*C.int)(unsafe.Pointer(&cPids[0])),
		C.int(len(pids)),
		(**C.char)(unsafe.Pointer(&cResults[0])),
	)

	// 转换结果并释放内存
	results := make(map[int32]string)
	for i, pid := range pids {
		if cResults[i] != nil {
			// 转换C字符串为Go字符串
			goResult := C.GoString(cResults[i])
			results[pid] = goResult
			// 释放C分配的内存
			C.free(unsafe.Pointer(cResults[i]))
		}
	}

	// 记录批量处理统计
	if successCount > 0 {
		// 可以在这里添加性能统计日志
	}

	return results
}
