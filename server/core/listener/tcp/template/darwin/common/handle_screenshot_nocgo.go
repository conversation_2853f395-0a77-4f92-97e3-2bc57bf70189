//go:build darwin && !cgo
// +build darwin,!cgo

package common

import (
	"fmt"
	"log"
	"os/exec"
	"time"
)

// ScreenshotRequest 截图请求
type ScreenshotRequest struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Type         int    `json:"type"`          // 截图类型: 0=全屏, 1=活动窗口, 2=指定区域
	MonitorID    int    `json:"monitor_id"`    // 显示器ID (多显示器支持)
	MonitorIndex int    `json:"monitor_index"` // 显示器索引 (前端兼容)
	X            int    `json:"x"`             // 区域截图的X坐标
	Y            int    `json:"y"`             // 区域截图的Y坐标
	Width        int    `json:"width"`         // 区域截图的宽度
	Height       int    `json:"height"`        // 区域截图的高度
	Format       string `json:"format"`        // 图片格式: "png", "jpeg", "bmp"
	Quality      int    `json:"quality"`       // JPEG质量 (1-100)
}

// ScreenshotResponse 截图响应
type ScreenshotResponse struct {
	TaskID    uint64 `json:"task_id"`    // 任务ID
	Success   bool   `json:"success"`    // 是否成功
	Error     string `json:"error"`      // 错误信息
	ImageData []byte `json:"image_data"` // 图片数据
	Width     int    `json:"width"`      // 图片宽度
	Height    int    `json:"height"`     // 图片高度
	Format    string `json:"format"`     // 图片格式
	Size      int64  `json:"size"`       // 文件大小
	Timestamp int64  `json:"timestamp"`  // 截图时间戳
}

// 处理截图请求的主函数（非CGO版本，使用命令行工具）
func (cm *ConnectionManager) handleScreenshotRequest(packet *Packet) {
	// 完全模仿Linux stub版本的switch逻辑
	switch packet.Header.Code {
	case Pic:
		cm.handleScreenshot(packet)
	case StreamStart:
		cm.handleScreenStreamStart(packet)
	case StreamStop:
		cm.handleScreenStreamStop(packet)
	case StreamData:
		cm.handleScreenStream(packet)
	case MonitorList:
		cm.handleMonitorList(packet)
	default:
		log.Printf("未知的截图操作代码: %d", packet.Header.Code)
	}
}

// handleScreenshot 处理单次截图请求（Darwin命令行版本）
func (cm *ConnectionManager) handleScreenshot(packet *Packet) {
	// 完全模仿Linux stub版本的逻辑
	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析截图请求失败: %v", err)
		return
	}

	log.Printf("🖼️ 开始处理截图请求 - 类型: %d, 格式: %s, 质量: %d", req.Type, req.Format, req.Quality)

	// 使用captureScreenDarwin函数
	imageData, width, height, err := cm.captureScreenDarwin(&req)
	if err != nil {
		log.Printf("❌ 截图失败: %v", err)
		errorResp := ScreenshotResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "截图失败: " + err.Error(),
		}
		cm.sendResp(Screenshot, Pic, errorResp)
		return
	}

	// 发送成功响应
	cm.sendResp(Screenshot, Pic, ScreenshotResponse{
		TaskID:    req.TaskID,
		Success:   true,
		ImageData: imageData,
		Width:     width,
		Height:    height,
		Format:    req.Format,
		Size:      int64(len(imageData)),
		Timestamp: time.Now().Unix(),
	})

	log.Printf("✅ 截图完成 - 尺寸: %dx%d, 大小: %d bytes", width, height, len(imageData))
}

// 简化的流状态管理
var darwinStreamState struct {
	isRunning   bool
	stopChannel chan bool
	taskID      uint64
	// 性能统计
	frameCount       uint64
	startTime        time.Time
	totalCaptureTime time.Duration
	totalSendTime    time.Duration
}

// handleScreenStreamStart 处理视频流开始请求（Darwin命令行版本）
func (cm *ConnectionManager) handleScreenStreamStart(packet *Packet) {
	log.Printf("📹 视频流开始请求（Darwin命令行版本）")

	// 解析请求
	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流启动请求失败: %v", err)
		return
	}

	// 如果已经在运行，先停止
	if darwinStreamState.isRunning {
		darwinStreamState.stopChannel <- true
		time.Sleep(100 * time.Millisecond)
	}

	// 初始化流状态和统计
	darwinStreamState.isRunning = true
	darwinStreamState.stopChannel = make(chan bool, 1)
	darwinStreamState.taskID = req.TaskID
	darwinStreamState.frameCount = 0
	darwinStreamState.startTime = time.Now()
	darwinStreamState.totalCaptureTime = 0
	darwinStreamState.totalSendTime = 0

	// 启动流数据发送goroutine
	go cm.runScreenStreamDarwin(req.TaskID)

	log.Printf("🎥 屏幕流已启动（Darwin命令行版本）- TaskID: %d", req.TaskID)

	// 发送开始成功响应
	response := map[string]interface{}{
		"task_id":   req.TaskID,
		"success":   true,
		"stream_id": fmt.Sprintf("stream_%d_%d", req.TaskID, time.Now().Unix()),
		"error":     "",
	}
	cm.sendResp(Screenshot, StreamStart, response)
}

// handleScreenStreamStop 处理视频流停止请求（Darwin命令行版本）
func (cm *ConnectionManager) handleScreenStreamStop(packet *Packet) {
	log.Printf("⏹️ 视频流停止请求")

	// 解析请求
	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流停止请求失败: %v", err)
		return
	}

	// 停止流并输出统计
	if darwinStreamState.isRunning {
		darwinStreamState.stopChannel <- true
		darwinStreamState.isRunning = false

		// 🛑 完全模仿Linux版本的最终统计日志
		duration := time.Since(darwinStreamState.startTime)
		avgFPS := float64(darwinStreamState.frameCount) / duration.Seconds()
		log.Printf("🛑 屏幕流线程退出 - 总帧数: %d, 运行时长: %.2fs, 平均帧率: %.2f fps",
			darwinStreamState.frameCount, duration.Seconds(), avgFPS)

		// 📊 详细性能统计
		if darwinStreamState.frameCount > 0 {
			log.Printf("📊 性能统计详情:")
			log.Printf("   截图总耗时: %v (平均: %v/帧)",
				darwinStreamState.totalCaptureTime,
				darwinStreamState.totalCaptureTime/time.Duration(darwinStreamState.frameCount))
			log.Printf("   发送总耗时: %v (平均: %v/帧)",
				darwinStreamState.totalSendTime,
				darwinStreamState.totalSendTime/time.Duration(darwinStreamState.frameCount))

			// 计算各步骤占比
			totalProcessTime := darwinStreamState.totalCaptureTime + darwinStreamState.totalSendTime
			if totalProcessTime > 0 {
				log.Printf("📈 各步骤耗时占比:")
				log.Printf("   截图: %.1f%%", float64(darwinStreamState.totalCaptureTime)/float64(totalProcessTime)*100)
				log.Printf("   发送: %.1f%%", float64(darwinStreamState.totalSendTime)/float64(totalProcessTime)*100)
			}
		}

		log.Printf("🛑 屏幕流已停止")
	} else {
		log.Printf("⚠️ 屏幕流未运行")
	}

	// 发送停止成功响应
	response := map[string]interface{}{
		"task_id": req.TaskID,
		"success": true,
		"error":   "",
	}
	cm.sendResp(Screenshot, StreamStop, response)
}

// handleScreenStream 处理视频流数据请求（Darwin命令行版本）
func (cm *ConnectionManager) handleScreenStream(packet *Packet) {
	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流请求失败: %v", err)
		return
	}

	// 模仿Linux版本：如果流未运行，发送单张截图
	imageData, width, height, err := cm.captureScreenDarwin(&req)
	if err != nil {
		log.Printf("❌ 屏幕流截图失败: %v", err)
		return
	}

	// 完全模仿Linux版本的响应格式
	cm.sendResp(Screenshot, Pic, ScreenshotResponse{
		TaskID:    req.TaskID,
		Success:   true,
		ImageData: imageData,
		Width:     width,
		Height:    height,
		Format:    req.Format,
		Size:      int64(len(imageData)),
		Timestamp: time.Now().Unix(),
	})
}

// handleMonitorList 处理显示器列表请求（Darwin命令行版本）
func (cm *ConnectionManager) handleMonitorList(packet *Packet) {
	log.Printf("🖥️ 显示器列表请求")

	// 解析请求
	var taskID uint64 = 0
	if len(packet.PacketData.Data) > 0 {
		var req map[string]interface{}
		if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err == nil {
			if id, ok := req["task_id"].(float64); ok {
				taskID = uint64(id)
			}
		}
	}

	// 简化的显示器信息
	monitors := []map[string]interface{}{
		{
			"id":     0,
			"name":   "主显示器",
			"width":  1920,
			"height": 1080,
			"x":      0,
			"y":      0,
		},
	}

	response := map[string]interface{}{
		"task_id":  taskID,
		"success":  true,
		"monitors": monitors,
		"count":    1,
	}
	cm.sendResp(Screenshot, MonitorList, response)
}

// captureScreenDarwin 使用macOS命令行工具进行截图
func (cm *ConnectionManager) captureScreenDarwin(req *ScreenshotRequest) ([]byte, int, int, error) {
	log.Printf("🔍 Darwin截图方法检测 - 请求类型: %d, 格式: %s, 质量: %d", req.Type, req.Format, req.Quality)

	var output []byte
	var err error

	// macOS截图命令优先级：screencapture > sips
	log.Printf("📸 尝试方法1: screencapture命令截图")

	// 使用screencapture命令，输出到stdout
	// -x: 不播放截图声音
	// -t: 指定格式 (png, jpg, pdf, tiff)
	// -: 输出到stdout
	format := "png"
	if req.Format == "jpeg" || req.Format == "jpg" {
		format = "jpg"
	}

	cmd := exec.Command("screencapture", "-x", "-t", format, "-")
	output, err = cmd.Output()
	if err != nil {
		log.Printf("❌ 方法1失败: screencapture命令不可用 - %v", err)

		// 回退方案：尝试使用system_profiler获取显示信息，然后用其他方法
		log.Printf("📸 尝试方法2: 使用备用截图方案")
		return nil, 0, 0, fmt.Errorf("Darwin截图命令执行失败，请确保screencapture命令可用: %v", err)
	} else {
		log.Printf("✅ 方法1成功: screencapture命令截图")
	}

	// 返回默认分辨率（实际应该解析图片获取真实尺寸）
	width := 1920
	height := 1080

	log.Printf("✅ Darwin截图完成 - 尺寸: %dx%d, 大小: %d bytes", width, height, len(output))
	return output, width, height, nil
}

// runScreenStreamDarwin 运行Darwin屏幕流（优化版本）
func (cm *ConnectionManager) runScreenStreamDarwin(taskID uint64) {
	log.Printf("🎬 开始运行Darwin屏幕流 - TaskID: %d", taskID)

	ticker := time.NewTicker(66 * time.Millisecond) // 15 FPS
	defer ticker.Stop()

	log.Printf("🔧 使用Darwin截图工具: screencapture")

	// 🕒 性能计时统计（完全模仿Linux版本）
	defer func() {
		// 确保在退出时输出最终统计
		if darwinStreamState.frameCount > 0 {
			duration := time.Since(darwinStreamState.startTime)
			avgFPS := float64(darwinStreamState.frameCount) / duration.Seconds()
			log.Printf("🛑 Darwin屏幕流线程退出 - 总帧数: %d, 运行时长: %.2fs, 平均帧率: %.2f fps",
				darwinStreamState.frameCount, duration.Seconds(), avgFPS)
		}
	}()

	for {
		select {
		case <-darwinStreamState.stopChannel:
			log.Printf("🛑 收到停止信号，退出Darwin屏幕流")
			return
		case <-ticker.C:
			// 🕒 帧开始时间
			frameStart := time.Now()
			darwinStreamState.frameCount++

			// 🕒 截图计时
			captureStart := time.Now()
			imageData, width, height, err := cm.fastCaptureScreenDarwin()
			captureTime := time.Since(captureStart)
			darwinStreamState.totalCaptureTime += captureTime

			if err != nil {
				log.Printf("❌ Darwin流截图失败: %v", err)
				continue
			}

			// 🕒 发送计时
			sendStart := time.Now()
			response := map[string]interface{}{
				"task_id":    taskID,
				"success":    true,
				"stream_id":  fmt.Sprintf("stream_%d", taskID),
				"frame_data": imageData,
				"width":      width,
				"height":     height,
				"format":     "jpeg",
				"size":       len(imageData),
				"frame":      darwinStreamState.frameCount,
			}

			cm.sendResp(Screenshot, StreamData, response)
			sendTime := time.Since(sendStart)
			darwinStreamState.totalSendTime += sendTime

			// 🕒 总体统计
			totalFrameTime := time.Since(frameStart)

			// 只在前10帧显示详细日志，之后每50帧显示一次
			if darwinStreamState.frameCount <= 10 || darwinStreamState.frameCount%50 == 0 {
				log.Printf("✅ Darwin帧 #%d 处理完成 - 总耗时: %v (截图: %v, 发送: %v), 大小: %d bytes",
					darwinStreamState.frameCount, totalFrameTime, captureTime, sendTime, len(imageData))
			}

			// 每10帧输出一次平均性能统计（完全模仿Linux版本）
			if darwinStreamState.frameCount%10 == 0 {
				avgCaptureTime := darwinStreamState.totalCaptureTime / time.Duration(darwinStreamState.frameCount)
				avgSendTime := darwinStreamState.totalSendTime / time.Duration(darwinStreamState.frameCount)
				currentDuration := time.Since(darwinStreamState.startTime)
				currentFPS := float64(darwinStreamState.frameCount) / currentDuration.Seconds()

				log.Printf("📊 Darwin最近10帧平均性能 - 截图: %v, 发送: %v, 当前FPS: %.2f",
					avgCaptureTime, avgSendTime, currentFPS)
			}
		}
	}
}

// fastCaptureScreenDarwin 快速Darwin截图（优化版本）
func (cm *ConnectionManager) fastCaptureScreenDarwin() ([]byte, int, int, error) {
	// 使用screencapture命令，优化参数以提高速度
	// -x: 不播放截图声音
	// -t jpg: 使用JPEG格式（比PNG更快）
	// -: 输出到stdout
	cmd := exec.Command("screencapture", "-x", "-t", "jpg", "-")

	output, err := cmd.Output()
	if err != nil {
		return nil, 0, 0, err
	}

	// 返回原始分辨率（实际应该解析图片获取真实尺寸）
	width := 1920  // 原始分辨率
	height := 1080 // 原始分辨率

	return output, width, height, nil
}
