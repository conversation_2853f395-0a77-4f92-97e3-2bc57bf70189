package tcp

import (
	"bufio"
	"fmt"
	"net"
	"net/http"
)

// HijackResponseWriter 实现http.ResponseWriter接口，将响应写回到TCP连接
type HijackResponseWriter struct {
	conn       net.Conn
	header     http.Header
	statuscode int
	bufWriter  *bufio.Writer
}

// 创建一个新的HijackResponseWriter
func newHijackResponseWriter(conn net.Conn) *HijackResponseWriter {
	return &HijackResponseWriter{
		conn:       conn,
		header:     make(http.Header),
		statuscode: 0, // 初始化为0，表示还没有写入过头部
		bufWriter:  bufio.NewWriter(conn),
	}
}

// Header 返回响应头
func (w *HijackResponseWriter) Header() http.Header {
	return w.header
}

// Write 将数据写入连接
func (w *HijackResponseWriter) Write(data []byte) (int, error) {
	// 如果还没有写入响应头，先写入
	if w.statuscode == 0 {
		w.WriteHeader(http.StatusOK)
		w.statuscode = 0 // 重置状态码，表示头部已经写入
	}
	n, err := w.bufWriter.Write(data)
	if err != nil {
		return n, err
	}
	// 只在写入完成后刷新缓冲区，而不是每次写入都刷新
	err = w.bufWriter.Flush()
	return n, err
}

// WriteHeader 写入响应状态码和头部
func (w *HijackResponseWriter) WriteHeader(statusCode int) {
	// 如果已经写入过头部，不再写入
	if w.statuscode == 0 {
		// 保存状态码，非零值表示已经写入过头部
		w.statuscode = statusCode

		// 写入响应行
		fmt.Fprintf(w.bufWriter, "HTTP/1.1 %d %s\r\n", statusCode, http.StatusText(statusCode))

		// 写入头部
		for key, values := range w.header {
			for _, value := range values {
				fmt.Fprintf(w.bufWriter, "%s: %s\r\n", key, value)
			}
		}

		// 写入空行，表示头部结束
		fmt.Fprintf(w.bufWriter, "\r\n")

		// 刷新缓冲区
		w.bufWriter.Flush()
	}
}
