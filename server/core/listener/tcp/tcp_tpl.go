package tcp

import (
	"embed"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"server/global"
	"strings"

	"go.uber.org/zap"
)

//go:embed template
var templateFS embed.FS

// getTemplateContent 从模板文件中读取内容
func getTemplateContent(filename string) (string, error) {
	content, err := templateFS.ReadFile("template/" + filename)
	if err != nil {
		return "", fmt.Errorf("读取模板文件 %s 失败: %v", filename, err)
	}
	return string(content), nil
}

// getDefaultNginxPage 获取默认nginx页面内容
func getDefaultNginxPage() string {
	content, err := getTemplateContent("nginx_page.html")
	if err != nil {
		global.LOG.Error("读取nginx页面模板失败: ", zap.Error(err))
		return "<html><body><h1>Welcome to nginx!</h1></body></html>" // fallback
	}
	return content
}

// getDefaultLinuxScriptTpl 获取Linux脚本模板
func getDefaultLinuxScriptTpl() string {
	content, err := getTemplateContent("linux/linux_script.sh")
	if err != nil {
		global.LOG.Error("读取Linux脚本模板失败: ", zap.Error(err))
		return "#!/bin/bash\necho 'Template not found'" // fallback
	}
	return content
}

// getDefaultWindowsScriptTpl 获取Windows脚本模板
func getDefaultWindowsScriptTpl() string {
	content, err := getTemplateContent("windows/windows_script.bat")
	if err != nil {
		global.LOG.Error("读取Windows脚本模板失败: ", zap.Error(err))
		return "@echo off\necho Template not found" // fallback
	}
	return content
}

// 删除了未使用的模板函数，现在使用extractClientCodeToTemp

// getDefaultDarwinScriptTpl 获取Darwin脚本模板
func getDefaultDarwinScriptTpl() string {
	content, err := getTemplateContent("darwin/darwin_script.sh")
	if err != nil {
		global.LOG.Error("读取Darwin脚本模板失败: ", zap.Error(err))
		return "#!/bin/bash\necho 'Template not found'" // fallback
	}
	return content
}

// extractClientCodeToTemp 从嵌入的FS中提取完整的客户端代码到临时目录
func extractClientCodeToTemp(platform string, tempDir string) error {
	platformDir := "template/" + platform

	// 先检查目录是否存在
	entries, err := templateFS.ReadDir(platformDir)
	if err != nil {
		// 尝试列出template目录的内容来调试
		if rootEntries, rootErr := templateFS.ReadDir("template"); rootErr == nil {
			global.LOG.Error("template目录内容:")
			for _, entry := range rootEntries {
				global.LOG.Error(fmt.Sprintf("  - %s (isDir: %v)", entry.Name(), entry.IsDir()))
			}
		}
		return fmt.Errorf("平台目录 %s 不存在: %v", platformDir, err)
	}

	global.LOG.Info(fmt.Sprintf("找到平台目录 %s，包含 %d 个条目", platformDir, len(entries)))

	// 递归遍历嵌入的文件系统
	return fs.WalkDir(templateFS, platformDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 计算目标路径
		relPath := strings.TrimPrefix(path, platformDir+"/")
		if relPath == path {
			// 如果没有变化，说明是根目录
			return nil
		}
		targetPath := filepath.Join(tempDir, relPath)

		if d.IsDir() {
			// 创建目录
			return os.MkdirAll(targetPath, 0755)
		} else {
			// 复制文件
			content, err := templateFS.ReadFile(path)
			if err != nil {
				return fmt.Errorf("读取文件 %s 失败: %v", path, err)
			}

			// 确保目标目录存在
			if err := os.MkdirAll(filepath.Dir(targetPath), 0755); err != nil {
				return fmt.Errorf("创建目录 %s 失败: %v", filepath.Dir(targetPath), err)
			}

			// 写入文件
			return os.WriteFile(targetPath, content, 0644)
		}
	})
}

// 删除了调试函数，embed现在应该正常工作
