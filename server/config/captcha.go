package config

type Captcha struct {
	KeyLong            int  `mapstructure:"key-long" json:"key-long" yaml:"key-long"`                                     // 验证码长度
	ImgWidth           int  `mapstructure:"img-width" json:"img-width" yaml:"img-width"`                                  // 验证码宽度
	ImgHeight          int  `mapstructure:"img-height" json:"img-height" yaml:"img-height"`                               // 验证码高度
	OpenCaptcha        bool `mapstructure:"open-captcha" json:"open-captcha" yaml:"open-captcha"`                         // 是否开启验证码，true表示开启，false表示关闭
	MaxFailedAttempts  int  `mapstructure:"max-failed-attempts" json:"max-failed-attempts" yaml:"max-failed-attempts"`   // 最大登录失败次数，超过此次数将被暂时禁止登录
	OpenCaptchaTimeOut int  `mapstructure:"open-captcha-timeout" json:"open-captcha-timeout" yaml:"open-captcha-timeout"` // 防爆破验证码超时时间，单位：s(秒)
}
