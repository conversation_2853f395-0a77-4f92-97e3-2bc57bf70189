package factory

import (
	"errors"
	"server/core/manager/dbpool"
	"server/core/listener/pipe"
	"server/core/listener/tcp"
	"server/global"
	"server/model/sys"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func UpdateListenerFactory(listener *sys.Listener) {
	switch listener.Type {
	case "pipe":
		// 这里直接调用global.PipeManager的方法来启动或停止监听器
		go func() {
			// 获取更新后的监听器信息
			var updatedListener sys.Listener
			// 🚀 使用数据库连接池查询更新后的监听器信息
			if err := dbpool.ExecuteDBOperationAsyncAndWait("listener_update_query_pipe", func(db *gorm.DB) error {
				return db.Where("id = ?", listener.ID).First(&updatedListener).Error
			}); err != nil {
				global.LOG.Error("获取更新后的监听器信息失败", zap.Error(err))
				return
			}

			// 根据状态启动或停止pipe监听器
			if listener.Status == 1 {
				global.LOG.Info("启动pipe监听器", zap.Uint("id", listener.ID), zap.String("addr", updatedListener.LocalListenAddr))
				// 使用global包中的方法
				pipe.PipeManager.UpdatePipeListener(listener.ID, listener.Status)
			} else {
				global.LOG.Info("停止pipe监听器", zap.Uint("id", listener.ID), zap.String("addr", updatedListener.LocalListenAddr))
				// 使用global包中的方法
				pipe.PipeManager.UpdatePipeListener(listener.ID, listener.Status)
			}
		}()
	case "tcp":
		// 处理TCP监听器
		go func() {
			// 获取更新后的监听器信息
			var updatedListener sys.Listener
			// 🚀 使用数据库连接池查询更新后的监听器信息
			if err := dbpool.ExecuteDBOperationAsyncAndWait("listener_update_query_tcp", func(db *gorm.DB) error {
				return db.Where("id = ?", listener.ID).First(&updatedListener).Error
			}); err != nil {
				global.LOG.Error("获取更新后的监听器信息失败", zap.Error(err))
				return
			}

			// 根据状态启动或停止TCP监听器
			if listener.Status == 1 {
				global.LOG.Info("启动TCP监听器", zap.Uint("id", listener.ID), zap.String("addr", updatedListener.LocalListenAddr))
				// 启动TCP监听器
				if err := tcp.TCPManager.StartListener(updatedListener); err != nil {
					global.LOG.Error("启动TCP监听器失败", zap.Error(err))
				}
			} else {
				global.LOG.Info("停止TCP监听器", zap.Uint("id", listener.ID), zap.String("addr", updatedListener.LocalListenAddr))
				// 停止TCP监听器
				if err := tcp.TCPManager.StopListener(listener.ID); err != nil {
					global.LOG.Error("停止TCP监听器失败", zap.Error(err))
				}
			}
		}()
	default:
		global.LOG.Error("不支持的监听器类型", zap.Error(errors.New(listener.Type)))
	}
}

func StopListenerFactory(listener *sys.Listener) {
	switch listener.Type {
	case "pipe":
		pipe.PipeManager.StopListener(listener.ID)
	case "tcp":
		// 停止TCP监听器
		if err := tcp.TCPManager.StopListener(listener.ID); err != nil {
			global.LOG.Error("停止TCP监听器失败", zap.Error(err))
		}
	}
}
