package c2

import (
	"context"
	"fmt"
	"net/http"
	"server/core/manager/shutdown"
	"server/core/manager/ssemgr"
	"server/global"
	"server/model/request/network"
	"server/model/response"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type NetworkApi struct{}

// GetNetworkStats 获取网络统计信息
func (n *NetworkApi) GetNetworkStats(c *gin.Context) {
	clientIDStr := c.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", c)
		return
	}

	var req network.NetworkStatsRequest
	taskID, err := networkService.GetNetworkStats(uint(clientID), req)
	if err != nil {
		global.LOG.Error("获取网络统计信息失败", zap.Error(err))
		response.ErrorWithMessage("获取网络统计信息失败: "+err.Error(), c)
		return
	}

	// 等待客户端响应（增加超时时间）
	waitForResponseAsyncWithClientId(c, taskID, "获取网络统计信息", clientIDStr)
}

// GetNetworkInterfaces 获取网络接口信息
func (n *NetworkApi) GetNetworkInterfaces(c *gin.Context) {
	clientIDStr := c.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", c)
		return
	}

	var req network.NetworkInterfacesRequest
	taskID, err := networkService.GetNetworkInterfaces(uint(clientID), req)
	if err != nil {
		global.LOG.Error("获取网络接口信息失败", zap.Error(err))
		response.ErrorWithMessage("获取网络接口信息失败: "+err.Error(), c)
		return
	}

	// 等待客户端响应（网络接口信息获取可能需要更长时间）
	waitForResponseAsyncWithClientId(c, taskID, "获取网络接口信息", clientIDStr)
}

// GetNetworkInterfaceProgressStream 获取网络接口处理进度流 (SSE)
func (n *NetworkApi) GetNetworkInterfaceProgressStream(c *gin.Context) {
	clientIDStr := c.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", c)
		return
	}

	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 创建一个超时上下文，防止连接无限期阻塞
	timeoutCtx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Minute)
	defer cancel()

	// 🚀 注册goroutine到关闭管理器
	shutdown.RegisterGoroutine()
	defer shutdown.UnregisterGoroutine()

	global.LOG.Info("网络接口进度SSE连接已建立", zap.Uint("clientID", uint(clientID)))

	// 获取进度管理器并注册SSE连接
	progressManager := ssemgr.GetProgressManager()
	clientAddr := fmt.Sprintf("client_%d", clientID) // 使用统一的客户端地址格式
	progressManager.AddSSEClient(clientAddr, c)
	defer progressManager.RemoveSSEClient(clientAddr, c)

	// 发送初始连接确认
	c.SSEvent("connected", gin.H{
		"type":      "connected",
		"message":   "网络接口进度推送已连接",
		"clientId":  clientID,
		"timestamp": time.Now().Unix(),
	})
	c.Writer.Flush()

	// 发送当前进度（如果有的话）
	if currentProgress := progressManager.GetProgress(clientAddr); currentProgress != nil {
		c.SSEvent("interface_progress", gin.H{
			"type":      "interface_progress",
			"data":      currentProgress,
			"timestamp": time.Now().Unix(),
		})
		c.Writer.Flush()
	}

	// 创建保活循环
	ticker := time.NewTicker(30 * time.Second) // 每30秒发送保活信号
	defer ticker.Stop()

	for {
		select {
		case <-timeoutCtx.Done():
			global.LOG.Info("网络接口进度SSE连接超时或被取消", zap.Uint("clientID", uint(clientID)))
			return
		case <-c.Request.Context().Done():
			global.LOG.Info("网络接口进度SSE连接已断开", zap.Uint("clientID", uint(clientID)))
			return
		case <-shutdown.GetShutdownChannel():
			global.LOG.Info("收到服务器关闭信号，停止网络接口进度SSE推送", zap.Uint("clientID", uint(clientID)))
			return
		case <-ticker.C:
			// 发送保活信号
			c.SSEvent("keepalive", gin.H{
				"type":      "keepalive",
				"timestamp": time.Now().Unix(),
			})
			if flusher, ok := c.Writer.(http.Flusher); ok {
				flusher.Flush()
			}
		}
	}
}

// GetNetworkConnections 获取网络连接信息
func (n *NetworkApi) GetNetworkConnections(c *gin.Context) {
	clientIDStr := c.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", c)
		return
	}

	protocol := c.Query("protocol")
	state := c.Query("state")

	req := network.NetworkConnectionsRequest{
		Protocol: protocol,
		State:    state,
	}

	taskID, err := networkService.GetNetworkConnections(uint(clientID), req)
	if err != nil {
		global.LOG.Error("获取网络连接信息失败", zap.Error(err))
		response.ErrorWithMessage("获取网络连接信息失败: "+err.Error(), c)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(c, taskID, "获取网络连接信息", clientIDStr)
}

// CloseConnection 关闭网络连接
func (n *NetworkApi) CloseConnection(c *gin.Context) {
	clientIDStr := c.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", c)
		return
	}

	var req network.CloseConnectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	taskID, err := networkService.CloseConnection(uint(clientID), req)
	if err != nil {
		global.LOG.Error("关闭网络连接失败", zap.Error(err))
		response.ErrorWithMessage("关闭网络连接失败: "+err.Error(), c)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(c, taskID, "关闭网络连接", clientIDStr)
}
