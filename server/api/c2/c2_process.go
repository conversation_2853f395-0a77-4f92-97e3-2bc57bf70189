package c2

import (
	"server/global"
	"server/model/request/proc"
	"server/model/response"
	"server/utils"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProcApi struct{}

// ListProc 获取进程列表
func (p *ProcApi) ListProc(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req proc.ProcessListRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := procService.ListProc(uint(clientID), req)
	if err != nil {
		global.LOG.Error("列出进程失败", zap.Error(err))
		response.ErrorWithMessage("列出进程失败: "+err.Error(), ctx)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(ctx, taskID, "列出进程", clientIDStr)
}

// KillProcess 终止进程
func (p *ProcApi) KillProcess(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req proc.ProcessKillRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := procService.KillProcess(uint(clientID), req)
	if err != nil {
		global.LOG.Error("终止进程失败", zap.Error(err))
		response.ErrorWithMessage("终止进程失败: "+err.Error(), ctx)
		return
	}

	waitForResponseAsyncWithClientId(ctx, taskID, "终止进程", clientIDStr)
}

// StartProcess 启动进程
func (p *ProcApi) StartProcess(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req proc.ProcessStartRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := procService.StartProcess(uint(clientID), req)
	if err != nil {
		global.LOG.Error("启动进程失败", zap.Error(err))
		response.ErrorWithMessage("启动进程失败: "+err.Error(), ctx)
		return
	}
	global.LOG.Info("等待结果", zap.String("command", utils.NormalizePath(req.Command)))
	waitForResponseAsyncWithClientId(ctx, taskID, "启动进程", clientIDStr)
}

// GetProcessDetails 获取进程详情
func (p *ProcApi) GetProcessDetails(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req proc.ProcessDetailsRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := procService.GetProcessDetails(uint(clientID), req)
	if err != nil {
		global.LOG.Error("获取进程详情失败", zap.Error(err))
		response.ErrorWithMessage("获取进程详情失败: "+err.Error(), ctx)
		return
	}

	waitForResponseAsyncWithClientId(ctx, taskID, "获取进程详情", clientIDStr)
}

// SuspendProcess 挂起进程
func (p *ProcApi) SuspendProcess(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req proc.ProcessSuspendRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := procService.SuspendProcess(uint(clientID), req)
	if err != nil {
		global.LOG.Error("挂起进程失败", zap.Error(err))
		response.ErrorWithMessage("挂起进程失败: "+err.Error(), ctx)
		return
	}

	waitForResponseAsyncWithClientId(ctx, taskID, "挂起进程", clientIDStr)
}

// ResumeProcess 恢复进程
func (p *ProcApi) ResumeProcess(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req proc.ProcessResumeRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := procService.ResumeProcess(uint(clientID), req)
	if err != nil {
		global.LOG.Error("恢复进程失败", zap.Error(err))
		response.ErrorWithMessage("恢复进程失败: "+err.Error(), ctx)
		return
	}

	waitForResponseAsyncWithClientId(ctx, taskID, "恢复进程", clientIDStr)
}
