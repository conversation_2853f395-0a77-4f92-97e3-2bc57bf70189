package c2

import (
	"server/global"
	"server/model/request/fs"
	"server/model/response"
	"strconv"


	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)



type DirApi struct{}

// CreateDir 创建目录
func (d *DirApi) CreateDir(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.DirCreateRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := dirService.CreateDir(uint(clientID), req)
	if err != nil {
		global.LOG.Error("创建目录失败", zap.Error(err))
		response.ErrorWithMessage("创建目录失败: "+err.Error(), ctx)
		return
	}
	// 🚨 使用异步等待避免阻塞
	waitForResponseAsyncWithClientId(ctx, taskID, "创建目录", clientIDStr)
}

// ListDir 列出目录内容
func (d *DirApi) ListDir(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.DirListRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := dirService.ListDir(uint(clientID), req)
	if err != nil {
		global.LOG.Error("列出目录失败", zap.Error(err))
		response.ErrorWithMessage("列出目录失败: "+err.Error(), ctx)
		return
	}

	// 🚨 使用异步等待避免阻塞
	waitForResponseAsyncWithClientId(ctx, taskID, "获取目录列表", clientIDStr)
}

// MoveDir 移动目录
func (d *DirApi) MoveDir(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.DirMoveRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := dirService.MoveDir(uint(clientID), req)
	if err != nil {
		global.LOG.Error("移动目录失败", zap.Error(err))
		response.ErrorWithMessage("移动目录失败: "+err.Error(), ctx)
		return
	}
	// 🚨 使用异步等待避免阻塞
	waitForResponseAsyncWithClientId(ctx, taskID, "移动目录", clientIDStr)
}

// DeleteDir 删除目录
func (d *DirApi) DeleteDir(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.DirDeleteRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := dirService.DeleteDir(uint(clientID), req)
	if err != nil {
		global.LOG.Error("删除目录失败", zap.Error(err))
		response.ErrorWithMessage("删除目录失败: "+err.Error(), ctx)
		return
	}
	// 🚨 使用异步等待避免阻塞
	waitForResponseAsyncWithClientId(ctx, taskID, "删除目录", clientIDStr)
}

// CopyDir 复制目录
func (d *DirApi) CopyDir(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.DirCopyRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := dirService.CopyDir(uint(clientID), req)
	if err != nil {
		global.LOG.Error("复制目录失败", zap.Error(err))
		response.ErrorWithMessage("复制目录失败: "+err.Error(), ctx)
		return
	}
	// 🚨 使用异步等待避免阻塞
	waitForResponseAsyncWithClientId(ctx, taskID, "复制目录", clientIDStr)
}

// ListDisks 获取磁盘列表
func (d *DirApi) ListDisks(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.DiskListRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := dirService.ListDisks(uint(clientID), req)
	if err != nil {
		global.LOG.Error("获取磁盘列表失败", zap.Error(err))
		response.ErrorWithMessage("获取磁盘列表失败: "+err.Error(), ctx)
		return
	}

	// 🚨 使用异步等待避免阻塞
	waitForResponseAsyncWithClientId(ctx, taskID, "获取磁盘列表", clientIDStr)
}
