package c2

import (
	"context"
	"fmt"
	"net/http"
	"server/core/manager/shutdown"
	"server/global"
	"server/model/request"
	"server/model/response"
	"server/model/sys"
	"server/service/c2"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ClientApi struct{}

// GetClientList 获取客户端列表
func (c *ClientApi) GetClientList(ctx *gin.Context) {
	var searchInfo request.ClientSearch
	if err := ctx.ShouldBindJSON(&searchInfo); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	if searchInfo.Page == 0 {
		searchInfo.Page = 1
	}
	if searchInfo.PageSize == 0 {
		searchInfo.PageSize = 10
	}

	clientInfo := sys.Client{
		ListenerID:   searchInfo.ListenerID,
		ListenerType: searchInfo.ListenerType,
		RemoteAddr:   searchInfo.RemoteAddr,
		Remark:       searchInfo.Remark,
	}

	// 处理状态过滤，使用指针来区分是否设置了状态
	var statusFilter *int
	// 对于POST请求，直接使用传入的status值
	if searchInfo.StatusSet {
		statusFilter = &searchInfo.Status
	}

	list, total, err := clientService.GetClientList(clientInfo, searchInfo.PageSize, searchInfo.Page, statusFilter)
	if err != nil {
		global.LOG.Error("获取客户端列表失败", zap.Error(err))
		response.ErrorWithMessage("获取客户端列表失败: "+err.Error(), ctx)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     searchInfo.Page,
		PageSize: searchInfo.PageSize,
	}, "获取成功", ctx)
}

// GetClient 获取单个客户端
func (c *ClientApi) GetClient(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("参数错误", ctx)
		return
	}

	client, err := clientService.GetClient(uint(id))
	if err != nil {
		global.LOG.Error("获取客户端失败", zap.Error(err))
		response.ErrorWithMessage("获取客户端失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(client, ctx)
}

// UpdateClientRemark 更新客户端备注
func (c *ClientApi) UpdateClientRemark(ctx *gin.Context) {
	var client sys.Client
	if err := ctx.ShouldBindJSON(&client); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	if client.ID == 0 {
		response.ErrorWithMessage("缺少客户端ID", ctx)
		return
	}

	if err := clientService.UpdateClientRemark(client.ID, client.Remark); err != nil {
		global.LOG.Error("更新客户端备注失败", zap.Error(err))
		response.ErrorWithMessage("更新客户端备注失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("更新客户端备注成功", ctx)
}

// DeleteClient 删除客户端
func (c *ClientApi) DeleteClient(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("参数错误", ctx)
		return
	}

	if err = clientService.DeleteClient(uint(id)); err != nil {
		global.LOG.Error("删除客户端失败", zap.Error(err))
		response.ErrorWithMessage("删除客户端失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("删除客户端成功", ctx)
}

// SendCommand 向客户端发送命令
func (c *ClientApi) SendCommand(ctx *gin.Context) {
	var req request.ClientCommandRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	if err := commandService.SendCommand(req.ID, req.Command); err != nil {
		global.LOG.Error("发送命令失败", zap.Error(err))
		response.ErrorWithMessage("发送命令失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx) // 只返回成功状态码，不返回消息
}

// SendCommandToTerminal 向客户端的指定终端发送命令
func (c *ClientApi) SendCommandToTerminal(ctx *gin.Context) {
	var req struct {
		ID         uint   `json:"id" binding:"required"`
		Command    string `json:"command" binding:"required"`
		TerminalID uint32 `json:"terminal_id"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	if err := commandService.SendCommandToTerminal(req.ID, req.Command, req.TerminalID); err != nil {
		global.LOG.Error("发送多终端命令失败", zap.Error(err))
		response.ErrorWithMessage("发送命令失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// CreateBackupTerminal 创建备用终端
func (c *ClientApi) CreateBackupTerminal(ctx *gin.Context) {
	clientIDStr := ctx.Param("id")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID格式错误", ctx)
		return
	}

	global.LOG.Info("创建备用终端请求", zap.Uint("clientID", uint(clientID)))

	// 调用service创建终端，获取taskID
	taskID, err := commandService.SendCreateTerminalCommand(uint(clientID))
	if err != nil {
		global.LOG.Error("发送创建终端命令失败", zap.Error(err))
		response.ErrorWithMessage("发送创建终端命令失败: "+err.Error(), ctx)
		return
	}

	// 等待客户端响应
	waitForResponseAsync(ctx, taskID, "创建终端")
}

// CloseBackupTerminal 关闭备用终端
func (c *ClientApi) CloseBackupTerminal(ctx *gin.Context) {
	clientIDStr := ctx.Param("id")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID格式错误", ctx)
		return
	}

	var req struct {
		TerminalID uint32 `json:"terminal_id" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	global.LOG.Info("关闭备用终端请求",
		zap.Uint("clientID", uint(clientID)),
		zap.Uint32("terminalID", req.TerminalID))

	// 调用service关闭终端
	err = commandService.SendCloseTerminalCommand(uint(clientID), req.TerminalID)
	if err != nil {
		global.LOG.Error("发送关闭终端命令失败", zap.Error(err))
		response.ErrorWithMessage("发送关闭终端命令失败: "+err.Error(), ctx)
		return
	}

	// 等待客户端响应
	taskID := clientID*10000 + uint64(req.TerminalID) // 使用不同的taskID避免冲突
	waitForResponseAsync(ctx, taskID, "关闭终端")
}

// GetTerminalList 获取客户端终端列表
func (c *ClientApi) GetTerminalList(ctx *gin.Context) {
	clientIDStr := ctx.Param("id")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID格式错误", ctx)
		return
	}

	global.LOG.Info("获取终端列表请求",
		zap.Uint("clientID", uint(clientID)))

	// 调用service获取终端列表，获取taskID
	taskID, err := commandService.GetTerminalList(uint(clientID))
	if err != nil {
		global.LOG.Error("发送获取终端列表命令失败", zap.Error(err))
		response.ErrorWithMessage("发送获取终端列表命令失败: "+err.Error(), ctx)
		return
	}

	// 等待客户端响应
	waitForResponseAsync(ctx, taskID, "获取终端列表")
}

// DisconnectClient 断开客户端连接
func (c *ClientApi) DisconnectClient(ctx *gin.Context) {
	var req request.ClientTerminalRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	if err := clientService.DisconnectClient(req.ID); err != nil {
		global.LOG.Error("断开连接失败", zap.Error(err))
		response.ErrorWithMessage("断开连接失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("断开连接成功", ctx)
}

// ClearOfflineClients 清除所有离线客户端
func (c *ClientApi) ClearOfflineClients(ctx *gin.Context) {
	count, err := clientService.ClearOfflineClients()
	if err != nil {
		global.LOG.Error("清除离线客户端失败", zap.Error(err))
		response.ErrorWithMessage("清除离线客户端失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage(fmt.Sprintf("成功清除 %d 个离线客户端", count), ctx)
}

// GetClientHeartbeatStream 获取客户端心跳数据流 (SSE)
func (c *ClientApi) GetClientHeartbeatStream(ctx *gin.Context) {
	// 验证token
	token := sys.GetToken(ctx)
	if token == "" {
		ctx.String(http.StatusUnauthorized, "未提供认证信息")
		return
	}

	// 验证token有效性
	j := sys.NewJWT()
	_, err := j.ParseToken(token)
	if err != nil {
		global.LOG.Error("SSE连接认证失败", zap.Error(err))
		ctx.String(http.StatusUnauthorized, "认证失败")
		return
	}

	clientIDStr := ctx.Param("id")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID格式错误", ctx)
		return
	}

	// 设置SSE响应头
	ctx.Header("Content-Type", "text/event-stream")
	ctx.Header("Cache-Control", "no-cache")
	ctx.Header("Connection", "keep-alive")
	ctx.Header("Access-Control-Allow-Origin", "*")
	ctx.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 获取客户端信息
	var clientService c2.ClientService
	client, err := clientService.GetClient(uint(clientID))
	if err != nil {
		global.LOG.Error("获取客户端信息失败", zap.Uint("clientID", uint(clientID)), zap.Error(err))
		// 为了测试，创建一个模拟的客户端对象
		client = sys.Client{
			OS:           "linux",
			Architecture: "amd64",
			RemoteAddr:   "127.0.0.1:12345",
			Status:       1,
			ConnectedAt:  time.Now().Add(-time.Hour), // 1小时前创建
			LastActiveAt: time.Now(),
			Username:     "testuser",
			Hostname:     "test-host",
		}
		client.ID = uint(clientID) // 设置ID字段
		global.LOG.Info("使用模拟客户端数据进行测试", zap.Uint("clientID", uint(clientID)))
	}

	global.LOG.Info("开始推送客户端心跳数据流", zap.Uint("clientID", uint(clientID)))

	// 创建心跳数据推送循环
	ticker := time.NewTicker(2 * time.Second) // 每2秒推送一次
	defer ticker.Stop()

	// 立即发送一次当前数据
	c.sendHeartbeatData(ctx, &client)

	// 创建一个超时上下文，防止连接无限期阻塞
	timeoutCtx, cancel := context.WithTimeout(ctx.Request.Context(), 30*time.Minute)
	defer cancel()

	// 🚀 注册goroutine到关闭管理器
	shutdown.RegisterGoroutine()
	defer shutdown.UnregisterGoroutine()

	for {
		select {
		case <-timeoutCtx.Done():
			global.LOG.Info("客户端心跳数据流连接超时或被取消", zap.Uint("clientID", uint(clientID)))
			return
		case <-ctx.Request.Context().Done():
			global.LOG.Info("客户端心跳数据流连接已断开", zap.Uint("clientID", uint(clientID)))
			return
		case <-shutdown.GetShutdownChannel():
			global.LOG.Info("收到服务器关闭信号，停止SSE推送", zap.Uint("clientID", uint(clientID)))
			return
		case <-ticker.C:
			// 重新获取最新的客户端信息
			updatedClient, err := clientService.GetClient(uint(clientID))
			if err != nil {
				ctx.SSEvent("error", map[string]string{"message": "获取客户端信息失败"})
				continue
			}
			c.sendHeartbeatData(ctx, &updatedClient)

			// 检查连接是否还活跃
			if flusher, ok := ctx.Writer.(http.Flusher); ok {
				flusher.Flush()
			}

			// 检查写入是否成功
			if ctx.Writer.Size() < 0 {
				global.LOG.Info("客户端连接已断开，停止推送", zap.Uint("clientID", uint(clientID)))
				return
			}
		}
	}
}

// sendHeartbeatData 发送心跳数据
func (c *ClientApi) sendHeartbeatData(ctx *gin.Context, client *sys.Client) {
	// 处理系统信息，提供默认值
	osInfo := client.OS
	if osInfo == "" {
		osInfo = "unknown" // 默认值
	}
	archInfo := client.Architecture
	if archInfo == "" {
		archInfo = "unknown" // 默认值
	}

	// 构造心跳数据 - 使用真实的心跳数据
	heartbeatData := map[string]interface{}{
		"timestamp": time.Now(),
		"client_id": client.ID,
		"system_info": map[string]interface{}{
			"os":           osInfo,
			"arch":         archInfo,
			"cpu_usage":    client.CPUUsage,                                      // 来自客户端心跳数据
			"memory_usage": client.MemoryUsage,                                   // 来自客户端心跳数据
			"disk_usage":   client.DiskUsage,                                     // 来自客户端心跳数据
			"uptime":       c.calculateUptime(client.Uptime, client.ConnectedAt), // 修正运行时间计算
			"load_avg":     client.LoadAvg,                                       // 来自客户端心跳数据
		},
		"network_info": map[string]interface{}{
			"local_ip":    client.LocalIP,    // 来自客户端心跳数据
			"public_ip":   client.PublicIP,   // 来自客户端心跳数据
			"latency":     client.Latency,    // 来自客户端心跳数据
			"packet_loss": client.PacketLoss, // 来自客户端心跳数据
			"bandwidth":   client.Bandwidth,  // 来自客户端心跳数据
		},
		"config": c.getHeartbeatConfigForClient(client.ID),
		"client_info": map[string]interface{}{
			"should_reconnect": false,
			"new_server_addr":  "",
			"config_update":    false,
			"commands":         []string{}, // 可以从数据库获取待执行命令
			// 地理位置信息
			"country":    client.Country,
			"province":   client.Province,
			"city":       client.City,
			"isp":        client.ISP,
			"asn":        client.ASN,
			"geo_source": client.GeoSource,
		},
		"status": map[string]interface{}{
			"online":          client.Status == 1,
			"last_active_at":  client.LastActiveAt,
			"connection_time": client.ConnectedAt, // 使用ConnectedAt而不是CreatedAt
		},
	}

	// 发送SSE事件
	ctx.SSEvent("heartbeat", heartbeatData)
	// 关键修复：立即刷新缓冲区，避免chunked编码问题
	ctx.Writer.Flush()
}

// calculateUptime 计算正确的运行时间
func (c *ClientApi) calculateUptime(clientUptime int64, connectedAt time.Time) int64 {
	// 如果客户端发送的uptime看起来是绝对时间戳（太大），则使用连接时间计算
	if clientUptime > 86400*365 { // 如果超过1年的秒数，认为是错误数据
		return int64(time.Since(connectedAt).Seconds())
	}
	// 否则使用客户端提供的uptime
	return clientUptime
}

// SendClientReconnect 发送客户端重连指令
func (c *ClientApi) SendClientReconnect(ctx *gin.Context) {
	clientIDStr := ctx.Param("id")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID格式错误", ctx)
		return
	}

	// TODO: 实现向客户端发送重连指令的逻辑
	// 可以通过心跳管理器或者消息队列发送指令

	global.LOG.Info("发送客户端重连指令", zap.Uint("clientID", uint(clientID)))
	response.OkWithMessage("重连指令已发送", ctx)
}

// SendClientConfigUpdate 发送客户端配置更新指令
func (c *ClientApi) SendClientConfigUpdate(ctx *gin.Context) {
	clientIDStr := ctx.Param("id")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID格式错误", ctx)
		return
	}

	// TODO: 实现向客户端发送配置更新指令的逻辑

	global.LOG.Info("发送客户端配置更新指令", zap.Uint("clientID", uint(clientID)))
	response.OkWithMessage("配置更新指令已发送", ctx)
}

// ExecuteClientCommand 执行客户端命令
func (c *ClientApi) ExecuteClientCommand(ctx *gin.Context) {
	clientIDStr := ctx.Param("id")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID格式错误", ctx)
		return
	}

	var req struct {
		Command string `json:"command" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	// TODO: 实现向客户端发送命令执行指令的逻辑

	global.LOG.Info("发送客户端命令执行指令",
		zap.Uint("clientID", uint(clientID)),
		zap.String("command", req.Command))
	response.OkWithMessage("命令执行指令已发送", ctx)
}

// getHeartbeatConfigForClient 获取客户端的心跳配置
func (c *ClientApi) getHeartbeatConfigForClient(clientID uint) map[string]interface{} {
	configService := heartbeatConfigService
	config, err := configService.GetHeartbeatConfigForClient(clientID)
	if err != nil {
		// 如果获取失败，返回默认配置
		return map[string]interface{}{
			"interval":     30,
			"timeout":      10,
			"max_retries":  5,
			"jitter_range": 5000,
		}
	}

	return map[string]interface{}{
		"interval":     config.Interval,
		"timeout":      config.Timeout,
		"max_retries":  config.MaxRetries,
		"jitter_range": config.JitterRange,
	}
}


