package sys

import (
	"bufio"
	"io"
	"fmt"
	"os"
	"path/filepath"
	"server/core/manager/workerpool"
	"server/global"
	"server/model/response"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type LogApi struct{}

// LogFileInfo 日志文件信息
type LogFileInfo struct {
	Name         string    `json:"name"`         // 文件名
	Path         string    `json:"path"`         // 文件路径
	Size         int64     `json:"size"`         // 文件大小
	ModTime      time.Time `json:"modTime"`      // 修改时间
	Level        string    `json:"level"`        // 日志级别
	Date         string    `json:"date"`         // 日期
	RelativePath string    `json:"relativePath"` // 相对路径
}

// LogContent 日志内容
type LogContent struct {
	Lines      []string `json:"lines"`      // 日志行
	TotalLines int      `json:"totalLines"` // 总行数
	FileSize   int64    `json:"fileSize"`   // 文件大小
	LastUpdate time.Time `json:"lastUpdate"` // 最后更新时间
}

// GetLogFileList 获取日志文件列表
func (l *LogApi) GetLogFileList(c *gin.Context) {
	logDir := global.CONFIG.Zap.Director
	if logDir == "" {
		logDir = "log"
	}

	// 确保日志目录存在
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		response.ErrorWithMessage("日志目录不存在", c)
		return
	}

	var logFiles []LogFileInfo

	// 遍历日志目录
	err := filepath.Walk(logDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理.log文件
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".log") {
			// 解析文件路径获取日期和级别
			relativePath, _ := filepath.Rel(logDir, path)
			pathParts := strings.Split(relativePath, string(os.PathSeparator))
			
			var date, level string
			if len(pathParts) >= 2 {
				date = pathParts[0]
				fileName := pathParts[1]
				level = strings.TrimSuffix(fileName, ".log")
			} else {
				fileName := info.Name()
				level = strings.TrimSuffix(fileName, ".log")
				date = "unknown"
			}

			logFile := LogFileInfo{
				Name:         info.Name(),
				Path:         path,
				Size:         info.Size(),
				ModTime:      info.ModTime(),
				Level:        level,
				Date:         date,
				RelativePath: relativePath,
			}
			logFiles = append(logFiles, logFile)
		}
		return nil
	})

	if err != nil {
		global.LOG.Error("读取日志目录失败", zap.Error(err))
		response.ErrorWithMessage("读取日志目录失败", c)
		return
	}

	// 按修改时间倒序排列
	sort.Slice(logFiles, func(i, j int) bool {
		return logFiles[i].ModTime.After(logFiles[j].ModTime)
	})

	response.OkWithDetailed(gin.H{"files": logFiles}, "获取成功", c)
}

// GetLogContent 获取日志文件内容
func (l *LogApi) GetLogContent(c *gin.Context) {
	filePath := c.Query("path")
	if filePath == "" {
		// 如果没有指定路径，尝试获取最新的日志文件
		logDir := global.CONFIG.Zap.Director
		if logDir == "" {
			logDir = "log"
		}

		// 获取今天的日志文件
		today := time.Now().Format("2006-01-02")
		infoLogPath := filepath.Join(logDir, today, "info.log")

		if _, err := os.Stat(infoLogPath); err == nil {
			filePath = infoLogPath
		} else {
			response.ErrorWithMessage("未找到可用的日志文件", c)
			return
		}
	}

	// 安全检查：确保文件在日志目录内
	logDir := global.CONFIG.Zap.Director
	if logDir == "" {
		logDir = "log"
	}

	absLogDir, err := filepath.Abs(logDir)
	if err != nil {
		response.ErrorWithMessage("日志目录路径错误", c)
		return
	}

	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		response.ErrorWithMessage("文件路径错误", c)
		return
	}

	if !strings.HasPrefix(absFilePath, absLogDir) {
		response.ErrorWithMessage("无权访问该文件", c)
		return
	}

	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "100")
	
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 1000 {
		pageSize = 100
	}

	// 读取文件
	file, err := os.Open(filePath)
	if err != nil {
		global.LOG.Error("打开日志文件失败", zap.Error(err))
		response.ErrorWithMessage("打开日志文件失败", c)
		return
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		response.ErrorWithMessage("获取文件信息失败", c)
		return
	}

	// 读取所有行（对于大文件，这里可以优化为流式读取）
	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	if err := scanner.Err(); err != nil {
		global.LOG.Error("读取日志文件失败", zap.Error(err))
		response.ErrorWithMessage("读取日志文件失败", c)
		return
	}

	totalLines := len(lines)
	
	// 分页处理
	start := (page - 1) * pageSize
	end := start + pageSize
	
	if start >= totalLines {
		lines = []string{}
	} else {
		if end > totalLines {
			end = totalLines
		}
		lines = lines[start:end]
	}

	logContent := LogContent{
		Lines:      lines,
		TotalLines: totalLines,
		FileSize:   fileInfo.Size(),
		LastUpdate: fileInfo.ModTime(),
	}

	response.OkWithDetailed(gin.H{
		"content":    logContent,
		"page":       page,
		"pageSize":   pageSize,
		"totalPages": (totalLines + pageSize - 1) / pageSize,
	}, "获取成功", c)
}

// StreamLogContent 实时流式获取日志内容
func (l *LogApi) StreamLogContent(c *gin.Context) {
	filePath := c.Query("path")
	if filePath == "" {
		response.ErrorWithMessage("文件路径不能为空", c)
		return
	}

	// 安全检查
	logDir := global.CONFIG.Zap.Director
	if logDir == "" {
		logDir = "log"
	}

	absLogDir, err := filepath.Abs(logDir)
	if err != nil {
		response.ErrorWithMessage("日志目录路径错误", c)
		return
	}

	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		response.ErrorWithMessage("文件路径错误", c)
		return
	}

	if !strings.HasPrefix(absFilePath, absLogDir) {
		response.ErrorWithMessage("无权访问该文件", c)
		return
	}

	// 设置SSE头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// 获取文件当前大小
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		c.SSEvent("error", "文件不存在")
		return
	}

	lastSize := fileInfo.Size()
	
	// 发送初始内容
	l.sendFileContent(c, filePath, 0, lastSize)

	// 定时检查文件变化
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.Request.Context().Done():
			return
		case <-ticker.C:
			currentInfo, err := os.Stat(filePath)
			if err != nil {
				continue
			}

			currentSize := currentInfo.Size()
			if currentSize > lastSize {
				// 文件有新内容
				l.sendFileContent(c, filePath, lastSize, currentSize-lastSize)
				lastSize = currentSize
			}
		}
	}
}

// sendFileContent 发送文件内容
func (l *LogApi) sendFileContent(c *gin.Context, filePath string, offset, length int64) {
	file, err := os.Open(filePath)
	if err != nil {
		return
	}
	defer file.Close()

	// 定位到指定位置
	if offset > 0 {
		_, err = file.Seek(offset, io.SeekStart)
		if err != nil {
			return
		}
	}

	// 读取指定长度的内容
	buffer := make([]byte, length)
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return
	}

	if n > 0 {
		content := string(buffer[:n])
		lines := strings.Split(content, "\n")
		
		for _, line := range lines {
			if strings.TrimSpace(line) != "" {
				c.SSEvent("log", gin.H{
					"line":      line,
					"timestamp": time.Now().Unix(),
				})
				c.Writer.Flush()
			}
		}
	}
}

// GetLogStats 获取日志统计信息
func (l *LogApi) GetLogStats(c *gin.Context) {
	// 🚀 使用工作池处理文件I/O操作，避免阻塞
	done := make(chan gin.H, 1)
	errChan := make(chan error, 1)

	task := workerpool.NewFileIOTask("get_log_stats", func() error {
		defer func() {
			if r := recover(); r != nil {
				errChan <- fmt.Errorf("获取日志统计时发生panic: %v", r)
			}
		}()

		logDir := global.CONFIG.Zap.Director
		if logDir == "" {
			logDir = "log"
		}

		stats := make(map[string]interface{})

		// 统计各级别日志文件数量和大小
		levelStats := make(map[string]map[string]interface{})

		err := filepath.Walk(logDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && strings.HasSuffix(info.Name(), ".log") {
			relativePath, _ := filepath.Rel(logDir, path)
			pathParts := strings.Split(relativePath, string(os.PathSeparator))
			
			var level string
			if len(pathParts) >= 2 {
				fileName := pathParts[1]
				level = strings.TrimSuffix(fileName, ".log")
			} else {
				fileName := info.Name()
				level = strings.TrimSuffix(fileName, ".log")
			}

			if levelStats[level] == nil {
				levelStats[level] = map[string]interface{}{
					"count": 0,
					"size":  int64(0),
				}
			}
			
			levelStats[level]["count"] = levelStats[level]["count"].(int) + 1
			levelStats[level]["size"] = levelStats[level]["size"].(int64) + info.Size()
		}
		return nil
		})

		if err != nil {
			errChan <- fmt.Errorf("统计日志信息失败: %w", err)
			return err
		}

		stats["levels"] = levelStats
		stats["logDir"] = logDir
		stats["timestamp"] = time.Now().Unix()

		done <- gin.H(stats)
		return nil
	})

	if err := workerpool.SubmitFileIOTask(task); err != nil {
		global.LOG.Error("提交日志统计任务失败", zap.Error(err))
		response.ErrorWithMessage("提交统计任务失败", c)
		return
	}

	select {
	case responseData := <-done:
		response.OkWithDetailed(responseData, "获取成功", c)
	case err := <-errChan:
		global.LOG.Error("获取日志统计失败", zap.Error(err))
		response.ErrorWithMessage("获取日志统计失败: "+err.Error(), c)
	case <-time.After(10 * time.Second): // 文件I/O可能需要更长时间
		global.LOG.Warn("获取日志统计超时")
		response.ErrorWithMessage("获取日志统计超时", c)
	}
}

// DownloadLogFile 下载日志文件
func (l *LogApi) DownloadLogFile(c *gin.Context) {
	filePath := c.Query("path")
	if filePath == "" {
		response.ErrorWithMessage("文件路径不能为空", c)
		return
	}

	// 安全检查：确保文件在日志目录内
	logDir := global.CONFIG.Zap.Director
	if logDir == "" {
		logDir = "log"
	}

	absLogDir, err := filepath.Abs(logDir)
	if err != nil {
		response.ErrorWithMessage("日志目录路径错误", c)
		return
	}

	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		response.ErrorWithMessage("文件路径错误", c)
		return
	}

	if !strings.HasPrefix(absFilePath, absLogDir) {
		response.ErrorWithMessage("无权访问该文件", c)
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		response.ErrorWithMessage("文件不存在", c)
		return
	}

	// 获取文件名
	fileName := filepath.Base(filePath)

	// 设置下载头
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", "application/octet-stream")

	// 发送文件
	c.File(filePath)
}
