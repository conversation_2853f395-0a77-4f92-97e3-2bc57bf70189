package sys

import (
	"github.com/gin-gonic/gin"
	"net/url"
	"server/global"
	"server/model/request"
	"server/model/response"
)

type ConfigApi struct{}

// GetServerConfig 获取服务器配置信息
func (a *ConfigApi) GetServerConfig(c *gin.Context) {
	// 获取当前请求的主机和协议
	scheme := "http"
	if c.Request.TLS != nil {
		scheme = "https"
	}

	// 获取当前请求的主机地址
	host := c.Request.Host

	// 构建API基础URL
	apiBaseURL := url.URL{
		Scheme: scheme,
		Host:   host,
		Path:   global.CONFIG.Server.RouterPrefix,
	}

	// 构建WebSocket URL
	wsScheme := "ws"
	if scheme == "https" {
		wsScheme = "wss"
	}
	wsBaseURL := url.URL{
		Scheme: wsScheme,
		Host:   host,
		Path:   global.CONFIG.Server.RouterPrefix,
	}

	// 返回配置信息
	response.OkWithData(map[string]interface{}{
		"apiBaseUrl": apiBaseURL.String(),
		"wsBaseUrl":  wsBaseURL.String(),
		"serverPort": global.CONFIG.Server.PORT,
		"version":    "1.0.0", // 可以从全局配置中获取版本信息
	}, c)
}

// GetGeneralConfig 获取常规配置
func (a *ConfigApi) GetGeneralConfig(c *gin.Context) {
	config, err := configService.GetGeneralConfig()
	if err != nil {
		response.ErrorWithMessage("获取配置失败: "+err.Error(), c)
		return
	}
	response.OkWithData(config, c)
}

// UpdateGeneralConfig 更新常规配置
func (a *ConfigApi) UpdateGeneralConfig(c *gin.Context) {
	var req request.GeneralConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数验证失败: "+err.Error(), c)
		return
	}

	// 额外的业务验证
	if req.Server.ServerStartPort >= req.Server.ServerEndPort {
		response.ErrorWithMessage("代理端口起始范围必须小于结束范围", c)
		return
	}

	// 验证路由前缀格式
	if req.Server.RouterPrefix == "" || req.Server.RouterPrefix[0] != '/' {
		response.ErrorWithMessage("路由前缀必须以/开头", c)
		return
	}

	// 验证日志级别
	validLogLevels := map[string]bool{"debug": true, "info": true, "warn": true, "error": true}
	if !validLogLevels[req.Zap.Level] {
		response.ErrorWithMessage("日志级别必须是debug、info、warn或error之一", c)
		return
	}

	if err := configService.UpdateGeneralConfig(req); err != nil {
		response.ErrorWithMessage("更新配置失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("配置更新成功", c)
}

// GetNotificationConfig 获取通知配置
func (a *ConfigApi) GetNotificationConfig(c *gin.Context) {
	config, err := configService.GetNotificationConfig()
	if err != nil {
		response.ErrorWithMessage("获取通知配置失败: "+err.Error(), c)
		return
	}
	response.OkWithData(config, c)
}

// UpdateNotificationConfig 更新通知配置
func (a *ConfigApi) UpdateNotificationConfig(c *gin.Context) {
	var req request.NotificationConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数验证失败: "+err.Error(), c)
		return
	}

	if err := configService.UpdateNotificationConfig(req); err != nil {
		response.ErrorWithMessage("更新通知配置失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("通知配置更新成功", c)
}

// ResetConfig 重置配置到默认值
func (a *ConfigApi) ResetConfig(c *gin.Context) {
	if err := configService.ResetConfig(); err != nil {
		response.ErrorWithMessage("重置配置失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("配置重置成功", c)
}