package sys

import (
	"server/global"
	"server/model/request"
	"server/model/response"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserManageApi struct{}

// GetUserList 获取用户列表
func (u *UserManageApi) GetUserList(c *gin.Context) {
	var searchInfo request.UserSearch
	if err := c.ShouldBindJSON(&searchInfo); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if searchInfo.Page == 0 {
		searchInfo.Page = 1
	}
	if searchInfo.PageSize == 0 {
		searchInfo.PageSize = 10
	}

	list, total, err := userManageService.GetUserList(searchInfo)
	if err != nil {
		global.LOG.Error("获取用户列表失败", zap.Error(err))
		response.ErrorWithMessage("获取用户列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     searchInfo.Page,
		PageSize: searchInfo.PageSize,
	}, "获取成功", c)
}

// CreateUser 创建用户
func (u *UserManageApi) CreateUser(c *gin.Context) {
	var req request.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if err := userManageService.CreateUser(req); err != nil {
		global.LOG.Error("创建用户失败", zap.Error(err))
		response.ErrorWithMessage("创建用户失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("创建用户成功", c)
}

// UpdateUser 更新用户
func (u *UserManageApi) UpdateUser(c *gin.Context) {
	var req request.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if err := userManageService.UpdateUser(req); err != nil {
		global.LOG.Error("更新用户失败", zap.Error(err))
		response.ErrorWithMessage("更新用户失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新用户成功", c)
}

// DeleteUser 删除用户
func (u *UserManageApi) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("用户ID参数错误", c)
		return
	}

	req := request.DeleteUserRequest{ID: uint(id)}
	if err := userManageService.DeleteUser(req); err != nil {
		global.LOG.Error("删除用户失败", zap.Error(err))
		response.ErrorWithMessage("删除用户失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除用户成功", c)
}

// ChangeUserStatus 修改用户状态
func (u *UserManageApi) ChangeUserStatus(c *gin.Context) {
	var req request.ChangeUserStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if err := userManageService.ChangeUserStatus(req); err != nil {
		global.LOG.Error("修改用户状态失败", zap.Error(err))
		response.ErrorWithMessage("修改用户状态失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("修改用户状态成功", c)
}
