package sys

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"server/global"
	"server/model/response"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
)

type DownloadApi struct{}

// DownloadServerFile 从服务器下载文件，支持断点续传
func (d *DownloadApi) DownloadServerFile(c *gin.Context) {
	// 获取文件路径参数
	filePath := c.Query("path")
	if filePath == "" {
		response.ErrorWithMessage("文件路径不能为空", c)
		return
	}

	// JWT中间件已经处理了token验证，包括URL参数中的token
	// 这里可以直接从上下文获取用户信息
	claims, exists := c.Get("claims")
	if !exists {
		response.NoAuth("未登录或非法访问", c)
		return
	}
	_ = claims // 使用claims变量避免未使用警告

	// 安全检查：确保文件路径在允许的目录内
	if !isValidDownloadPath(filePath) {
		response.ErrorWithMessage("无效的文件路径", c)
		return
	}

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			response.ErrorWithMessage("文件不存在", c)
		} else {
			response.ErrorWithMessage("获取文件信息失败", c)
		}
		return
	}

	// 检查是否为目录
	if fileInfo.IsDir() {
		response.ErrorWithMessage("不能下载目录", c)
		return
	}

	fileSize := fileInfo.Size()
	fileName := filepath.Base(filePath)

	// 解析Range请求头
	rangeHeader := c.GetHeader("Range")
	var start, end int64 = 0, fileSize - 1

	if rangeHeader != "" {
		if !strings.HasPrefix(rangeHeader, "bytes=") {
			c.Header("Content-Range", fmt.Sprintf("bytes */%d", fileSize))
			c.Status(http.StatusRequestedRangeNotSatisfiable)
			return
		}

		rangeSpec := strings.TrimPrefix(rangeHeader, "bytes=")
		if strings.Contains(rangeSpec, "-") {
			parts := strings.Split(rangeSpec, "-")
			if len(parts) == 2 {
				if parts[0] != "" {
					if s, err := strconv.ParseInt(parts[0], 10, 64); err == nil {
						start = s
					}
				}
				if parts[1] != "" {
					if e, err := strconv.ParseInt(parts[1], 10, 64); err == nil {
						end = e
					}
				}
			}
		}
	}

	// 验证范围
	if start < 0 || end >= fileSize || start > end {
		c.Header("Content-Range", fmt.Sprintf("bytes */%d", fileSize))
		c.Status(http.StatusRequestedRangeNotSatisfiable)
		return
	}

	contentLength := end - start + 1

	// 设置响应头
	c.Header("Accept-Ranges", "bytes")
	c.Header("Content-Length", strconv.FormatInt(contentLength, 10))
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, fileName))
	c.Header("Cache-Control", "no-cache")

	if rangeHeader != "" {
		c.Header("Content-Range", fmt.Sprintf("bytes %d-%d/%d", start, end, fileSize))
		c.Status(http.StatusPartialContent)
	} else {
		c.Status(http.StatusOK)
	}

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		global.LOG.Error("打开文件失败", zap.Error(err), zap.String("path", filePath))
		c.Status(http.StatusInternalServerError)
		return
	}
	defer file.Close()

	// 定位到起始位置
	if start > 0 {
		if _, err := file.Seek(start, 0); err != nil {
			global.LOG.Error("文件定位失败", zap.Error(err), zap.Int64("start", start))
			c.Status(http.StatusInternalServerError)
			return
		}
	}

	// 流式传输文件内容
	bufferSize := int64(64 * 1024) // 64KB缓冲区
	buffer := make([]byte, bufferSize)
	transferred := int64(0)

	global.LOG.Info("开始文件下载",
		zap.String("fileName", fileName),
		zap.Int64("start", start),
		zap.Int64("end", end),
		zap.Int64("contentLength", contentLength))

	for transferred < contentLength {
		// 检查客户端是否断开连接
		select {
		case <-c.Request.Context().Done():
			global.LOG.Info("客户端断开连接，停止下载", zap.String("fileName", fileName))
			return
		default:
		}

		// 计算本次读取的大小
		readSize := bufferSize
		if remaining := contentLength - transferred; remaining < bufferSize {
			readSize = remaining
		}

		// 读取文件内容
		n, err := file.Read(buffer[:readSize])
		if err != nil {
			if err == io.EOF {
				break
			}
			global.LOG.Error("读取文件失败", zap.Error(err))
			return
		}

		// 写入响应
		if _, err := c.Writer.Write(buffer[:n]); err != nil {
			global.LOG.Error("写入响应失败", zap.Error(err))
			return
		}

		transferred += int64(n)

		// 刷新缓冲区
		if flusher, ok := c.Writer.(http.Flusher); ok {
			flusher.Flush()
		}
	}

	global.LOG.Info("文件下载完成",
		zap.String("fileName", fileName),
		zap.Int64("transferred", transferred))
}

// DownloadScreenshotFile 下载截图文件
func (d *DownloadApi) DownloadScreenshotFile(c *gin.Context) {
	// 获取文件路径参数
	filePath := c.Query("path")
	if filePath == "" {
		response.ErrorWithMessage("文件路径不能为空", c)
		return
	}

	// JWT中间件已经处理了token验证
	claims, exists := c.Get("claims")
	if !exists {
		response.NoAuth("未登录或非法访问", c)
		return
	}
	_ = claims

	// 构建完整的截图文件路径
	screenshotDir := filepath.Join(global.CONFIG.Server.UploadDir, "screenshots")
	fullPath := filepath.Join(screenshotDir, filePath)

	// 安全检查：确保文件路径在截图目录内
	if !isValidScreenshotPath(fullPath, screenshotDir) {
		response.ErrorWithMessage("无效的文件路径", c)
		return
	}

	// 检查文件是否存在
	fileInfo, err := os.Stat(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			response.ErrorWithMessage("文件不存在", c)
		} else {
			response.ErrorWithMessage("获取文件信息失败", c)
		}
		return
	}

	// 检查是否为目录
	if fileInfo.IsDir() {
		response.ErrorWithMessage("不能下载目录", c)
		return
	}

	fileName := filepath.Base(fullPath)
	fileSize := fileInfo.Size()

	// 设置响应头
	c.Header("Content-Type", "image/png") // 默认为PNG，可以根据文件扩展名调整
	c.Header("Content-Disposition", fmt.Sprintf(`inline; filename="%s"`, fileName))
	c.Header("Content-Length", strconv.FormatInt(fileSize, 10))
	c.Header("Cache-Control", "public, max-age=3600") // 缓存1小时

	// 直接发送文件
	c.File(fullPath)
}

// isValidScreenshotPath 检查截图文件路径是否安全
func isValidScreenshotPath(filePath, baseDir string) bool {
	// 获取绝对路径
	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		return false
	}

	absBaseDir, err := filepath.Abs(baseDir)
	if err != nil {
		return false
	}

	// 检查文件路径是否在基础目录内
	return strings.HasPrefix(absFilePath, absBaseDir)
}

// isValidDownloadPath 检查文件路径是否安全
func isValidDownloadPath(filePath string) bool {
	// 清理路径
	cleanPath := filepath.Clean(filePath)

	// 检查是否包含危险的路径遍历
	if strings.Contains(cleanPath, "..") {
		return false
	}

	// 获取绝对路径
	absPath, err := filepath.Abs(cleanPath)
	if err != nil {
		return false
	}

	// 获取download和upload目录的绝对路径
	downloadDir, _ := filepath.Abs(global.CONFIG.Server.DownloadDir)
	uploadDir, _ := filepath.Abs(global.CONFIG.Server.UploadDir)

	// 检查文件是否在允许的目录内
	isInDownloadDir := strings.HasPrefix(absPath, downloadDir+string(filepath.Separator)) || absPath == downloadDir
	isInUploadDir := strings.HasPrefix(absPath, uploadDir+string(filepath.Separator)) || absPath == uploadDir

	return isInDownloadDir || isInUploadDir
}
