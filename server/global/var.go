package global

import (
	"server/config"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songzhibin97/gkit/cache/local_cache"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"golang.org/x/sync/singleflight"
	"gorm.io/gorm"
)

var (
	DB                  *gorm.DB
	CONFIG              config.Config
	VP                  *viper.Viper
	LOG                 *zap.Logger
	ROUTES              gin.RoutesInfo
	lock                sync.RWMutex
	Concurrency_Control = &singleflight.Group{}
	BlackCache          local_cache.Cache
	CLIENT_BIN_DIR      string // 客户端二进制文件存储目录
	StartTime           time.Time // 服务器启动时间

	// 响应缓存，用于存储客户端操作的响应结果
	ResponseCache      local_cache.Cache
	ResponseCacheMutex sync.RWMutex
)
