package c2

import (
	"context"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"server/core/manager/dbpool"
	"server/core/manager/downloader"
	"server/core/manager/filetransferpool"
	"server/core/manager/uploader"
	"server/factory"
	"server/global"
	"server/model/request/fs"
	"server/model/response"
	"server/model/sys"
	"server/model/task"
	"server/model/tlv"
	"server/utils"
	"strings"
	"sync"
	"time"

	"github.com/minio/sha256-simd"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type FileService struct {
	uploadDir              string
	downloadDir            string
	taskMutex              sync.RWMutex
	runningTasks           map[uint64]*FileTransferContext
	downloadSemaphore      chan struct{} // 并发下载控制
	maxConcurrentDownloads int           // 最大并发下载数
}

type FileTransferContext struct {
	ctx    context.Context
	cancel context.CancelFunc
	task   *task.FileTransferTask
}

func NewFileService() *FileService {
	uploadDir := filepath.Join(".", "upload")
	downloadDir := filepath.Join(".", "download")

	// 确保目录存在
	os.MkdirAll(uploadDir, 0755)
	os.MkdirAll(downloadDir, 0755)

	// 设置最大并发下载数
	maxConcurrent := 10

	return &FileService{
		uploadDir:              uploadDir,
		downloadDir:            downloadDir,
		runningTasks:           make(map[uint64]*FileTransferContext),
		downloadSemaphore:      make(chan struct{}, maxConcurrent),
		maxConcurrentDownloads: maxConcurrent,
	}
}

func (s *FileService) executeFileTask(task *task.FileTransferTask, req interface{}) {
	client, err := s.getOnlineClient(task.ClientID)
	if err != nil {
		utils.UpdateTaskStatus(task.ID, "failed", err.Error())
		return
	}

	// 🚀 设置TaskID到请求中
	req = setFileTaskIDToRequest(req, task.ID)
	reqBytes, err := utils.SerializerManager.Serialize(req)
	if err != nil {
		return
	}
	// 创建TLV数据包
		packet := &tlv.Packet{
			Header: &tlv.Header{
				Type: tlv.File,
			},
			PacketData: &tlv.PacketData{
				Data: reqBytes,
			},
		}
	switch task.TaskType {
	case "info_file":
		packet.Header.Code = tlv.FileInfo
	case "copy_file":
		packet.Header.Code = tlv.FileCopy
	case "delete_file":
		packet.Header.Code = tlv.FileDelete
	case "move_file":
		packet.Header.Code = tlv.FileMove
	case "upload_file":
		r := req.(fs.FileUploadToClientRequest)
		global.LOG.Info("开始上传文件", zap.String("source", task.SourcePath), zap.String("destination", task.DestinationPath))
		// 执行分块上传
		go s.executeUploadFile(task, r, client)
		return
	case "download_file":
		packet.Header.Code = tlv.FileDownload
		r := req.(fs.FileDownloadRequest)
		s.executeDownloadFile(task,r,client)
	case "read_file":
		packet.Header.Code = tlv.FileRead
	case "write_file":
		packet.Header.Code = tlv.FileWrite
	case "create_file":
		packet.Header.Code = tlv.FileCreate
	}
	if task.TaskType != "upload_file"{
		err = s.sendPacket(client, packet)
		if err != nil {
			return
		}
	}
	utils.UpdateTaskStatus(task.ID, "running", fmt.Sprintf("文件%s请求已发送",task.TaskType))
}

func (s *FileService) executeDownloadFile(task *task.FileTransferTask,req fs.FileDownloadRequest, client *sys.Client) {
	global.LOG.Info("FileDownloadRequest的TaskID是:", zap.Uint64("TaskID", req.TaskID))
	global.LOG.Info("开始下载文件", zap.String("source", req.Path), zap.String("destination", task.DestinationPath))
	existCount := 1
	OriginPath := task.DestinationPath
	for utils.FileExist(task.DestinationPath) {
		if n := strings.LastIndex(OriginPath, "."); n != -1 && strings.Count(OriginPath[:n], ".") == 0 {
			Name := OriginPath[:n]
			Ext := OriginPath[n+1:]
			task.DestinationPath = fmt.Sprintf("%v(%v).%v", Name, existCount, Ext)
			existCount++
		} else {
			task.DestinationPath = fmt.Sprintf("%v(%v)", OriginPath, existCount)
			existCount++
		}
	}
	file, err := os.OpenFile(task.DestinationPath, os.O_CREATE|os.O_RDWR|os.O_APPEND, 0644)
	if err != nil {
		global.LOG.Error("创建下载文件失败", zap.Error(err))
		utils.UpdateTaskStatus(task.ID, "failed", err.Error())
		return
	}
	state := &downloader.DownloadTaskState{
		TaskID:      task.ID,
		ClientID:    task.ClientID,
		File:        file,
		Path:        req.Path,
		DestPath:    task.DestinationPath,
		NextChunk:   req.StartChunk,
		ChunkSize:   req.ChunkSize,
		LastUpdated: time.Now(),
	}
	downloader.DownloadManager.AddTask(state)
}

// executeUploadFile 执行分块文件上传
func (s *FileService) executeUploadFile(task *task.FileTransferTask, req fs.FileUploadToClientRequest, client *sys.Client) {
	// 打开源文件
	file, err := os.Open(req.SourcePath)
	if err != nil {
		global.LOG.Error("打开源文件失败", zap.Error(err))
		utils.UpdateTaskStatus(task.ID, "failed", "打开源文件失败: "+err.Error())
		return
	}

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		file.Close()
		global.LOG.Error("获取文件信息失败", zap.Error(err))
		utils.UpdateTaskStatus(task.ID, "failed", "获取文件信息失败: "+err.Error())
		return
	}

	fileSize := fileInfo.Size()
	chunkSize := req.ChunkSize
	if chunkSize == 0 {
		chunkSize = 32 * 1024 // 默认32KB
	}

	// 计算总分块数
	totalChunks := (fileSize + chunkSize - 1) / chunkSize

	// 计算文件SHA256哈希
	fileHash, err := s.calculateSHA256(req.SourcePath)
	if err != nil {
		file.Close()
		global.LOG.Error("计算文件SHA256失败", zap.Error(err))
		utils.UpdateTaskStatus(task.ID, "failed", "计算文件SHA256失败: "+err.Error())
		return
	}

	// 创建上传任务状态并添加到管理器
	state := &uploader.UploadTaskState{
		TaskID:      task.ID,
		ClientID:    task.ClientID,
		File:        file,
		DestPath:    req.DestinationPath,
		FileSize:    fileSize,
		FileHash:    fileHash,
		ChunkSize:   chunkSize,
		TotalChunks: totalChunks,
		NextChunk:   0,
		Transferred: 0,
		LastUpdated: time.Now(),
	}
	uploader.UploadManager.AddTask(state)

	utils.UpdateTaskStatus(task.ID, "running", "开始分块上传")

	// 使用文件传输内存池读取第一个分块数据
	buf := filetransferpool.GetChunkBuffer(int(chunkSize))
	defer filetransferpool.PutChunkBuffer(buf)

	n, err := file.Read(buf)
	if err != nil && err != io.EOF {
		file.Close()
		uploader.UploadManager.RemoveTask(task.ID)
		global.LOG.Error("读取第一个分块失败", zap.Error(err))
		utils.UpdateTaskStatus(task.ID, "failed", "读取第一个分块失败: "+err.Error())
		return
	}

	// 记录传输字节数
	filetransferpool.AddTransferredBytes(int64(n))

	// 构造第一个分块请求 - 创建数据副本以避免内存池冲突
	chunkData := make([]byte, n)
	copy(chunkData, buf[:n])

	uploadReq := fs.FileUploadRequest{
		TaskID:       task.ID,
		Destination:  req.DestinationPath,
		StartChunk:   0,
		CurrentChunk: 0,
		TotalChunk:   totalChunks,
		ChunkContent: chunkData,
		Force:        req.Force,
		FileSize:     fileSize,
		FileHash:     fileHash,
	}

	// 序列化请求数据
	reqBytes, err := utils.SerializerManager.Serialize(uploadReq)
	if err != nil {
		file.Close()
		uploader.UploadManager.RemoveTask(task.ID)
		global.LOG.Error("序列化第一个分块请求失败", zap.Error(err))
		utils.UpdateTaskStatus(task.ID, "failed", "序列化第一个分块请求失败: "+err.Error())
		return
	}

	// 创建TLV数据包
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.File,
			Code: tlv.FileUpload,
		},
		PacketData: &tlv.PacketData{
			Data: reqBytes,
		},
	}

	// 发送第一个分块到客户端
	err = s.sendPacket(client, packet)
	if err != nil {
		file.Close()
		uploader.UploadManager.RemoveTask(task.ID)
		global.LOG.Error("发送第一个分块失败", zap.Error(err))
		utils.UpdateTaskStatus(task.ID, "failed", "发送第一个分块失败: "+err.Error())
		return
	}

	global.LOG.Info("已发送第一个分块，等待Agent响应",
		zap.Uint64("taskID", task.ID),
		zap.String("destination", req.DestinationPath),
		zap.Int64("totalChunks", totalChunks))
}

// GetFileInfo 获取文件信息
func (s *FileService) GetFileInfo(clientID uint, req fs.FileInfoRequest) (uint64, error) {
	task := &task.FileTransferTask{
		ClientID:   clientID,
		TaskType:   "info_file",
		SourcePath: req.Path,
		Status:     "pending",
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务，提升响应速度
	if err := dbpool.ExecuteDBOperationAsyncAndWait("file_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go s.executeFileTask(task, req)
	return task.ID, nil
}

// CopyFile 复制文件
func (s *FileService) CopyFile(clientID uint, req fs.FileCopyRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "copy_file",
		SourcePath:      req.Source,
		DestinationPath: req.Destination,
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("file_copy_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行复制任务
	go s.executeFileTask(task, req)

	return task.ID, nil
}

// DeleteFile 删除文件
func (s *FileService) DeleteFile(clientID uint, req fs.FileDeleteRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "delete_file",
		SourcePath:      req.Path,
		DestinationPath: "",
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("file_delete_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行删除任务
	go s.executeFileTask(task, req)

	return task.ID, nil
}

// MoveFile 移动文件
func (s *FileService) MoveFile(clientID uint, req fs.FileMoveRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "move_file",
		SourcePath:      req.Source,
		DestinationPath: req.Destination,
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("file_move_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行移动任务
	go s.executeFileTask(task, req)

	return task.ID, nil
}

// UploadFileToClient 上传文件到客户端
func (s *FileService) UploadFileToClient(clientID uint, req fs.FileUploadToClientRequest) (uint64, error) {
	// 检查源文件是否存在
	fileInfo, err := os.Stat(req.SourcePath)
	if err != nil {
		return 0, fmt.Errorf("源文件不存在: %v", err)
	}

	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "upload_file",
		SourcePath:      req.SourcePath,
		DestinationPath: req.DestinationPath,
		FileSize:        fileInfo.Size(),
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池创建文件传输任务
	if err = dbpool.ExecuteDBOperationAsyncAndWait("file_transfer_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行上传任务
	go s.executeFileTask(task, req)

	return task.ID, nil
}

// DownloadFileFromClient 从客户端下载文件
func (s *FileService) DownloadFileFromClient(clientID uint, req fs.FileDownloadRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "download_file",
		SourcePath:      req.Path,
		DestinationPath: filepath.Join(s.downloadDir, filepath.Base(req.Path)),
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("file_download_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 设置任务ID到请求中
	req.TaskID = task.ID

	// 异步执行下载任务
	go s.executeFileTask(task, req)

	return task.ID, nil
}

// TransferServerFileToClient 传输服务器文件到客户端
func (s *FileService) TransferServerFileToClient(clientID uint, req fs.TransferServerFileToClientRequest) (uint64, error) {
	// 构建完整的服务器文件路径
	fullServerPath := filepath.Join(s.uploadDir, req.ServerFilePath)

	// 检查文件是否存在
	fileInfo, err := os.Stat(fullServerPath)
	if err != nil {
		return 0, fmt.Errorf("服务器文件不存在: %v", err)
	}

	// 检查是否为文件（不是目录）
	if fileInfo.IsDir() {
		return 0, fmt.Errorf("不能传输目录，只能传输文件")
	}

	// 创建传输请求
	transferReq := fs.FileUploadToClientRequest{
		SourcePath:      fullServerPath,
		DestinationPath: req.DestinationPath,
		Force:           req.Force,
		ChunkSize:       req.ChunkSize,
	}

	// 如果没有指定分块大小，使用默认值
	if transferReq.ChunkSize == 0 {
		transferReq.ChunkSize = 65536 // 64KB
	}

	// 调用现有的上传方法
	taskID, err := s.UploadFileToClient(clientID, transferReq)
	if err != nil {
		return 0, fmt.Errorf("创建传输任务失败: %v", err)
	}

	global.LOG.Info("创建服务器文件传输任务",
		zap.String("serverFile", req.ServerFilePath),
		zap.String("destination", req.DestinationPath),
		zap.Uint64("taskID", taskID),
		zap.Uint("clientID", clientID))

	return taskID, nil
}

// SaveUploadedFile 保存上传的文件，支持文件去重
func (s *FileService) SaveUploadedFile(file multipart.File, filename string) (string, error) {
	// 首先计算上传文件的哈希值
	fileHash, err := utils.CalculateMultipartFileHash(file)
	if err != nil {
		return "", fmt.Errorf("计算文件哈希失败: %v", err)
	}

	// 重置文件指针到开头
	if seeker, ok := file.(io.Seeker); ok {
		if _, err := seeker.Seek(0, io.SeekStart); err != nil {
			return "", fmt.Errorf("重置文件指针失败: %v", err)
		}
	}

	// 检查上传目录中是否已存在相同哈希和文件名的文件
	existingFilePath, err := s.findExistingFile(filename, fileHash)
	if err != nil {
		return "", fmt.Errorf("检查已存在文件失败: %v", err)
	}

	// 如果找到相同的文件，直接返回已存在文件的路径
	if existingFilePath != "" {
		global.LOG.Info("发现重复文件，使用已存在的文件",
			zap.String("filename", filename),
			zap.String("hash", fileHash),
			zap.String("existing_path", existingFilePath))
		return existingFilePath, nil
	}

	// 如果没有找到重复文件，保存新文件
	ext := filepath.Ext(filename)
	name := filename[:len(filename)-len(ext)]
	timestamp := time.Now().Format("20060102150405")
	uniqueFilename := fmt.Sprintf("%s_%s%s", name, timestamp, ext)

	filePath := filepath.Join(s.uploadDir, uniqueFilename)

	// 创建目标文件
	dst, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer dst.Close()

	// 使用文件传输内存池进行文件复制
	copyBuffer := filetransferpool.GetWriteBuffer()
	defer filetransferpool.PutWriteBuffer(copyBuffer)

	bytesWritten, err := io.CopyBuffer(dst, file, copyBuffer)
	if err != nil {
		return "", err
	}

	// 记录传输字节数
	filetransferpool.AddTransferredBytes(bytesWritten)

	global.LOG.Info("保存新文件成功",
		zap.String("filename", filename),
		zap.String("hash", fileHash),
		zap.String("path", filePath))

	return filePath, nil
}

// GetUploadedFiles 获取已上传的文件列表
func (s *FileService) GetUploadedFiles() ([]response.ServerFile, error) {
	return s.getFilesInDir(s.uploadDir)
}

func (s *FileService) GetDownloadFiles() ([]response.ServerFile, error) {
	return s.getFilesInDir(s.downloadDir)
}

// GetUploadedFilesWithPath 获取上传目录指定路径下的文件列表
func (s *FileService) GetUploadedFilesWithPath(subPath string) ([]response.ServerFile, error) {
	return s.getFilesInDirWithPath(s.uploadDir, subPath)
}

// GetDownloadFilesWithPath 获取下载目录指定路径下的文件列表
func (s *FileService) GetDownloadFilesWithPath(subPath string) ([]response.ServerFile, error) {
	return s.getFilesInDirWithPath(s.downloadDir, subPath)
}

// DeleteServerFile 删除服务器本地文件
func (s *FileService) DeleteServerFile(filePath string) error {
	// 安全检查：确保文件路径在允许的目录内
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return fmt.Errorf("无法解析文件路径: %v", err)
	}

	// 检查文件是否在上传或下载目录内
	uploadDirAbs, _ := filepath.Abs(s.uploadDir)
	downloadDirAbs, _ := filepath.Abs(s.downloadDir)

	if !strings.HasPrefix(absPath, uploadDirAbs) && !strings.HasPrefix(absPath, downloadDirAbs) {
		return fmt.Errorf("不允许删除指定目录外的文件")
	}

	// 检查文件是否存在
	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在")
	}

	// 删除文件
	if err := os.Remove(absPath); err != nil {
		return fmt.Errorf("删除文件失败: %v", err)
	}

	global.LOG.Info("成功删除服务器文件", zap.String("path", absPath))
	return nil
}

// DeleteServerFiles 批量删除服务器本地文件
func (s *FileService) DeleteServerFiles(filePaths []string) error {
	var errors []string

	for _, filePath := range filePaths {
		if err := s.DeleteServerFile(filePath); err != nil {
			errors = append(errors, fmt.Sprintf("%s: %v", filePath, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分文件删除失败: %s", strings.Join(errors, "; "))
	}

	return nil
}

// GetFileTransferTasks 获取文件传输任务列表
func (s *FileService) GetFileTransferTasks() ([]task.FileTransferTask, error) {
	var tasks []task.FileTransferTask
	// 🚀 使用数据库连接池进行查询操作
	err := dbpool.ExecuteDBOperationAsyncAndWait("file_tasks_query", func(db *gorm.DB) error {
		return db.Order("created_at desc").Find(&tasks).Error
	})
	return tasks, err
}

// GetFileTransferTask 获取单个文件传输任务
func (s *FileService) GetFileTransferTask(taskID uint64) (*task.FileTransferTask, error) {
	var task task.FileTransferTask
	// 🚀 使用数据库连接池进行查询操作
	err := dbpool.ExecuteDBOperationAsyncAndWait("file_task_get", func(db *gorm.DB) error {
		return db.Where("id = ?", taskID).First(&task).Error
	})
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// CancelFileTransferTask 取消文件传输任务
func (s *FileService) CancelFileTransferTask(taskID uint64) error {
	s.taskMutex.Lock()
	defer s.taskMutex.Unlock()

	// --- 新增逻辑：通知Agent取消 ---
	// 检查下载任务
	downloadState, downloadExists := downloader.DownloadManager.GetTask(taskID)
	if downloadExists {
		// 获取与此任务关联的clientID
		clientID := downloadState.ClientID
		client, err := s.getOnlineClient(clientID)
		if err == nil {
			global.LOG.Info("正在向Agent发送下载取消任务信令", zap.Uint64("taskID", taskID), zap.Uint("clientID", clientID))

			// 构建取消请求包
			cancelReq := fs.FileDownloadRequest{
				TaskID:     taskID,
				StartChunk: -1,                 // 使用-1作为取消信令
				Path:       downloadState.Path, // Path 字段最好也带上，用于Agent确认
			}
			reqBytes, _ := utils.SerializerManager.Serialize(cancelReq)

			cancelPacket := &tlv.Packet{
				Header: &tlv.Header{
					Type: tlv.File,
					Code: tlv.FileDownload,
				},
				PacketData: &tlv.PacketData{
					Data: reqBytes,
				},
			}
			// 发送取消包，如果失败也只是记录日志，不影响Server端的取消流程
			if err := s.sendPacket(client, cancelPacket); err != nil {
				global.LOG.Warn("向Agent发送下载取消信令失败", zap.Error(err), zap.Uint64("taskID", taskID))
			}
		} else {
			global.LOG.Warn("无法找到在线客户端以发送下载取消信令", zap.Uint64("taskID", taskID), zap.Uint("clientID", clientID))
		}
	}

	// 检查上传任务
	uploadState, uploadExists := uploader.UploadManager.GetTask(taskID)
	if uploadExists {
		// 获取与此任务关联的clientID
		clientID := uploadState.ClientID
		client, err := s.getOnlineClient(clientID)
		if err == nil {
			global.LOG.Info("正在向Agent发送上传取消任务信令", zap.Uint64("taskID", taskID), zap.Uint("clientID", clientID))

			// 构建取消请求包
			cancelReq := fs.FileUploadRequest{
				TaskID:     taskID,
				StartChunk: -1, // 使用-1作为取消信令
			}
			reqBytes, _ := utils.SerializerManager.Serialize(cancelReq)

			cancelPacket := &tlv.Packet{
				Header: &tlv.Header{
					Type: tlv.File,
					Code: tlv.FileUpload,
				},
				PacketData: &tlv.PacketData{
					Data: reqBytes,
				},
			}
			// 发送取消包，如果失败也只是记录日志，不影响Server端的取消流程
			if err := s.sendPacket(client, cancelPacket); err != nil {
				global.LOG.Warn("向Agent发送上传取消信令失败", zap.Error(err), zap.Uint64("taskID", taskID))
			}
		} else {
			global.LOG.Warn("无法找到在线客户端以发送上传取消信令", zap.Uint64("taskID", taskID), zap.Uint("clientID", clientID))
		}
	}
	// --- 新增逻辑结束 ---

	// 取消正在运行的任务
	if ctx, exists := s.runningTasks[taskID]; exists {
		ctx.cancel()
		delete(s.runningTasks, taskID)
	}

	// 从DownloadManager中移除任务
	// 这句要放在发送取消信令之后，以确保能获取到state信息
	if _, exists := downloader.DownloadManager.GetTask(taskID); exists {
		downloader.DownloadManager.RemoveTask(taskID)
	}

	// 从UploadManager中移除任务
	if _, exists := uploader.UploadManager.GetTask(taskID); exists {
		uploader.UploadManager.RemoveTask(taskID)
	}

	// 🚀 更新数据库状态
	return dbpool.ExecuteDBOperationAsyncAndWait("file_transfer_task_cancel", func(db *gorm.DB) error {
		return db.Model(&task.FileTransferTask{}).Where("id = ?", taskID).Updates(map[string]interface{}{
			"status":     "cancelled",
			"updated_at": time.Now(),
		}).Error
	})
}

// 辅助方法
func (s *FileService) getOnlineClient(clientID uint) (*sys.Client, error) {
	var client sys.Client
	// 🚀 使用数据库连接池查询在线客户端
	if err := dbpool.ExecuteDBOperationAsyncAndWait("online_client_query", func(db *gorm.DB) error {
		return db.Where("id = ? AND status = ?", clientID, 1).First(&client).Error
	}); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("客户端不存在或不在线")
		}
		return nil, err
	}
	return &client, nil
}

func (s *FileService) sendPacket(client *sys.Client, packet *tlv.Packet) error {
	// 使用factory发送文件数据包到客户端
	return factory.SendPacketFactory(*client, packet)
}

func (s *FileService) getFilesInDir(dir string) ([]response.ServerFile, error) {
	return s.getFilesInDirWithPath(dir, "")
}

// getFilesInDirWithPath 获取指定路径下的文件列表（只返回直接子项）
func (s *FileService) getFilesInDirWithPath(baseDir, subPath string) ([]response.ServerFile, error) {
	var files []response.ServerFile

	// 构建完整路径
	fullPath := filepath.Join(baseDir, subPath)

	// 确保路径在基础目录内（安全检查）
	absBasePath, err := filepath.Abs(baseDir)
	if err != nil {
		return nil, fmt.Errorf("无法解析基础路径: %v", err)
	}

	absFullPath, err := filepath.Abs(fullPath)
	if err != nil {
		return nil, fmt.Errorf("无法解析目标路径: %v", err)
	}

	if !strings.HasPrefix(absFullPath, absBasePath) {
		return nil, fmt.Errorf("路径不在允许的目录范围内")
	}

	// 检查目录是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("目录不存在: %s", fullPath)
	}

	// 读取目录内容（只读取直接子项）
	entries, err := os.ReadDir(fullPath)
	if err != nil {
		return nil, fmt.Errorf("读取目录失败: %v", err)
	}

	for _, entry := range entries {
		itemPath := filepath.Join(fullPath, entry.Name())
		info, err := entry.Info()
		if err != nil {
			continue // 跳过无法获取信息的项目
		}

		// 计算相对路径
		relPath := filepath.Join(subPath, entry.Name())
		if subPath == "" {
			relPath = entry.Name()
		}

		files = append(files, response.ServerFile{
			Name:         info.Name(),
			Path:         itemPath,
			Size:         info.Size(),
			ModTime:      info.ModTime(),
			IsDir:        info.IsDir(),
			RelativePath: relPath,
		})
	}

	return files, nil
}

func (s *FileService) calculateSHA256(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := sha256.New()
	if _, err = io.Copy(hash, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// findExistingFile 查找上传目录中是否存在相同哈希和文件名的文件
func (s *FileService) findExistingFile(filename, targetHash string) (string, error) {
	// 获取原始文件名（不包含扩展名）
	ext := filepath.Ext(filename)
	baseName := filename[:len(filename)-len(ext)]

	// 遍历上传目录中的所有文件
	err := filepath.Walk(s.uploadDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查文件名是否匹配（忽略时间戳部分）
		existingFilename := info.Name()
		existingExt := filepath.Ext(existingFilename)
		existingBaseName := existingFilename[:len(existingFilename)-len(existingExt)]

		// 检查扩展名是否相同
		if existingExt != ext {
			return nil
		}

		// 检查基础文件名是否匹配（可能包含时间戳）
		// 格式：原文件名_时间戳.扩展名
		if !strings.HasPrefix(existingBaseName, baseName+"_") && existingBaseName != baseName {
			return nil
		}

		// 计算现有文件的哈希值
		existingHash, err := utils.CalculateFileHash(path)
		if err != nil {
			// 如果计算哈希失败，记录日志但继续检查其他文件
			global.LOG.Warn("计算已存在文件哈希失败",
				zap.String("path", path),
				zap.Error(err))
			return nil
		}

		// 如果哈希值相同，说明找到了重复文件
		if existingHash == targetHash {
			// 使用一个特殊的错误来传递找到的文件路径
			return fmt.Errorf("FOUND:%s", path)
		}

		return nil
	})

	// 检查是否找到了重复文件
	if err != nil && strings.HasPrefix(err.Error(), "FOUND:") {
		foundPath := strings.TrimPrefix(err.Error(), "FOUND:")
		return foundPath, nil
	}

	// 如果有其他错误，返回错误
	if err != nil {
		return "", err
	}

	// 没有找到重复文件
	return "", nil
}

// ReadFileContent 读取文件内容
func (s *FileService) ReadFileContent(clientID uint, req fs.FileReadRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:   clientID,
		TaskType:   "read_file",
		SourcePath: req.Path,
		Status:     "pending",
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("file_read_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行读取任务
	go s.executeFileTask(task, req)

	return task.ID, nil
}

// WriteFileContent 写入文件内容
func (s *FileService) WriteFileContent(clientID uint, req fs.FileWriteRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "write_file",
		SourcePath:      req.Path,
		DestinationPath: req.Path,
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("file_write_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行写入任务
	go s.executeFileTask(task, req)

	return task.ID, nil
}

// CreateFile 创建文件
func (s *FileService) CreateFile(clientID uint, req fs.FileCreateRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "create_file",
		SourcePath:      req.Path,
		DestinationPath: req.Path,
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("file_create_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行创建文件任务
	go s.executeFileTask(task, req)

	return task.ID, nil
}

// setFileTaskIDToRequest 设置TaskID到文件请求中（通用辅助函数）
func setFileTaskIDToRequest(req interface{}, taskID uint64) interface{} {
	switch r := req.(type) {
	case *fs.FileInfoRequest:
		r.TaskID = taskID
		return r
	case *fs.FileCopyRequest:
		r.TaskID = taskID
		return r
	case *fs.FileDeleteRequest:
		r.TaskID = taskID
		return r
	case *fs.FileMoveRequest:
		r.TaskID = taskID
		return r
	case *fs.FileUploadToClientRequest:
		r.TaskID = taskID
		return r
	case *fs.FileDownloadRequest:
		r.TaskID = taskID
		return r
	case *fs.FileReadRequest:
		r.TaskID = taskID
		return r
	case *fs.FileWriteRequest:
		r.TaskID = taskID
		return r
	case *fs.FileCreateRequest:
		r.TaskID = taskID
		return r
	case fs.FileInfoRequest:
		r.TaskID = taskID
		return r
	case fs.FileCopyRequest:
		r.TaskID = taskID
		return r
	case fs.FileDeleteRequest:
		r.TaskID = taskID
		return r
	case fs.FileMoveRequest:
		r.TaskID = taskID
		return r
	case fs.FileUploadToClientRequest:
		r.TaskID = taskID
		return r
	case fs.FileDownloadRequest:
		r.TaskID = taskID
		return r
	case fs.FileReadRequest:
		r.TaskID = taskID
		return r
	case fs.FileWriteRequest:
		r.TaskID = taskID
		return r
	case fs.FileCreateRequest:
		r.TaskID = taskID
		return r
	default:
		global.LOG.Error("未知的文件请求类型", zap.Any("req", req))
		return req
	}
}
