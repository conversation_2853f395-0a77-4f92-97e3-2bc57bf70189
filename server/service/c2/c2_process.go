package c2

import (
	"errors"
	"fmt"
	"server/core/manager/dbpool"
	"server/factory"
	"server/model/request/proc"
	"server/model/sys"
	"server/model/task"
	"server/model/tlv"
	"server/utils"

	"gorm.io/gorm"
)

type ProcessService struct{}

func NewProcessService() *ProcessService {
	return &ProcessService{}
}

// ListProc 获取进程列表
func (p *ProcessService) ListProc(clientID uint, req proc.ProcessListRequest) (uint64, error) {
	task := &task.ProcessTask{
		ClientID: clientID,
		TaskType: "list_process",
		Status:   "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("process_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go p.executeProcessTask(task, req)
	return task.ID, nil
}

// KillProcess 终止进程
func (p *ProcessService) KillProcess(clientID uint, req proc.ProcessKillRequest) (uint64, error) {
	task := &task.ProcessTask{
		ClientID:  clientID,
		TaskType:  "kill_process",
		TargetPID: req.PID,
		Status:    "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("process_kill_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go p.executeProcessTask(task, req)
	return task.ID, nil
}

// StartProcess 启动进程
func (p *ProcessService) StartProcess(clientID uint, req proc.ProcessStartRequest) (uint64, error) {
	task := &task.ProcessTask{
		ClientID: clientID,
		TaskType: "start_process",
		Status:   "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("process_start_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go p.executeProcessTask(task, req)
	return task.ID, nil
}

// GetProcessDetails 获取进程详情
func (p *ProcessService) GetProcessDetails(clientID uint, req proc.ProcessDetailsRequest) (uint64, error) {
	task := &task.ProcessTask{
		ClientID:  clientID,
		TaskType:  "get_process_details",
		TargetPID: req.PID,
		Status:    "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("process_details_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go p.executeProcessTask(task, req)
	return task.ID, nil
}

// SuspendProcess 挂起进程
func (p *ProcessService) SuspendProcess(clientID uint, req proc.ProcessSuspendRequest) (uint64, error) {
	task := &task.ProcessTask{
		ClientID:  clientID,
		TaskType:  "suspend_process",
		TargetPID: req.PID,
		Status:    "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("process_suspend_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go p.executeProcessTask(task, req)
	return task.ID, nil
}

// ResumeProcess 恢复进程
func (p *ProcessService) ResumeProcess(clientID uint, req proc.ProcessResumeRequest) (uint64, error) {
	task := &task.ProcessTask{
		ClientID:  clientID,
		TaskType:  "resume_process",
		TargetPID: req.PID,
		Status:    "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("process_resume_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go p.executeProcessTask(task, req)
	return task.ID, nil
}

// executeProcessTask 执行进程任务
func (p *ProcessService) executeProcessTask(task *task.ProcessTask, req interface{}) {
	client, err := p.getOnlineClient(task.ClientID)
	if err != nil {
		p.updateTaskStatus(task.ID, "failed", err.Error())
		return
	}

	// 🚀 设置TaskID到请求中
	req = setProcessTaskIDToRequest(req, task.ID)
	reqBytes, err := utils.SerializerManager.Serialize(req)
	if err != nil {
		p.updateTaskStatus(task.ID, "failed", err.Error())
		return
	}
	// 创建TLV数据包
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.Process,
		},
		PacketData: &tlv.PacketData{
			Data: reqBytes,
		},
	}
	switch task.TaskType {
	case "list_process":
		// 发送到客户端
		packet.Header.Code = tlv.ProcessList
	case "kill_process":
		packet.Header.Code = tlv.ProcessKill
	case "start_process":
		packet.Header.Code = tlv.ProcessStart
	case "get_process_details":
		packet.Header.Code = tlv.ProcessDetails
	case "suspend_process":
		packet.Header.Code = tlv.ProcessSuspend
	case "resume_process":
		packet.Header.Code = tlv.ProcessResume
	}

	err = p.sendPacket(client, packet)
	if err != nil {
		p.updateTaskStatus(task.ID, "failed", err.Error())
		return
	}
	p.updateTaskStatus(task.ID, "running", fmt.Sprintf("进程%s请求已发送",task.TaskType))
}

// getOnlineClient 获取在线客户端
func (p *ProcessService) getOnlineClient(clientID uint) (*sys.Client, error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	err := dbpool.ExecuteDBOperationAsyncAndWait("client_online_check", func(db *gorm.DB) error {
		return db.Where("id = ? AND status = ?", clientID, 1).First(&client).Error
	})

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("客户端不在线或不存在")
		}
		return nil, err
	}
	return &client, nil
}

// sendPacket 发送数据包到客户端
func (p *ProcessService) sendPacket(client *sys.Client, packet *tlv.Packet) error {
	// 获取客户端连接并发送数据包
	return factory.SendPacketFactory(*client, packet)
}

// updateTaskStatus 更新任务状态
func (p *ProcessService) updateTaskStatus(taskID uint64, status, errorMsg string) {
	utils.UpdateProcessTaskStatus(taskID, status, errorMsg)
}

// setProcessTaskIDToRequest 设置TaskID到进程请求中（通用辅助函数）
func setProcessTaskIDToRequest(req interface{}, taskID uint64) interface{} {
	switch r := req.(type) {
	case *proc.ProcessListRequest:
		r.TaskID = taskID
		return r
	case *proc.ProcessKillRequest:
		r.TaskID = taskID
		return r
	case *proc.ProcessStartRequest:
		r.TaskID = taskID
		return r
	case *proc.ProcessDetailsRequest:
		r.TaskID = taskID
		return r
	case *proc.ProcessSuspendRequest:
		r.TaskID = taskID
		return r
	case *proc.ProcessResumeRequest:
		r.TaskID = taskID
		return r
	case proc.ProcessListRequest:
		r.TaskID = taskID
		return r
	case proc.ProcessKillRequest:
		r.TaskID = taskID
		return r
	case proc.ProcessStartRequest:
		r.TaskID = taskID
		return r
	case proc.ProcessDetailsRequest:
		r.TaskID = taskID
		return r
	case proc.ProcessSuspendRequest:
		r.TaskID = taskID
		return r
	case proc.ProcessResumeRequest:
		r.TaskID = taskID
		return r
	default:
		return req
	}
}
