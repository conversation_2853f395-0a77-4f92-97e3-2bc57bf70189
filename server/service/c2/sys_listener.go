package c2

import (
	"errors"
	"fmt"
	"net"
	"strconv"
	"strings"
	"server/core/manager/dbpool"
	"server/core/manager/workerpool"
	"server/factory"
	"server/global"
	"server/model/request"
	"server/model/sys"
	sysService "server/service/sys"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ListenerService struct{}

// CreateListener 创建监听器
func (s *ListenerService) CreateListener(listener sys.Listener) (err error) {
	// 🚀 使用数据库连接池进行事务操作，确保监听器创建的原子性
	return dbpool.ExecuteDBTransaction("listener_create", func(db *gorm.DB) error {
		// 检查数据库中是否已存在相同的本地监听地址
		var count int64
		if err := db.Model(&sys.Listener{}).Where("local_listen_addr = ?", listener.LocalListenAddr).Count(&count).Error; err != nil {
			return err
		}
		if count > 0 {
			return errors.New("该监听地址已存在于数据库中")
		}

		// 检查系统中是否已有程序占用该端口
		if listener.Type != "pipe" { // pipe类型不需要检查端口占用
			// 尝试监听该地址，检查端口是否被占用
			l, err := net.Listen("tcp", listener.LocalListenAddr)
			if err != nil {
				return errors.New("该监听地址已被系统占用: " + err.Error())
			}
			// 关闭监听，释放端口
			l.Close()
		}

		// 创建监听器记录
		if err := db.Create(&listener).Error; err != nil {
			return err
		}

		// 🚀 如果监听器状态为启用，则异步启动监听器
		if listener.Status == 1 {
			// 异步启动监听器，避免阻塞事务
			go func() {
				factory.UpdateListenerFactory(&listener)
			}()
		}

		// 🔔 发送监听器创建通知
		s.sendListenerCreatedNotification(listener)

		return nil
	})
}

// DeleteListener 删除监听器
func (s *ListenerService) DeleteListener(id uint) (err error) {
	// 🚀 使用数据库连接池进行事务操作，确保删除的原子性
	return dbpool.ExecuteDBTransaction("listener_delete", func(db *gorm.DB) error {
		var listener sys.Listener
		if err := db.Where("id = ?", id).First(&listener).Error; err != nil {
			return errors.New("未找到监听器")
		}

		// 先停止监听器
		factory.StopListenerFactory(&listener)

		// 🔔 发送监听器删除通知
		s.sendListenerDeletedNotification(listener)

		// 删除数据库记录
		return db.Delete(&listener).Error
	})
}

// UpdateListener 更新监听器
func (s *ListenerService) UpdateListener(listener sys.Listener) (err error) {
	var oldListener sys.Listener

	// 🚀 使用数据库连接池进行事务操作，确保更新的原子性
	err = dbpool.ExecuteDBTransaction("listener_update", func(db *gorm.DB) error {
		// 1. 查询旧记录（加锁防止并发修改）
		if err := db.Set("gorm:query_option", "FOR UPDATE").
			Where("id = ?", listener.ID).
			First(&oldListener).Error; err != nil {
			return errors.New("未找到监听器")
		}

		// 如果本地监听地址发生变化，需要检查新地址是否可用
		if listener.LocalListenAddr != oldListener.LocalListenAddr {
			// 检查数据库中是否已存在相同的本地监听地址（排除当前记录）
			var count int64
			if err := db.Model(&sys.Listener{}).Where("local_listen_addr = ? AND id != ?", listener.LocalListenAddr, listener.ID).Count(&count).Error; err != nil {
				return err
			}
			if count > 0 {
				return errors.New("该监听地址已存在于数据库中")
			}

			// 检查系统中是否已有程序占用该端口
			if listener.Type != "pipe" { // pipe类型不需要检查端口占用
				// 尝试监听该地址，检查端口是否被占用
				l, err := net.Listen("tcp", listener.LocalListenAddr)
				if err != nil {
					return errors.New("该监听地址已被系统占用: " + err.Error())
				}
				// 关闭监听，释放端口
				l.Close()
			}
		}

		// 更新监听器记录
		return db.Model(&sys.Listener{}).
			Where("id = ?", listener.ID).
			Updates(listener).Error
	})

	if err != nil {
		return err
	}

	// 🚀 异步操作监听器，避免阻塞响应
	go func() {
		factory.UpdateListenerFactory(&listener) // 启动新监听
		factory.StopListenerFactory(&oldListener) // 停止旧监听
	}()

	return nil
}

// 🔔 发送监听器创建通知
func (s *ListenerService) sendListenerCreatedNotification(listener sys.Listener) {
		// 🚀 使用工作池异步发送通知，避免阻塞主流程
		task := workerpool.NewGeneralTask("listener_created_notification", func() error {
			notificationService := &sysService.NotificationService{}

			// 创建通知数据
			listenerID := listener.ID
			listenerName := listener.Remark
			if listenerName == "" {
				listenerName = fmt.Sprintf("%s监听器", listener.Type)
			}
			listenerPort := uint16(0)
			// 从地址中提取端口
			if listener.LocalListenAddr != "" {
				// 解析端口，假设格式为 host:port
				if colonIndex := strings.LastIndex(listener.LocalListenAddr, ":"); colonIndex != -1 {
					portStr := listener.LocalListenAddr[colonIndex+1:]
					if port, err := strconv.ParseUint(portStr, 10, 16); err == nil {
						listenerPort = uint16(port)
					}
				}
			}

			data := sys.NotificationData{
				ListenerID:   &listenerID,
				ListenerName: &listenerName,
				ListenerPort: &listenerPort,
				ExtraInfo: map[string]interface{}{
					"type":                listener.Type,
					"local_listen_addr":   listener.LocalListenAddr,
					"remote_connect_addr": listener.RemoteConnectAddr,
					"status":              listener.Status,
				},
			}

			// 创建通知请求
			notificationReq := request.NotificationCreateRequest{
				UserID:  listener.UserID,  // 🚨 关键修复：发送给监听器创建者
				Type:    sys.NotificationListenerCreated,
				Level:   sys.NotificationLevelInfo,
				Title:   "监听器创建",
				Content: fmt.Sprintf("监听器 %s (%s) 已创建", listenerName, listener.LocalListenAddr),
				Data:    data,
			}

			// 发送通知
			if err := notificationService.CreateNotification(notificationReq); err != nil {
				global.LOG.Error("发送监听器创建通知失败",
					zap.Uint("listener_id", listener.ID),
					zap.Error(err))
				return err
			} else {
				global.LOG.Debug("监听器创建通知发送成功",
					zap.Uint("listener_id", listener.ID),
					zap.String("name", listenerName))
				return nil
			}
		})

		workerpool.SubmitGeneralTask(task)
	}

// 🔔 发送监听器删除通知
func (s *ListenerService) sendListenerDeletedNotification(listener sys.Listener) {
	// 🚀 使用工作池异步发送通知，避免阻塞主流程
	task := workerpool.NewGeneralTask("listener_deleted_notification", func() error {
		notificationService := &sysService.NotificationService{}

		// 创建通知数据
		listenerID := listener.ID
		listenerName := listener.Remark
		if listenerName == "" {
			listenerName = fmt.Sprintf("%s监听器", listener.Type)
		}
		listenerPort := uint16(0)

		data := sys.NotificationData{
			ListenerID:   &listenerID,
			ListenerName: &listenerName,
			ListenerPort: &listenerPort,
			ExtraInfo: map[string]interface{}{
				"type":                listener.Type,
				"local_listen_addr":   listener.LocalListenAddr,
				"remote_connect_addr": listener.RemoteConnectAddr,
			},
		}

		// 创建通知请求
		notificationReq := request.NotificationCreateRequest{
			UserID:  listener.UserID,  // 🚨 关键修复：发送给监听器创建者
			Type:    sys.NotificationListenerDeleted,
			Level:   sys.NotificationLevelWarning,
			Title:   "监听器删除",
			Content: fmt.Sprintf("监听器 %s (%s) 已删除", listenerName, listener.LocalListenAddr),
			Data:    data,
		}

		// 发送通知
		if err := notificationService.CreateNotification(notificationReq); err != nil {
			global.LOG.Error("发送监听器删除通知失败",
				zap.Uint("listener_id", listener.ID),
				zap.Error(err))
			return err
		} else {
			global.LOG.Debug("监听器删除通知发送成功",
				zap.Uint("listener_id", listener.ID),
				zap.String("name", listenerName))
			return nil
		}
	})

	workerpool.SubmitGeneralTask(task)
}

// 🔔 发送监听器关闭通知
func (s *ListenerService) sendListenerClosedNotification(listener sys.Listener) {
	// 🚀 使用工作池异步发送通知，避免阻塞主流程
	task := workerpool.NewGeneralTask("listener_closed_notification", func() error {
		notificationService := &sysService.NotificationService{}

		// 创建通知数据
		listenerID := listener.ID
		listenerName := listener.Remark
		if listenerName == "" {
			listenerName = fmt.Sprintf("%s监听器", listener.Type)
		}
		listenerPort := uint16(0)

		data := sys.NotificationData{
			ListenerID:   &listenerID,
			ListenerName: &listenerName,
			ListenerPort: &listenerPort,
			ExtraInfo: map[string]interface{}{
				"type":                listener.Type,
				"local_listen_addr":   listener.LocalListenAddr,
				"remote_connect_addr": listener.RemoteConnectAddr,
			},
		}

		// 创建通知请求
		notificationReq := request.NotificationCreateRequest{
			Type:    sys.NotificationListenerClosed,
			Level:   sys.NotificationLevelWarning,
			Title:   "监听器关闭",
			Content: fmt.Sprintf("监听器 %s (%s) 已关闭", listenerName, listener.LocalListenAddr),
			Data:    data,
		}

		// 发送通知
		if err := notificationService.CreateNotification(notificationReq); err != nil {
			global.LOG.Error("发送监听器关闭通知失败",
				zap.Uint("listener_id", listener.ID),
				zap.Error(err))
			return err
		} else {
			global.LOG.Debug("监听器关闭通知发送成功",
				zap.Uint("listener_id", listener.ID),
				zap.String("name", listenerName))
			return nil
		}
	})

	workerpool.SubmitGeneralTask(task)
}

// GetListener 获取单个监听器
func (s *ListenerService) GetListener(id uint) (listener sys.Listener, err error) {
	// 🚀 使用数据库连接池进行查询操作
	err = dbpool.ExecuteDBOperationAsyncAndWait("listener_get", func(db *gorm.DB) error {
		if err := db.Where("id = ?", id).First(&listener).Error; err != nil {
			return errors.New("未找到监听器")
		}
		return nil
	})
	return listener, err
}

// GetListenerList 获取监听器列表
func (s *ListenerService) GetListenerList(info sys.Listener, pageSize int, pageNum int) (list []sys.Listener, total int64, err error) {
	limit := pageSize
	offset := pageSize * (pageNum - 1)

	// 🚀 使用数据库连接池进行查询操作
	err = dbpool.ExecuteDBOperationAsyncAndWait("listener_list_query", func(db *gorm.DB) error {
		query := db.Model(&sys.Listener{})

		// 添加查询条件
		if info.Type != "" {
			query = query.Where("type = ?", info.Type)
		}
		if info.Status != 0 {
			query = query.Where("status = ?", info.Status)
		}
		if info.Remark != "" {
			query = query.Where("remark LIKE ?", "%"+info.Remark+"%")
		}
		if info.LocalListenAddr != "" {
			query = query.Where("local_listen_addr LIKE ?", "%"+info.LocalListenAddr+"%")
		}
		if info.RemoteConnectAddr != "" {
			query = query.Where("remote_connect_addr LIKE ?", "%"+info.RemoteConnectAddr+"%")
		}

		// 获取总数
		if err := query.Count(&total).Error; err != nil {
			return err
		}

		// 获取列表
		return query.Limit(limit).Offset(offset).Order("id desc").Find(&list).Error
	})

	return list, total, err
}

// UpdateListenerStatus 更新监听器状态
func (s *ListenerService) UpdateListenerStatus(id uint, status int) (err error) {
	var listener sys.Listener

	// 🚀 使用数据库连接池进行异步更新操作，提升响应速度
	err = dbpool.ExecuteDBOperationAsyncAndWait("listener_status_update", func(db *gorm.DB) error {
		if err := db.Where("id = ?", id).First(&listener).Error; err != nil {
			return errors.New("未找到监听器")
		}

		// 更新状态
		listener.Status = status
		if err := db.Model(&listener).Update("status", status).Error; err != nil {
			return err
		}

		// 异步操作监听器
		go func() {
			factory.UpdateListenerFactory(&listener)
		}()

		// 🔔 发送监听器状态变更通知
		if status == 0 {
			s.sendListenerClosedNotification(listener)
		}

		return nil
	})

	return err
}
