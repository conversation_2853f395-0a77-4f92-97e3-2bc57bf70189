package c2

import (
	"errors"
	"fmt"
	"server/core/manager/dbpool"
	"server/core/manager/events"
	"server/core/manager/workerpool"
	"server/factory"
	"server/global"
	"server/model/request"
	"server/model/sys"
	sysService "server/service/sys"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ClientService struct{}

// InitClientEventHandlers 初始化客户端事件处理器
func (s *ClientService) InitClientEventHandlers() {
	if events.GlobalClientEventManager == nil {
		global.LOG.Error("🚨 客户端事件管理器未初始化")
		return
	}

	global.LOG.Info("🔧 开始初始化客户端事件处理器...")

	// 订阅客户端上线事件
	events.GlobalClientEventManager.Subscribe(events.ClientOnlineEvent, func(event events.ClientEvent) {
		global.LOG.Info("🔔 收到客户端上线事件",
			zap.Uint("client_id", event.Client.ID),
			zap.String("hostname", event.Client.Hostname),
			zap.String("remote_addr", event.Client.RemoteAddr))
		s.SendClientOnlineNotification(*event.Client)
	})

	// 订阅客户端离线事件
	events.GlobalClientEventManager.Subscribe(events.ClientOfflineEvent, func(event events.ClientEvent) {
		global.LOG.Info("🔔 收到客户端离线事件",
			zap.Uint("client_id", event.Client.ID),
			zap.String("hostname", event.Client.Hostname))
		s.SendClientOfflineNotification(*event.Client)
	})

	// 订阅客户端删除事件
	events.GlobalClientEventManager.Subscribe(events.ClientDeletedEvent, func(event events.ClientEvent) {
		global.LOG.Info("🔔 收到客户端删除事件",
			zap.Uint("client_id", event.Client.ID),
			zap.String("hostname", event.Client.Hostname))
		s.SendClientDeletedNotification(*event.Client)
	})

	global.LOG.Info("✅ 客户端事件处理器初始化完成")
}

// GetClientList 获取客户端列表
func (s *ClientService) GetClientList(info sys.Client, pageSize int, pageNum int, statusFilter *int) (list []sys.Client, total int64, err error) {
	limit := pageSize
	offset := pageSize * (pageNum - 1)

	// 🚀 使用数据库连接池进行查询操作，客户端列表查询频繁，保持同步以确保实时性
	err = dbpool.ExecuteDBOperationAsyncAndWait("client_list_query", func(db *gorm.DB) error {
		query := db.Model(&sys.Client{})

		// 添加查询条件
		if info.ListenerID != 0 {
			query = query.Where("listener_id = ?", info.ListenerID)
		}
		if info.ListenerType != "" {
			query = query.Where("listener_type = ?", info.ListenerType)
		}
		// 修复状态查询逻辑，使用指针来区分是否设置了状态过滤
		if statusFilter != nil {
			query = query.Where("status = ?", *statusFilter)
		}
		if info.RemoteAddr != "" {
			query = query.Where("remote_addr LIKE ?", "%"+info.RemoteAddr+"%")
		}
		if info.Remark != "" {
			query = query.Where("remark LIKE ?", "%"+info.Remark+"%")
		}

		// 获取总数
		if err := query.Count(&total).Error; err != nil {
			return err
		}

		// 获取列表
		return query.Limit(limit).Offset(offset).Order("id desc").Find(&list).Error
	})

	return list, total, err
}

// GetClient 获取单个客户端
func (s *ClientService) GetClient(id uint) (client sys.Client, err error) {
	// 🚀 使用数据库连接池进行查询操作
	err = dbpool.ExecuteDBOperationAsyncAndWait("client_get", func(db *gorm.DB) error {
		if err := db.Where("id = ?", id).First(&client).Error; err != nil {
			return errors.New(fmt.Sprintf("未找到客户端, id: %d", id))
		}
		return nil
	})
	return client, err
}

// UpdateClientRemark 更新客户端备注
func (s *ClientService) UpdateClientRemark(id uint, remark string) (err error) {
	// 🚀 使用数据库连接池进行异步更新操作，提升响应速度
	return dbpool.ExecuteDBOperationAsyncAndWait("client_update_remark", func(db *gorm.DB) error {
		var client sys.Client
		if err := db.Where("id = ?", id).First(&client).Error; err != nil {
			return errors.New("未找到客户端")
		}

		return db.Model(&client).Update("remark", remark).Error
	})
}

// DeleteClient 删除客户端
func (s *ClientService) DeleteClient(id uint) (err error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	if err = dbpool.ExecuteDBOperationAsyncAndWait("client_delete_query", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&client).Error
	}); err != nil {
		return errors.New("未找到客户端")
	}

	// 使用事务确保数据一致性
	err = factory.DeleteClientFactory(client)
	if err != nil {
		return err
	}

	// 🔔 发送客户端删除通知
	s.SendClientDeletedNotification(client)

	return nil
}

// DisconnectClient 断开客户端连接
func (s *ClientService) DisconnectClient(id uint) (err error) {
	var client sys.Client
	// 🚀 使用数据库连接池查询客户端
	if err = dbpool.ExecuteDBOperationAsyncAndWait("client_delete_query_final", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&client).Error
	}); err != nil {
		return errors.New("未找到客户端")
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return errors.New("客户端不在线")
	}

	// 根据监听器类型断开连接
	err = factory.DisconnectClientFactory(client)
	if err != nil {
		return err
	}

	// 🔔 发送客户端离线通知
	s.SendClientOfflineNotification(client)

	return nil
}

// ClearOfflineClients 清除所有离线客户端
func (s *ClientService) ClearOfflineClients() (count int64, err error) {
	// 查询所有离线客户端
	var offlineClients []sys.Client
	// 🚀 使用数据库连接池查询离线客户端
	if err = dbpool.ExecuteDBOperationAsyncAndWait("offline_clients_query", func(db *gorm.DB) error {
		return db.Where("status = ?", 0).Find(&offlineClients).Error
	}); err != nil {
		return 0, err
	}

	count = int64(len(offlineClients))
	if count == 0 {
		return 0, nil
	}

	// 批量删除离线客户端
	for _, client := range offlineClients {
		// 使用工厂方法删除客户端，确保清理相关资源
		if deleteErr := factory.DeleteClientFactory(client); deleteErr != nil {
			// 记录错误但继续删除其他客户端
			global.LOG.Error("删除离线客户端失败", zap.Uint("client_id", client.ID), zap.Error(deleteErr))
		}
	}

	return count, nil
}

// 🔔 发送客户端上线通知
func (s *ClientService) SendClientOnlineNotification(client sys.Client) {
	global.LOG.Info("📤 开始发送客户端上线通知",
		zap.Uint("client_id", client.ID),
		zap.String("hostname", client.Hostname),
		zap.Uint("listener_id", client.ListenerID))

	// 🚀 使用工作池异步发送通知，避免阻塞主流程
	task := workerpool.NewGeneralTask("client_online_notification", func() error {
		global.LOG.Info("🔧 工作池任务开始执行客户端上线通知",
			zap.Uint("client_id", client.ID))

		notificationService := &sysService.NotificationService{}

		// 创建通知数据
		clientID := client.ID
		clientName := client.Hostname
		clientIP := client.RemoteAddr
		listenerID := client.ListenerID

		data := sys.NotificationData{
			ClientID:   &clientID,
			ClientName: &clientName,
			ClientIP:   &clientIP,
			ListenerID: &listenerID,
			ExtraInfo: map[string]interface{}{
				"os":           client.OS,
				"architecture": client.Architecture,
				"session_type": client.SessionType,
			},
		}

		// 🚨 关键修复：获取客户端所属监听器的创建者ID
		global.LOG.Info("🔍 获取监听器创建者ID", zap.Uint("listener_id", client.ListenerID))
		listenerOwnerID, err := s.getListenerOwnerID(client.ListenerID)
		if err != nil {
			global.LOG.Error("❌ 获取监听器创建者ID失败", zap.Error(err))
			return err
		}
		global.LOG.Info("✅ 获取监听器创建者ID成功",
			zap.Uint("listener_id", client.ListenerID),
			zap.Uint("owner_id", listenerOwnerID))

		// 创建通知请求
		notificationReq := request.NotificationCreateRequest{
			UserID:  listenerOwnerID, // 🚨 关键修复：发送给监听器创建者
			Type:    sys.NotificationClientOnline,
			Level:   sys.NotificationLevelInfo,
			Title:   "客户端上线",
			Content: fmt.Sprintf("客户端 %s (%s) 已上线", client.Hostname, client.RemoteAddr),
			Data:    data,
		}

		global.LOG.Info("📨 准备创建通知",
			zap.Uint("user_id", listenerOwnerID),
			zap.String("title", notificationReq.Title),
			zap.String("content", notificationReq.Content))

		// 发送通知
		if err := notificationService.CreateNotification(notificationReq); err != nil {
			global.LOG.Error("❌ 发送客户端上线通知失败",
				zap.Uint("client_id", client.ID),
				zap.Uint("user_id", listenerOwnerID),
				zap.Error(err))
			return err
		} else {
			global.LOG.Info("✅ 客户端上线通知发送成功",
				zap.Uint("client_id", client.ID),
				zap.String("hostname", client.Hostname),
				zap.Uint("user_id", listenerOwnerID))
			return nil
		}
	})

	global.LOG.Info("🚀 提交客户端上线通知任务到工作池")
	workerpool.SubmitGeneralTask(task)
}

// getListenerOwnerID 获取监听器创建者ID
func (s *ClientService) getListenerOwnerID(listenerID uint) (uint, error) {
	var listener sys.Listener
	err := dbpool.ExecuteDBOperationAsyncAndWait("get_listener_owner", func(db *gorm.DB) error {
		return db.Select("user_id").Where("id = ?", listenerID).First(&listener).Error
	})
	if err != nil {
		return 0, fmt.Errorf("获取监听器创建者失败: %v", err)
	}
	return listener.UserID, nil
}

// getAdminUserID 获取管理员用户ID
func (s *ClientService) getAdminUserID() (uint, error) {
	var adminUser sys.SysUser
	err := dbpool.ExecuteDBOperationAsyncAndWait("get_admin_user", func(db *gorm.DB) error {
		return db.Where("role_name = ?", "superadmin").First(&adminUser).Error
	})
	if err != nil {
		return 0, fmt.Errorf("获取管理员用户失败: %v", err)
	}
	return adminUser.ID, nil
}

// 🔔 发送客户端离线通知
func (s *ClientService) SendClientOfflineNotification(client sys.Client) {
	// 🚀 使用工作池异步发送通知，避免阻塞主流程
	task := workerpool.NewGeneralTask("client_offline_notification", func() error {
		notificationService := &sysService.NotificationService{}

		// 🚨 关键修复：获取客户端所属监听器的创建者ID
		listenerOwnerID, err := s.getListenerOwnerID(client.ListenerID)
		if err != nil {
			global.LOG.Error("获取监听器创建者ID失败", zap.Error(err))
			return err
		}

		// 创建通知数据
		clientID := client.ID
		clientName := client.Hostname
		clientIP := client.RemoteAddr
		listenerID := client.ListenerID

		data := sys.NotificationData{
			ClientID:   &clientID,
			ClientName: &clientName,
			ClientIP:   &clientIP,
			ListenerID: &listenerID,
			ExtraInfo: map[string]interface{}{
				"os":           client.OS,
				"architecture": client.Architecture,
				"session_type": client.SessionType,
			},
		}

		// 创建通知请求
		notificationReq := request.NotificationCreateRequest{
			UserID:  listenerOwnerID, // 🚨 关键修复：发送给监听器创建者
			Type:    sys.NotificationClientOffline,
			Level:   sys.NotificationLevelWarning,
			Title:   "客户端离线",
			Content: fmt.Sprintf("客户端 %s (%s) 已离线", client.Hostname, client.RemoteAddr),
			Data:    data,
		}

		// 发送通知
		if err := notificationService.CreateNotification(notificationReq); err != nil {
			global.LOG.Error("发送客户端离线通知失败",
				zap.Uint("client_id", client.ID),
				zap.Error(err))
			return err
		} else {
			global.LOG.Debug("客户端离线通知发送成功",
				zap.Uint("client_id", client.ID),
				zap.String("hostname", client.Hostname))
			return nil
		}
	})

	workerpool.SubmitGeneralTask(task)
}

// 🔔 发送客户端删除通知
func (s *ClientService) SendClientDeletedNotification(client sys.Client) {
	// 🚀 使用工作池异步发送通知，避免阻塞主流程
	task := workerpool.NewGeneralTask("client_deleted_notification", func() error {
		notificationService := &sysService.NotificationService{}

		// 🚨 关键修复：获取客户端所属监听器的创建者ID
		listenerOwnerID, err := s.getListenerOwnerID(client.ListenerID)
		if err != nil {
			global.LOG.Error("获取监听器创建者ID失败", zap.Error(err))
			return err
		}

		// 创建通知数据
		clientID := client.ID
		clientName := client.Hostname
		clientIP := client.RemoteAddr
		listenerID := client.ListenerID

		data := sys.NotificationData{
			ClientID:   &clientID,
			ClientName: &clientName,
			ClientIP:   &clientIP,
			ListenerID: &listenerID,
			ExtraInfo: map[string]interface{}{
				"os":           client.OS,
				"architecture": client.Architecture,
				"session_type": client.SessionType,
			},
		}

		// 创建通知请求
		notificationReq := request.NotificationCreateRequest{
			UserID:  listenerOwnerID, // 🚨 关键修复：发送给监听器创建者
			Type:    sys.NotificationClientDeleted,
			Level:   sys.NotificationLevelInfo,
			Title:   "客户端删除",
			Content: fmt.Sprintf("客户端 %s (%s) 已被删除", client.Hostname, client.RemoteAddr),
			Data:    data,
		}

		// 发送通知
		if err := notificationService.CreateNotification(notificationReq); err != nil {
			global.LOG.Error("发送客户端删除通知失败",
				zap.Uint("client_id", client.ID),
				zap.Error(err))
			return err
		} else {
			global.LOG.Debug("客户端删除通知发送成功",
				zap.Uint("client_id", client.ID),
				zap.String("hostname", client.Hostname))
			return nil
		}
	})

	workerpool.SubmitGeneralTask(task)
}

// GetClientConnectionStats 获取客户端连接统计
func (s *ClientService) GetClientConnectionStats() map[string]interface{} {
	var totalClients int64
	var onlineClients int64
	var offlineClients int64
	var clientsByOS map[string]int64
	var clientsByListener map[uint]int64
	var avgConnectionTime float64

	// 🚀 获取客户端统计数据
	dbpool.ExecuteDBOperationAsyncAndWait("client_stats", func(db *gorm.DB) error {
		// 获取总客户端数
		db.Model(&sys.Client{}).Count(&totalClients)
		// 获取在线客户端数
		db.Model(&sys.Client{}).Where("status = ?", 1).Count(&onlineClients)
		return nil
	})

	// 计算离线客户端数
	offlineClients = totalClients - onlineClients

	// 按操作系统分组统计
	clientsByOS = make(map[string]int64)
	var osStats []struct {
		OS    string `json:"os"`
		Count int64  `json:"count"`
	}
	// 🚀 使用数据库连接池查询操作系统统计
	dbpool.ExecuteDBOperationAsyncAndWait("client_os_stats", func(db *gorm.DB) error {
		return db.Model(&sys.Client{}).
			Select("os, count(*) as count").
			Group("os").
			Scan(&osStats).Error
	})

	for _, stat := range osStats {
		clientsByOS[stat.OS] = stat.Count
	}

	// 按监听器分组统计
	clientsByListener = make(map[uint]int64)
	var listenerStats []struct {
		ListenerID uint  `json:"listener_id"`
		Count      int64 `json:"count"`
	}
	// 🚀 使用数据库连接池查询监听器统计
	dbpool.ExecuteDBOperationAsyncAndWait("client_listener_stats", func(db *gorm.DB) error {
		return db.Model(&sys.Client{}).
			Select("listener_id, count(*) as count").
			Group("listener_id").
			Scan(&listenerStats).Error
	})

	for _, stat := range listenerStats {
		clientsByListener[stat.ListenerID] = stat.Count
	}

	// 计算平均连接时间（基于在线客户端的连接时间）
	var connectionTimes []time.Time
	// 🚀 使用数据库连接池查询连接时间
	dbpool.ExecuteDBOperationAsyncAndWait("client_connection_times", func(db *gorm.DB) error {
		return db.Model(&sys.Client{}).
			Where("status = ? AND last_active_at IS NOT NULL", 1).
			Pluck("last_active_at", &connectionTimes).Error
	})

	if len(connectionTimes) > 0 {
		var totalDuration float64
		now := time.Now()
		for _, connTime := range connectionTimes {
			duration := now.Sub(connTime).Hours()
			totalDuration += duration
		}
		avgConnectionTime = totalDuration / float64(len(connectionTimes))
	}

	return map[string]interface{}{
		"total_clients":        totalClients,
		"online_clients":       onlineClients,
		"offline_clients":      offlineClients,
		"clients_by_os":        clientsByOS,
		"clients_by_listener":  clientsByListener,
		"avg_connection_hours": avgConnectionTime,
		"connection_rate":      float64(onlineClients) / float64(totalClients) * 100,
		"last_updated":         time.Now(),
	}
}
