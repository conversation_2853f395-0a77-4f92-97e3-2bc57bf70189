package sys

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"server/global"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"
)

type LogService struct{}

var LogServiceApp = new(LogService)

// LogFileInfo 日志文件信息
type LogFileInfo struct {
	Name         string    `json:"name"`
	Path         string    `json:"path"`
	Size         int64     `json:"size"`
	ModTime      time.Time `json:"modTime"`
	Level        string    `json:"level"`
	Date         string    `json:"date"`
	RelativePath string    `json:"relativePath"`
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time `json:"timestamp"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	File      string    `json:"file"`
	Line      int       `json:"line"`
	Raw       string    `json:"raw"`
}

// GetLogFiles 获取所有日志文件
func (s *LogService) GetLogFiles() ([]LogFileInfo, error) {
	logDir := global.CONFIG.Zap.Director
	if logDir == "" {
		logDir = "log"
	}

	var logFiles []LogFileInfo

	err := filepath.Walk(logDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && strings.HasSuffix(info.Name(), ".log") {
			relativePath, _ := filepath.Rel(logDir, path)
			pathParts := strings.Split(relativePath, string(os.PathSeparator))
			
			var date, level string
			if len(pathParts) >= 2 {
				date = pathParts[0]
				fileName := pathParts[1]
				level = strings.TrimSuffix(fileName, ".log")
			} else {
				fileName := info.Name()
				level = strings.TrimSuffix(fileName, ".log")
				date = "unknown"
			}

			logFile := LogFileInfo{
				Name:         info.Name(),
				Path:         path,
				Size:         info.Size(),
				ModTime:      info.ModTime(),
				Level:        level,
				Date:         date,
				RelativePath: relativePath,
			}
			logFiles = append(logFiles, logFile)
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 按修改时间倒序排列
	sort.Slice(logFiles, func(i, j int) bool {
		return logFiles[i].ModTime.After(logFiles[j].ModTime)
	})

	return logFiles, nil
}

// ReadLogFile 读取日志文件内容
func (s *LogService) ReadLogFile(filePath string, page, pageSize int) ([]string, int, error) {
	// 安全检查
	if !s.isValidLogPath(filePath) {
		return nil, 0, fmt.Errorf("无效的日志文件路径")
	}

	file, err := os.Open(filePath)
	if err != nil {
		return nil, 0, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	if err := scanner.Err(); err != nil {
		return nil, 0, err
	}

	totalLines := len(lines)
	
	// 分页处理
	start := (page - 1) * pageSize
	end := start + pageSize
	
	if start >= totalLines {
		return []string{}, totalLines, nil
	}
	
	if end > totalLines {
		end = totalLines
	}
	
	return lines[start:end], totalLines, nil
}

// ParseLogEntry 解析日志条目
func (s *LogService) ParseLogEntry(line string) (*LogEntry, error) {
	// 解析EzC2日志格式: [EzC2]2025-07-25 18:17:49.120	[34minfo[0m	E:/CTF/code/WorkSpace/GO/ez/server/utils/config_helper.go:28	未配置磁盘列表，将自动检测系统盘符
	
	if !strings.HasPrefix(line, "[EzC2]") {
		return &LogEntry{Raw: line}, nil
	}

	parts := strings.Split(line, "\t")
	if len(parts) < 4 {
		return &LogEntry{Raw: line}, nil
	}

	// 解析时间戳
	timeStr := strings.TrimPrefix(parts[0], "[EzC2]")
	timestamp, err := time.Parse("2006-01-02 15:04:05.000", timeStr)
	if err != nil {
		global.LOG.Warn("解析时间戳失败", zap.String("timeStr", timeStr), zap.Error(err))
		timestamp = time.Now()
	}

	// 解析级别（去除ANSI颜色代码）
	level := parts[1]
	level = strings.ReplaceAll(level, "[34m", "")
	level = strings.ReplaceAll(level, "[0m", "")
	level = strings.ReplaceAll(level, "[31m", "")
	level = strings.ReplaceAll(level, "[33m", "")
	level = strings.ReplaceAll(level, "[35m", "")

	// 解析文件和行号
	fileInfo := parts[2]
	
	// 解析消息
	message := ""
	if len(parts) > 3 {
		message = strings.Join(parts[3:], "\t")
	}

	return &LogEntry{
		Timestamp: timestamp,
		Level:     level,
		Message:   message,
		File:      fileInfo,
		Raw:       line,
	}, nil
}

// GetLogStats 获取日志统计信息
func (s *LogService) GetLogStats() (map[string]interface{}, error) {
	logDir := global.CONFIG.Zap.Director
	if logDir == "" {
		logDir = "log"
	}

	stats := make(map[string]interface{})
	levelStats := make(map[string]map[string]interface{})
	
	err := filepath.Walk(logDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && strings.HasSuffix(info.Name(), ".log") {
			relativePath, _ := filepath.Rel(logDir, path)
			pathParts := strings.Split(relativePath, string(os.PathSeparator))
			
			var level string
			if len(pathParts) >= 2 {
				fileName := pathParts[1]
				level = strings.TrimSuffix(fileName, ".log")
			} else {
				fileName := info.Name()
				level = strings.TrimSuffix(fileName, ".log")
			}

			if levelStats[level] == nil {
				levelStats[level] = map[string]interface{}{
					"count": 0,
					"size":  int64(0),
				}
			}
			
			levelStats[level]["count"] = levelStats[level]["count"].(int) + 1
			levelStats[level]["size"] = levelStats[level]["size"].(int64) + info.Size()
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	stats["levels"] = levelStats
	stats["logDir"] = logDir
	stats["timestamp"] = time.Now().Unix()

	return stats, nil
}

// isValidLogPath 检查是否为有效的日志文件路径
func (s *LogService) isValidLogPath(filePath string) bool {
	logDir := global.CONFIG.Zap.Director
	if logDir == "" {
		logDir = "log"
	}

	absLogDir, err := filepath.Abs(logDir)
	if err != nil {
		return false
	}

	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		return false
	}

	return strings.HasPrefix(absFilePath, absLogDir)
}

// GetRecentLogs 获取最近的日志条目
func (s *LogService) GetRecentLogs(level string, limit int) ([]LogEntry, error) {
	logFiles, err := s.GetLogFiles()
	if err != nil {
		return nil, err
	}

	var entries []LogEntry
	
	// 按时间倒序查找日志
	for _, logFile := range logFiles {
		if level != "" && logFile.Level != level {
			continue
		}

		lines, _, err := s.ReadLogFile(logFile.Path, 1, 100) // 读取最近100行
		if err != nil {
			continue
		}

		// 倒序处理，获取最新的日志
		for i := len(lines) - 1; i >= 0; i-- {
			entry, err := s.ParseLogEntry(lines[i])
			if err != nil {
				continue
			}
			
			entries = append(entries, *entry)
			if len(entries) >= limit {
				return entries, nil
			}
		}
	}

	return entries, nil
}

// SearchLogs 搜索日志
func (s *LogService) SearchLogs(keyword string, level string, startTime, endTime time.Time, limit int) ([]LogEntry, error) {
	logFiles, err := s.GetLogFiles()
	if err != nil {
		return nil, err
	}

	var entries []LogEntry
	
	for _, logFile := range logFiles {
		if level != "" && logFile.Level != level {
			continue
		}

		// 检查文件时间范围
		if !startTime.IsZero() && logFile.ModTime.Before(startTime) {
			continue
		}
		if !endTime.IsZero() && logFile.ModTime.After(endTime) {
			continue
		}

		lines, _, err := s.ReadLogFile(logFile.Path, 1, 1000) // 读取更多行用于搜索
		if err != nil {
			continue
		}

		for _, line := range lines {
			if keyword != "" && !strings.Contains(strings.ToLower(line), strings.ToLower(keyword)) {
				continue
			}

			entry, err := s.ParseLogEntry(line)
			if err != nil {
				continue
			}

			// 时间范围过滤
			if !startTime.IsZero() && entry.Timestamp.Before(startTime) {
				continue
			}
			if !endTime.IsZero() && entry.Timestamp.After(endTime) {
				continue
			}
			
			entries = append(entries, *entry)
			if len(entries) >= limit {
				return entries, nil
			}
		}
	}

	return entries, nil
}
