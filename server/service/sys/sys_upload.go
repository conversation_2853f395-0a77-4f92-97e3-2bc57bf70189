package sys

import (
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"regexp"
	"server/global"
	"strings"
	"time"

	"go.uber.org/zap"
)

type UploadService struct{}

var UploadServiceApp = new(UploadService)

// UploadType 上传类型枚举
type UploadType string

const (
	UploadTypeUpload   UploadType = "upload"   // 上传目录
	UploadTypeDownload UploadType = "download" // 下载目录
)

// UploadRequest 上传请求结构
type UploadRequest struct {
	File       *multipart.FileHeader `json:"-"`
	UploadType UploadType            `json:"upload_type" binding:"required"`
	SubDir     string                `json:"sub_dir,omitempty"` // 可选的子目录
}

// UploadResponse 上传响应结构
type UploadResponse struct {
	FileName     string `json:"file_name"`
	FilePath     string `json:"file_path"`
	RelativePath string `json:"relative_path"`
	FileSize     int64  `json:"file_size"`
	UploadType   string `json:"upload_type"`
}

// 允许的文件扩展名（白名单）
var allowedExtensions = map[string]bool{
	".txt":  true,
	".pdf":  true,
	".doc":  true,
	".docx": true,
	".xls":  true,
	".xlsx": true,
	".ppt":  true,
	".pptx": true,
	".jpg":  true,
	".jpeg": true,
	".png":  true,
	".gif":  true,
	".zip":  true,
	".rar":  true,
	".7z":   true,
	".tar":  true,
	".gz":   true,
	".json": true,
	".xml":  true,
	".csv":  true,
	".log":  true,
	".exe":  true,
	".msi":  true,
	".deb":  true,
	".rpm":  true,
	".dmg":  true,
	".pkg":  true,
}

// 危险文件名模式（黑名单）
var dangerousPatterns = []*regexp.Regexp{
	regexp.MustCompile(`\.\.`),                                  // 路径遍历
	regexp.MustCompile(`[<>:"|?*]`),                             // Windows非法字符
	regexp.MustCompile(`^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$`), // Windows保留名
}

// 最大文件大小 (100MB)
const maxFileSize = 100 * 1024 * 1024

// UploadFile 上传文件到指定目录
func (s *UploadService) UploadFile(req *UploadRequest) (*UploadResponse, error) {
	if req.File == nil {
		return nil, errors.New("文件不能为空")
	}

	// 验证文件大小
	if req.File.Size > maxFileSize {
		return nil, fmt.Errorf("文件大小超过限制，最大允许 %d MB", maxFileSize/(1024*1024))
	}

	// 验证文件名安全性
	if err := s.validateFileName(req.File.Filename); err != nil {
		return nil, err
	}

	// 验证文件扩展名
	if err := s.validateFileExtension(req.File.Filename); err != nil {
		return nil, err
	}

	// 获取目标目录
	targetDir, err := s.getTargetDirectory(req.UploadType, req.SubDir)
	if err != nil {
		return nil, err
	}

	// 确保目录存在
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return nil, fmt.Errorf("创建目录失败: %v", err)
	}

	// 生成安全的文件名（避免重名）
	safeFileName := s.generateSafeFileName(req.File.Filename, targetDir)
	filePath := filepath.Join(targetDir, safeFileName)

	// 保存文件
	if err := s.saveFile(req.File, filePath); err != nil {
		return nil, err
	}

	// 获取相对路径
	relativePath, err := s.getRelativePath(filePath, req.UploadType)
	if err != nil {
		return nil, err
	}

	global.LOG.Info("文件上传成功",
		zap.String("fileName", safeFileName),
		zap.String("filePath", filePath),
		zap.String("uploadType", string(req.UploadType)),
		zap.Int64("fileSize", req.File.Size),
	)

	return &UploadResponse{
		FileName:     safeFileName,
		FilePath:     filePath,
		RelativePath: relativePath,
		FileSize:     req.File.Size,
		UploadType:   string(req.UploadType),
	}, nil
}

// validateFileName 验证文件名安全性
func (s *UploadService) validateFileName(filename string) error {
	if filename == "" {
		return errors.New("文件名不能为空")
	}

	if len(filename) > 255 {
		return errors.New("文件名过长")
	}

	// 检查危险模式
	for _, pattern := range dangerousPatterns {
		if pattern.MatchString(filename) {
			return errors.New("文件名包含非法字符")
		}
	}

	// 检查是否以点开头（隐藏文件）
	if strings.HasPrefix(filename, ".") {
		return errors.New("不允许上传隐藏文件")
	}

	return nil
}

// validateFileExtension 验证文件扩展名
func (s *UploadService) validateFileExtension(filename string) error {
	ext := strings.ToLower(filepath.Ext(filename))
	if ext == "" {
		return errors.New("文件必须有扩展名")
	}

	if !allowedExtensions[ext] {
		return fmt.Errorf("不支持的文件类型: %s", ext)
	}

	return nil
}

// getTargetDirectory 获取目标目录
func (s *UploadService) getTargetDirectory(uploadType UploadType, subDir string) (string, error) {
	var baseDir string

	switch uploadType {
	case UploadTypeUpload:
		baseDir = global.CONFIG.Server.UploadDir
	case UploadTypeDownload:
		baseDir = global.CONFIG.Server.DownloadDir
	default:
		return "", errors.New("无效的上传类型")
	}

	if baseDir == "" {
		return "", errors.New("目标目录未配置")
	}

	// 清理并验证子目录
	if subDir != "" {
		// 清理路径，移除危险字符
		subDir = filepath.Clean(subDir)
		// 确保子目录不包含路径遍历
		if strings.Contains(subDir, "..") || strings.HasPrefix(subDir, "/") {
			return "", errors.New("子目录路径非法")
		}
		baseDir = filepath.Join(baseDir, subDir)
	}

	// 确保目标目录在允许的范围内
	absBaseDir, err := filepath.Abs(baseDir)
	if err != nil {
		return "", fmt.Errorf("获取绝对路径失败: %v", err)
	}

	// 验证路径是否在允许的目录内
	var allowedBaseDir string
	switch uploadType {
	case UploadTypeUpload:
		allowedBaseDir, _ = filepath.Abs(global.CONFIG.Server.UploadDir)
	case UploadTypeDownload:
		allowedBaseDir, _ = filepath.Abs(global.CONFIG.Server.DownloadDir)
	}

	if !strings.HasPrefix(absBaseDir, allowedBaseDir) {
		return "", errors.New("目标路径超出允许范围")
	}

	return absBaseDir, nil
}

// generateSafeFileName 生成安全的文件名（处理重名）
func (s *UploadService) generateSafeFileName(originalName, targetDir string) string {
	filePath := filepath.Join(targetDir, originalName)

	// 如果文件不存在，直接返回原名
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return originalName
	}

	// 文件存在，生成新名称
	ext := filepath.Ext(originalName)
	name := strings.TrimSuffix(originalName, ext)

	for i := 1; i < 1000; i++ {
		newName := fmt.Sprintf("%s(%d)%s", name, i, ext)
		newPath := filepath.Join(targetDir, newName)
		if _, err := os.Stat(newPath); os.IsNotExist(err) {
			return newName
		}
	}

	// 如果前999个都存在，使用时间戳
	return fmt.Sprintf("%s_%d%s", name, time.Now().Unix(), ext)
}

// saveFile 保存文件
func (s *UploadService) saveFile(fileHeader *multipart.FileHeader, targetPath string) error {
	// 打开上传的文件
	src, err := fileHeader.Open()
	if err != nil {
		return fmt.Errorf("打开上传文件失败: %v", err)
	}
	defer src.Close()

	// 创建目标文件
	dst, err := os.Create(targetPath)
	if err != nil {
		return fmt.Errorf("创建目标文件失败: %v", err)
	}
	defer dst.Close()

	// 复制文件内容
	_, err = io.Copy(dst, src)
	if err != nil {
		return fmt.Errorf("保存文件失败: %v", err)
	}

	return nil
}

// getRelativePath 获取相对路径
func (s *UploadService) getRelativePath(fullPath string, uploadType UploadType) (string, error) {
	var baseDir string
	switch uploadType {
	case UploadTypeUpload:
		baseDir = global.CONFIG.Server.UploadDir
	case UploadTypeDownload:
		baseDir = global.CONFIG.Server.DownloadDir
	default:
		return "", errors.New("无效的上传类型")
	}

	absBaseDir, err := filepath.Abs(baseDir)
	if err != nil {
		return "", err
	}

	relPath, err := filepath.Rel(absBaseDir, fullPath)
	if err != nil {
		return "", err
	}

	return relPath, nil
}

// GetAllowedExtensions 获取允许的文件扩展名列表
func (s *UploadService) GetAllowedExtensions() []string {
	extensions := make([]string, 0, len(allowedExtensions))
	for ext := range allowedExtensions {
		extensions = append(extensions, ext)
	}
	return extensions
}

// GetMaxFileSize 获取最大文件大小
func (s *UploadService) GetMaxFileSize() int64 {
	return maxFileSize
}
