package sys

import (
	"server/core/manager/dbpool"
	"server/global"
	"server/model/sys"
	"server/utils"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type DashboardService struct{}

var DashboardServiceApp = new(DashboardService)

// DashboardStats dashboard统计信息结构体
type DashboardStats struct {
	ClientStats    ClientStats    `json:"clientStats"`
	ListenerStats  ListenerStats  `json:"listenerStats"`
	SystemStats    SystemStats    `json:"systemStats"`
	RecentActivity RecentActivity `json:"recentActivity"`
}

// ClientStats 客户端统计
type ClientStats struct {
	Total    int64 `json:"total"`    // 总客户端数
	Online   int64 `json:"online"`   // 在线客户端数
	Offline  int64 `json:"offline"`  // 离线客户端数
	Windows  int64 `json:"windows"`  // Windows客户端数
	Linux    int64 `json:"linux"`    // Linux客户端数
	MacOS    int64 `json:"macOS"`    // macOS客户端数
	Unknown  int64 `json:"unknown"`  // 未知系统客户端数
	Today    int64 `json:"today"`    // 今日新增客户端
	ThisWeek int64 `json:"thisWeek"` // 本周新增客户端
}

// ListenerStats 监听器统计
type ListenerStats struct {
	Total   int64 `json:"total"`   // 总监听器数
	Running int64 `json:"running"` // 运行中监听器数
	Stopped int64 `json:"stopped"` // 已停止监听器数
	TCP     int64 `json:"tcp"`     // TCP监听器数
	Pipe    int64 `json:"pipe"`    // Pipe监听器数
}

// SystemStats 系统统计
type SystemStats struct {
	Uptime          int64   `json:"uptime"`          // 系统运行时间(秒)
	CPUUsage        float64 `json:"cpuUsage"`        // CPU使用率
	MemoryUsage     float64 `json:"memoryUsage"`     // 内存使用率
	DiskUsage       float64 `json:"diskUsage"`       // 磁盘使用率
	NetworkUpload   int     `json:"networkUpload"`   // 网络上传速度(KB/s)
	NetworkDownload int     `json:"networkDownload"` // 网络下载速度(KB/s)
}

// RecentActivity 最近活动
type RecentActivity struct {
	RecentClients   []RecentClient   `json:"recentClients"`   // 最近连接的客户端
	RecentListeners []RecentListener `json:"recentListeners"` // 最近创建的监听器
}

// RecentClient 最近客户端
type RecentClient struct {
	ID          uint      `json:"id"`
	RemoteAddr  string    `json:"remoteAddr"`
	OS          string    `json:"os"`
	Username    string    `json:"username"`
	Hostname    string    `json:"hostname"`
	ConnectedAt time.Time `json:"connectedAt"`
	Status      int       `json:"status"`
}

// RecentListener 最近监听器
type RecentListener struct {
	ID              uint      `json:"id"`
	Name            string    `json:"name"`
	ListenerType    string    `json:"listenerType"`
	LocalListenAddr string    `json:"localListenAddr"`
	Status          int       `json:"status"`
	CreatedAt       time.Time `json:"createdAt"`
}

func (dashboardService *DashboardService) GetSystemInfo() (server *utils.Server, err error) {
	var s utils.Server
	s.OS = utils.InitOS()
	if s.CPU, err = utils.InitCPU(); err != nil {
		global.LOG.Error("func utils.InitCPU() Failed", zap.String("err", err.Error()))
		return &s, err
	}
	if s.RAM, err = utils.InitRAM(); err != nil {
		global.LOG.Error("func utils.InitRAM() Failed", zap.String("err", err.Error()))
		return &s, err
	}
	if s.Disk, err = utils.InitDisk(); err != nil {
		global.LOG.Error("func utils.InitDisk() Failed", zap.String("err", err.Error()))
		return &s, err
	}
	if s.Net, err = utils.InitNet(); err != nil {
		global.LOG.Error("func utils.InitNet() Failed", zap.String("err", err.Error()))
		return &s, err
	}

	return &s, nil
}

// GetDashboardStats 获取dashboard统计信息
func (dashboardService *DashboardService) GetDashboardStats() (stats *DashboardStats, err error) {
	stats = &DashboardStats{}

	// 获取客户端统计
	if stats.ClientStats, err = dashboardService.getClientStats(); err != nil {
		global.LOG.Error("获取客户端统计失败", zap.Error(err))
		return nil, err
	}

	// 获取监听器统计
	if stats.ListenerStats, err = dashboardService.getListenerStats(); err != nil {
		global.LOG.Error("获取监听器统计失败", zap.Error(err))
		return nil, err
	}

	// 获取系统统计
	if stats.SystemStats, err = dashboardService.getSystemStats(); err != nil {
		global.LOG.Error("获取系统统计失败", zap.Error(err))
		return nil, err
	}

	// 获取最近活动
	if stats.RecentActivity, err = dashboardService.getRecentActivity(); err != nil {
		global.LOG.Error("获取最近活动失败", zap.Error(err))
		return nil, err
	}

	return stats, nil
}

// getClientStats 获取客户端统计信息
func (dashboardService *DashboardService) getClientStats() (stats ClientStats, err error) {
	// 🚀 使用数据库连接池获取客户端统计
	if err = dbpool.ExecuteDBOperationAsyncAndWait("client_dashboard_stats", func(db *gorm.DB) error {
		// 总客户端数
		if err := db.Model(&sys.Client{}).Count(&stats.Total).Error; err != nil {
			return err
		}
		// 在线客户端数
		return db.Model(&sys.Client{}).Where("status = ?", 1).Count(&stats.Online).Error
	}); err != nil {
		return stats, err
	}

	// 离线客户端数
	stats.Offline = stats.Total - stats.Online

	// 🚀 按操作系统统计
	if err = dbpool.ExecuteDBOperationAsyncAndWait("client_os_dashboard_stats", func(db *gorm.DB) error {
		if err := db.Model(&sys.Client{}).Where("os LIKE ?", "%windows%").Count(&stats.Windows).Error; err != nil {
			return err
		}
		if err := db.Model(&sys.Client{}).Where("os LIKE ? OR os LIKE ?", "%linux%", "%unix%").Count(&stats.Linux).Error; err != nil {
			return err
		}
		return db.Model(&sys.Client{}).Where("os LIKE ? OR os LIKE ?", "%darwin%", "%macos%").Count(&stats.MacOS).Error
	}); err != nil {
		return stats, err
	}

	stats.Unknown = stats.Total - stats.Windows - stats.Linux - stats.MacOS

	// 🚀 按时间统计
	today := time.Now().Truncate(24 * time.Hour)
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	if err = dbpool.ExecuteDBOperationAsyncAndWait("client_time_dashboard_stats", func(db *gorm.DB) error {
		// 今日新增客户端
		if err := db.Model(&sys.Client{}).Where("connected_at >= ?", today).Count(&stats.Today).Error; err != nil {
			return err
		}
		// 本周新增客户端
		return db.Model(&sys.Client{}).Where("connected_at >= ?", weekStart).Count(&stats.ThisWeek).Error
	}); err != nil {
		return stats, err
	}

	return stats, nil
}

// getListenerStats 获取监听器统计信息
func (dashboardService *DashboardService) getListenerStats() (stats ListenerStats, err error) {
	// 🚀 使用数据库连接池获取监听器基础统计
	if err = dbpool.ExecuteDBOperationAsyncAndWait("listener_basic_stats", func(db *gorm.DB) error {
		// 总监听器数
		if err := db.Model(&sys.Listener{}).Count(&stats.Total).Error; err != nil {
			return err
		}
		// 运行中监听器数
		return db.Model(&sys.Listener{}).Where("status = ?", 1).Count(&stats.Running).Error
	}); err != nil {
		return stats, err
	}

	// 已停止监听器数
	stats.Stopped = stats.Total - stats.Running

	// 🚀 按类型统计
	if err = dbpool.ExecuteDBOperationAsyncAndWait("listener_type_stats", func(db *gorm.DB) error {
		if err := db.Model(&sys.Listener{}).Where("type = ?", "tcp").Count(&stats.TCP).Error; err != nil {
			return err
		}
		return db.Model(&sys.Listener{}).Where("type = ?", "pipe").Count(&stats.Pipe).Error
	}); err != nil {
		return stats, err
	}

	return stats, nil
}

// getSystemStats 获取系统统计信息
func (dashboardService *DashboardService) getSystemStats() (stats SystemStats, err error) {
	// 获取系统信息
	server, err := dashboardService.GetSystemInfo()
	if err != nil {
		return stats, err
	}

	stats.Uptime = server.OS.Uptime

	// CPU使用率(取平均值)
	if len(server.CPU.Cpus) > 0 {
		var total float64
		for _, cpu := range server.CPU.Cpus {
			total += cpu
		}
		stats.CPUUsage = total / float64(len(server.CPU.Cpus))
	}

	// 内存使用率
	stats.MemoryUsage = float64(server.RAM.UsedPercent)

	// 磁盘使用率(取平均值)
	if len(server.Disk) > 0 {
		var total int
		for _, disk := range server.Disk {
			total += disk.UsedPercent
		}
		stats.DiskUsage = float64(total) / float64(len(server.Disk))
	}

	// 网络速度
	stats.NetworkUpload = server.Net.UploadKBPS
	stats.NetworkDownload = server.Net.DownloadKBPS

	return stats, nil
}

// getRecentActivity 获取最近活动
func (dashboardService *DashboardService) getRecentActivity() (activity RecentActivity, err error) {
	// 最近连接的客户端(最近10个)
	var clients []sys.Client
	// 🚀 使用数据库连接池获取最近客户端活动
	if err = dbpool.ExecuteDBOperationAsyncAndWait("recent_client_activity", func(db *gorm.DB) error {
		return db.Model(&sys.Client{}).Order("connected_at DESC").Limit(10).Find(&clients).Error
	}); err != nil {
		return activity, err
	}

	for _, client := range clients {
		activity.RecentClients = append(activity.RecentClients, RecentClient{
			ID:          client.ID,
			RemoteAddr:  client.RemoteAddr,
			OS:          client.OS,
			Username:    client.Username,
			Hostname:    client.Hostname,
			ConnectedAt: client.ConnectedAt,
			Status:      client.Status,
		})
	}

	// 最近创建的监听器(最近10个)
	var listeners []sys.Listener
	// 🚀 使用数据库连接池获取最近监听器活动
	if err = dbpool.ExecuteDBOperationAsyncAndWait("recent_listener_activity", func(db *gorm.DB) error {
		return db.Model(&sys.Listener{}).Order("created_at DESC").Limit(10).Find(&listeners).Error
	}); err != nil {
		return activity, err
	}

	for _, listener := range listeners {
		activity.RecentListeners = append(activity.RecentListeners, RecentListener{
			ID:              listener.ID,
			Name:            listener.Remark,
			ListenerType:    listener.Type,
			LocalListenAddr: listener.LocalListenAddr,
			Status:          listener.Status,
			CreatedAt:       listener.CreatedAt,
		})
	}

	return activity, nil
}

// 🌐 网络拓扑图数据结构
type NetworkTopology struct {
	Listeners []TopologyListener `json:"listeners"`
	Clients   []TopologyClient   `json:"clients"`
}

type TopologyListener struct {
	ID                uint    `json:"id"`
	Type              string  `json:"type"`
	Remark            string  `json:"remark"`
	Status            int     `json:"status"`
	LocalListenAddr   string  `json:"localListenAddr"`
	RemoteConnectAddr string  `json:"remoteConnectAddr"`
	ClientCount       int     `json:"clientCount"`
	// 拓扑图位置
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

type TopologyClient struct {
	ID           uint    `json:"id"`
	ListenerID   uint    `json:"listenerId"`
	OS           string  `json:"os"`
	OSVersion    string  `json:"osVersion"`
	Hostname     string  `json:"hostname"`
	Username     string  `json:"username"`
	RemoteAddr   string  `json:"remoteAddr"`
	LocalIP      string  `json:"localIP"`
	PublicIP     string  `json:"publicIP"`
	Status       int     `json:"status"`
	Architecture string  `json:"architecture"`
	Domain       string  `json:"domain"`
	Workgroup    string  `json:"workgroup"`
	NetworkType  string  `json:"networkType"`
	GeoLocation  string  `json:"geoLocation"`
	ISP          string  `json:"isp"`
	// 拓扑图位置
	X float64 `json:"x"`
	Y float64 `json:"y"`
	// 性能数据
	CPUUsage    float64 `json:"cpuUsage"`
	MemoryUsage float64 `json:"memoryUsage"`
	Latency     int64   `json:"latency"`
}

// GetNetworkTopology 获取网络拓扑图数据
func (dashboardService *DashboardService) GetNetworkTopology() (topology *NetworkTopology, err error) {
	topology = &NetworkTopology{}

	// 获取监听器数据
	var listeners []sys.Listener
	// 🚀 使用数据库连接池获取所有监听器数据
	if err = dbpool.ExecuteDBOperationAsyncAndWait("all_listeners_data", func(db *gorm.DB) error {
		return db.Find(&listeners).Error
	}); err != nil {
		global.LOG.Error("获取监听器数据失败", zap.Error(err))
		return nil, err
	}

	// 转换监听器数据
	for _, listener := range listeners {
		// 统计该监听器的客户端数量
		var clientCount int64
		// 🚀 使用数据库连接池统计监听器的客户端数量
		dbpool.ExecuteDBOperationAsyncAndWait("listener_client_count", func(db *gorm.DB) error {
			return db.Model(&sys.Client{}).Where("listener_id = ? AND status = 1", listener.ID).Count(&clientCount).Error
		})

		topologyListener := TopologyListener{
			ID:                listener.ID,
			Type:              listener.Type,
			Remark:            listener.Remark,
			Status:            listener.Status,
			LocalListenAddr:   listener.LocalListenAddr,
			RemoteConnectAddr: listener.RemoteConnectAddr,
			ClientCount:       int(clientCount),
			X:                 0, // 默认位置，前端会重新计算
			Y:                 0,
		}
		topology.Listeners = append(topology.Listeners, topologyListener)
	}

	// 获取客户端数据
	var clients []sys.Client
	// 🚀 使用数据库连接池获取所有客户端数据
	if err = dbpool.ExecuteDBOperationAsyncAndWait("all_clients_data", func(db *gorm.DB) error {
		return db.Find(&clients).Error
	}); err != nil {
		global.LOG.Error("获取客户端数据失败", zap.Error(err))
		return nil, err
	}

	// 转换客户端数据
	for _, client := range clients {
		topologyClient := TopologyClient{
			ID:           client.ID,
			ListenerID:   client.ListenerID,
			OS:           client.OS,
			OSVersion:    client.OSVersion,
			Hostname:     client.Hostname,
			Username:     client.Username,
			RemoteAddr:   client.RemoteAddr,
			LocalIP:      client.LocalIP,
			PublicIP:     client.PublicIP,
			Status:       client.Status,
			Architecture: client.Architecture,
			Domain:       client.Domain,
			Workgroup:    client.Workgroup,
			NetworkType:  client.NetworkType,
			GeoLocation:  client.GeoLocation,
			ISP:          client.ISP,
			X:            client.TopologyX,
			Y:            client.TopologyY,
			CPUUsage:     client.CPUUsage,
			MemoryUsage:  client.MemoryUsage,
			Latency:      client.Latency,
		}
		topology.Clients = append(topology.Clients, topologyClient)
	}

	return topology, nil
}
