# 测试自动配置功能
Write-Host "测试自动配置功能" -ForegroundColor Green

# 备份当前配置文件
Write-Host "备份当前配置文件" -ForegroundColor Yellow
if (Test-Path -Path "config.yaml.bak") {
    Remove-Item -Path "config.yaml.bak"
}
if (Test-Path -Path "config.yaml") {
    Copy-Item -Path "config.yaml" -Destination "config.yaml.bak"
}

# 使用示例配置文件
Write-Host "使用示例配置文件" -ForegroundColor Yellow
Copy-Item -Path "config.example.yaml" -Destination "config.yaml"

# 启动服务器测试自动配置功能
Write-Host "启动服务器测试自动配置功能" -ForegroundColor Yellow
go run main.go

# 测试完成，恢复原配置文件
Write-Host "测试完成，恢复原配置文件" -ForegroundColor Green
if (Test-Path -Path "config.yaml.bak") {
    Copy-Item -Path "config.yaml.bak" -Destination "config.yaml"
}