package task

import "time"

type ProxyTask struct {
	ID       uint64 `json:"id" gorm:"primarykey"`
	ClientID uint   `json:"client_id"`
	ProxyID  string `json:"proxy_id"`
	TaskType string `json:"task_type"` // start_proxy, stop_proxy
	Status   string `json:"status"`    // pending, running, completed, failed
	Result   string `json:"result"`    // JSON结果
	Error    string `json:"error"`     // 错误信息

	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	StartedAt   *time.Time `json:"started_at"`   // 开始时间
	CompletedAt *time.Time `json:"completed_at"` // 完成时间
}
