package task

import (
	"crypto/rand"
	"encoding/hex"
	"time"
)

// 任务状态常量
const (
	TaskStatusPending   = "pending"   // 等待中
	TaskStatusRunning   = "running"   // 运行中
	TaskStatusCompleted = "completed" // 已完成
	TaskStatusFailed    = "failed"    // 失败
	TaskStatusCancelled = "cancelled" // 已取消
)

// BaseTask 基础任务结构
type BaseTask struct {
	ID        uint      `json:"id"`        // 任务ID
	Type      string    `json:"type"`      // 任务类型
	ClientID  string    `json:"clientId"`  // 客户端ID
	Status    string    `json:"status"`    // 任务状态
	Error     string    `json:"error"`     // 错误信息
	CreatedAt time.Time `json:"createdAt"` // 创建时间
	UpdatedAt time.Time `json:"updatedAt"` // 更新时间
	StartedAt time.Time `json:"startedAt"` // 开始时间
	EndedAt   time.Time `json:"endedAt"`   // 结束时间
}

// GenerateTaskID 生成唯一的任务ID
func GenerateTaskID() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳作为备选方案
		return hex.EncodeToString([]byte(time.Now().Format("20060102150405.000000")))
	}
	return hex.EncodeToString(bytes)
}

// UpdateStatus 更新任务状态
func (t *BaseTask) UpdateStatus(status string, errorMsg ...string) {
	t.Status = status
	t.UpdatedAt = time.Now()

	if len(errorMsg) > 0 && errorMsg[0] != "" {
		t.Error = errorMsg[0]
	}

	switch status {
	case TaskStatusRunning:
		if t.StartedAt.IsZero() {
			t.StartedAt = time.Now()
		}
	case TaskStatusCompleted, TaskStatusFailed, TaskStatusCancelled:
		if t.EndedAt.IsZero() {
			t.EndedAt = time.Now()
		}
	}
}

// IsCompleted 检查任务是否已完成（成功或失败）
func (t *BaseTask) IsCompleted() bool {
	return t.Status == TaskStatusCompleted || t.Status == TaskStatusFailed || t.Status == TaskStatusCancelled
}

// IsRunning 检查任务是否正在运行
func (t *BaseTask) IsRunning() bool {
	return t.Status == TaskStatusRunning
}

// IsPending 检查任务是否等待中
func (t *BaseTask) IsPending() bool {
	return t.Status == TaskStatusPending
}

// Duration 获取任务执行时长
func (t *BaseTask) Duration() time.Duration {
	if t.StartedAt.IsZero() {
		return 0
	}

	endTime := t.EndedAt
	if endTime.IsZero() {
		endTime = time.Now()
	}

	return endTime.Sub(t.StartedAt)
}
