package task

import (
	"time"
)

// ScreenshotTask 截图任务模型
type ScreenshotTask struct {
	ID          uint64     `json:"id" gorm:"primarykey"`
	ClientID    uint       `json:"client_id" gorm:"not null;index"`
	TaskType    string     `json:"task_type" gorm:"size:50;not null"`
	Status      string     `json:"status" gorm:"size:20;default:'pending'"`
	Error       string     `json:"error" gorm:"type:text"`
	Quality     int        `json:"quality" gorm:"default:80"`
	Format      string     `json:"format" gorm:"size:10;default:'png'"`
	Filename    string     `json:"filename" gorm:"size:255"`
	FilePath    string     `json:"file_path" gorm:"size:500"`
	FileSize    int64      `json:"file_size" gorm:"default:0"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
}

// TableName 指定表名
func (ScreenshotTask) TableName() string {
	return "screenshot_tasks"
}
