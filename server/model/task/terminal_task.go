package task

import "time"

// TerminalTask 终端管理任务
type TerminalTask struct {
	ID          uint64     `json:"id" gorm:"primarykey"`
	ClientID    uint       `json:"client_id"`  // 客户端ID
	TaskType    string     `json:"task_type"`  // 任务类型：create_terminal, close_terminal
	TerminalID  uint32     `json:"terminal_id"` // 终端ID（用于关闭操作）
	Status      string     `json:"status"`     // 状态：pending, running, completed, failed, cancelled
	Error       string     `json:"error"`      // 错误信息
	Result      string     `json:"result"`     // 任务结果（JSON格式）
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	StartedAt   *time.Time `json:"started_at"`   // 开始时间
	CompletedAt *time.Time `json:"completed_at"` // 完成时间
}

func (TerminalTask) TableName() string {
	return "terminal_tasks"
}

// 任务类型常量
const (
	TerminalTaskTypeCreate  = "create_terminal"
	TerminalTaskTypeClose   = "close_terminal"
	TerminalTaskTypeGetList = "get_terminal_list"
)
