package task

import "time"

// FileTransferTask 文件传输任务
type FileTransferTask struct {
	ID              uint64     `json:"id" gorm:"primarykey"`
	ClientID        uint       `json:"client_id"`        // 客户端ID
	TaskType        string     `json:"task_type"`        // 任务类型：upload, download
	SourcePath      string     `json:"source_path"`      // 源路径
	DestinationPath string     `json:"destination_path"` // 目标路径
	FileSize        int64      `json:"file_size"`        // 文件大小
	TransferredSize int64      `json:"transferred_size"` // 已传输大小
	Status          string     `json:"status"`           // 状态：pending, running, completed, failed, cancelled
	Progress        float64    `json:"progress"`         // 进度百分比
	Error           string     `json:"error"`            // 错误信息
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	StartedAt       *time.Time `json:"started_at"`   // 开始时间
	CompletedAt     *time.Time `json:"completed_at"` // 完成时间
}
