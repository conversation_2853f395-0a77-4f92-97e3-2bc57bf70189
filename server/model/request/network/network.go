package network

// NetworkStatsRequest 网络统计信息请求
type NetworkStatsRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
}

// NetworkInterfacesRequest 网络接口信息请求
type NetworkInterfacesRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
}

// NetworkConnectionsRequest 网络连接信息请求
type NetworkConnectionsRequest struct {
	TaskID   uint64 `json:"task_id"`  // 任务ID
	Protocol string `json:"protocol"` // TCP, UDP, ALL
	State    string `json:"state"`    // ESTABLISHED, LISTENING, etc.
}

// CloseConnectionRequest 关闭网络连接请求
type CloseConnectionRequest struct {
	TaskID       uint64 `json:"task_id"`                          // 任务ID
	ConnectionID string `json:"connection_id" binding:"required"` // 连接ID
	LocalAddr    string `json:"local_addr"`                       // 本地地址
	LocalPort    int    `json:"local_port"`                       // 本地端口
	RemoteAddr   string `json:"remote_addr"`                      // 远程地址
	RemotePort   int    `json:"remote_port"`                      // 远程端口
	Protocol     string `json:"protocol"`                         // 协议
}
