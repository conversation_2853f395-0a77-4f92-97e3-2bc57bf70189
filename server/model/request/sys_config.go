package request

// GeneralConfigRequest 常规配置更新请求
type GeneralConfigRequest struct {
	Server  ServerConfigRequest  `json:"server" binding:"required"`
	JWT     JWTConfigRequest     `json:"jwt" binding:"required"`
	Captcha CaptchaConfigRequest `json:"captcha" binding:"required"`
	Sqlite  SqliteConfigRequest  `json:"sqlite" binding:"required"`
	Zap     ZapConfigRequest     `json:"zap" binding:"required"`
}

// ServerConfigRequest 服务器配置请求
type ServerConfigRequest struct {
	PORT            int    `json:"port" binding:"required,min=1,max=65535"`
	Version         string `json:"version"`
	RouterPrefix    string `json:"routerPrefix" binding:"required"`
	LimitCountIP    int    `json:"limitCountIP" binding:"required,min=1"`
	LimitTimeIP     int    `json:"limitTimeIP" binding:"required,min=1"`
	ServerStartPort uint16 `json:"serverStartPort" binding:"required,min=1024,max=65535"`
	ServerEndPort   uint16 `json:"serverEndPort" binding:"required,min=1024,max=65535"`
	ClientBinDir    string `json:"clientBinDir" binding:"required"`
	UploadDir       string `json:"uploadDir" binding:"required"`
	DownloadDir     string `json:"downloadDir" binding:"required"`
}

// JWTConfigRequest JWT配置请求
type JWTConfigRequest struct {
	ExpiresTime string `json:"expiresTime" binding:"required"`
	BufferTime  string `json:"bufferTime" binding:"required"`
	SigningKey  string `json:"signingKey" binding:"required,min=16"`
}

// SqliteConfigRequest SQLite配置请求
type SqliteConfigRequest struct {
	DbName       string `json:"dbName" binding:"required"`
	Path         string `json:"path" binding:"required"`
	MaxOpenConns int    `json:"maxOpenConns" binding:"required,min=1,max=1000"`
	MaxIdleConns int    `json:"maxIdleConns" binding:"required,min=1,max=100"`
}

// ZapConfigRequest 日志配置请求
type ZapConfigRequest struct {
	Level        string `json:"level" binding:"required,oneof=debug info warn error"`
	Director     string `json:"director" binding:"required"`
	RetentionDay int    `json:"retentionDay" binding:"required,min=-1,max=365"`
	LogInConsole bool   `json:"logInConsole"`
	ShowLine     bool   `json:"showLine"`
}

// CaptchaConfigRequest 验证码配置请求
type CaptchaConfigRequest struct {
	KeyLong            int  `json:"keyLong" binding:"required,min=4,max=8"`
	ImgWidth           int  `json:"imgWidth" binding:"required,min=100,max=500"`
	ImgHeight          int  `json:"imgHeight" binding:"required,min=50,max=200"`
	OpenCaptcha        bool `json:"openCaptcha"`
	MaxFailedAttempts  int  `json:"maxFailedAttempts" binding:"required,min=1,max=100"`
	OpenCaptchaTimeOut int  `json:"openCaptchaTimeOut" binding:"required,min=60"`
}

// NotificationConfigRequest 通知配置更新请求
type NotificationConfigRequest struct {
	ClientStatus     bool `json:"clientStatus"`
	DisplayDuration  int  `json:"displayDuration" binding:"required,min=1000,max=30000"`
	SoundEnabled     bool `json:"soundEnabled"`
	MaxNotifications int  `json:"maxNotifications" binding:"required,min=1,max=10"`
}