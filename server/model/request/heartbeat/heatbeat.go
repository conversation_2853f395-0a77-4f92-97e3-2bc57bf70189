package heartbeat

import (
	"server/model/basic"
	"time"
)

// HeartbeatRequest 心跳请求结构体
type HeartbeatRequest struct {
	// 基础信息
	ClientID    string    `json:"client_id"`    // 客户端唯一标识
	Timestamp   time.Time `json:"timestamp"`    // 发送时间戳
	SequenceNum uint64    `json:"sequence_num"` // 序列号，用于检测丢包

	// 系统状态信息
	SystemInfo basic.SystemStatus `json:"system_info"` // 系统状态信息

	// 网络状态
	NetworkInfo basic.NetworkStatus `json:"network_info"` // 网络状态信息

	// 心跳类型 (使用TLV中的常量：PING, PONG, ACK, NACK等)
	Type uint8 `json:"type"` // 心跳类型：使用tlv包中的常量

	// 随机抖动值（用于防止同步心跳）
	Jitter int `json:"jitter"` // 毫秒级抖动值
}
