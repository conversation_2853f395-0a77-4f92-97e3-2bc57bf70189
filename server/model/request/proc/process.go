package proc

// ProcessListRequest 进程列表请求
type ProcessListRequest struct {
	TaskID         uint64 `json:"task_id"`         // 任务ID
	Username       string `json:"username"`        // 过滤用户名（可选）
	ProcessName    string `json:"process_name"`    // 过滤进程名（可选）
	IncludeDetails bool   `json:"include_details"` // 是否包含详细信息
	ShowSystem     bool   `json:"showSystem"`      // 是否显示系统进程
}

// ProcessKillRequest 终止进程请求
type ProcessKillRequest struct {
	TaskID uint64 `json:"task_id"`                // 任务ID
	PID    int32  `json:"pid" binding:"required"` // 进程ID
	Force  bool   `json:"force"`                  // 是否强制终止
}

// ProcessStartRequest 启动进程请求
type ProcessStartRequest struct {
	TaskID     uint64 `json:"task_id"`                    // 任务ID
	Command    string `json:"command" binding:"required"` // 可执行文件路径
	Args       string `json:"args"`                       // 命令行参数
	WorkDir    string `json:"workDir"`                    // 工作目录
	RunAsAdmin bool   `json:"runAsAdmin"`                 // 是否以管理员权限运行
	HideWindow bool   `json:"hideWindow"`                 // 是否隐藏窗口（仅Windows有效）
}

// ProcessDetailsRequest 进程详情请求
type ProcessDetailsRequest struct {
	TaskID uint64 `json:"task_id"`                // 任务ID
	PID    int32  `json:"pid" binding:"required"` // 进程ID
}

// ProcessSuspendRequest 挂起进程请求
type ProcessSuspendRequest struct {
	TaskID uint64 `json:"task_id"`                // 任务ID
	PID    int32  `json:"pid" binding:"required"` // 进程ID
}

// ProcessResumeRequest 恢复进程请求
type ProcessResumeRequest struct {
	TaskID uint64 `json:"task_id"`                // 任务ID
	PID    int32  `json:"pid" binding:"required"` // 进程ID
}
