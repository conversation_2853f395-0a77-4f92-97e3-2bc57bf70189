package response

// GeneralConfigResponse 常规配置响应
type GeneralConfigResponse struct {
	Server  ServerConfigResponse  `json:"server"`
	JWT     JWTConfigResponse     `json:"jwt"`
	Captcha CaptchaConfigResponse `json:"captcha"`
	Sqlite  SqliteConfigResponse  `json:"sqlite"`
	Zap     ZapConfigResponse     `json:"zap"`
}

// ServerConfigResponse 服务器配置响应
type ServerConfigResponse struct {
	PORT            int    `json:"port"`
	Version         string `json:"version"`
	RouterPrefix    string `json:"routerPrefix"`
	LimitCountIP    int    `json:"limitCountIP"`
	LimitTimeIP     int    `json:"limitTimeIP"`
	ServerStartPort uint16 `json:"serverStartPort"`
	ServerEndPort   uint16 `json:"serverEndPort"`
	ClientBinDir    string `json:"clientBinDir"`
	UploadDir       string `json:"uploadDir"`
	DownloadDir     string `json:"downloadDir"`
}

// JWTConfigResponse JWT配置响应
type JWTConfigResponse struct {
	ExpiresTime string `json:"expiresTime"`
	BufferTime  string `json:"bufferTime"`
	SigningKey  string `json:"signingKey"`
}

// SqliteConfigResponse SQLite配置响应
type SqliteConfigResponse struct {
	DbName       string `json:"dbName"`
	Path         string `json:"path"`
	MaxOpenConns int    `json:"maxOpenConns"`
	MaxIdleConns int    `json:"maxIdleConns"`
}

// ZapConfigResponse 日志配置响应
type ZapConfigResponse struct {
	Level        string `json:"level"`
	Director     string `json:"director"`
	RetentionDay int    `json:"retentionDay"`
	LogInConsole bool   `json:"logInConsole"`
	ShowLine     bool   `json:"showLine"`
}

// CaptchaConfigResponse 验证码配置响应
type CaptchaConfigResponse struct {
	KeyLong            int  `json:"keyLong"`
	ImgWidth           int  `json:"imgWidth"`
	ImgHeight          int  `json:"imgHeight"`
	OpenCaptcha        bool `json:"openCaptcha"`
	MaxFailedAttempts  int  `json:"maxFailedAttempts"`
	OpenCaptchaTimeOut int  `json:"openCaptchaTimeOut"`
}

// NotificationConfigResponse 通知配置响应
type NotificationConfigResponse struct {
	ClientStatus     bool `json:"clientStatus"`
	DisplayDuration  int  `json:"displayDuration"`
	SoundEnabled     bool `json:"soundEnabled"`
	MaxNotifications int  `json:"maxNotifications"`
}