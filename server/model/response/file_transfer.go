package response

import "time"

// ServerFile 服务器文件信息
type ServerFile struct {
	Name         string    `json:"name"`          // 文件名
	Path         string    `json:"path"`          // 文件路径
	Size         int64     `json:"size"`          // 文件大小
	ModTime      time.Time `json:"mod_time"`      // 修改时间
	IsDir        bool      `json:"is_dir"`        // 是否为目录
	RelativePath string    `json:"relative_path"` // 相对路径（相对于upload或download目录）
}
