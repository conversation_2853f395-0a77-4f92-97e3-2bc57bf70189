package fs

import (
	"server/model/basic"
)

// DirCreateResponse 目录创建响应
type DirCreateResponse struct {
	TaskID      uint64   `json:"task_id"`      // 任务ID
	Success     bool   `json:"success"`      // 操作是否成功
	NotAllow    bool   `json:"not_allow"`    // 是否权限不足
	Exists      bool   `json:"exists"`       // 目录是否已存在
	CreatedPath string `json:"created_path"` // 实际创建的路径
	Error       string `json:"error"`        // 错误信息
}

// DirListResponse 目录列表响应
type DirListResponse struct {
	TaskID       uint64               `json:"task_id"`             // 任务ID
	Path              string             `json:"path"`                // 请求的路径
	ActualPath        string             `json:"actual_path"`         // 实际访问的路径
	NotExists         bool               `json:"not_exists"`          // 目录是否不存在
	NotAllow          bool               `json:"not_allow"`           // 是否权限不足
	FileInfoResponses []FileInfoResponse `json:"file_info_responses"` // 文件信息列表
	TotalCount        int                `json:"total_count"`         // 总文件数量
	Error             string             `json:"error"`               // 错误信息
	// 分页信息
	Page       int  `json:"page"`        // 当前页码
	PageSize   int  `json:"page_size"`   // 每页大小
	TotalPages int  `json:"total_pages"` // 总页数
	HasMore    bool `json:"has_more"`    // 是否还有更多数据
}

// DirMoveResponse 目录移动响应
type DirMoveResponse struct {
	TaskID       uint64   `json:"task_id"`            // 任务ID
	Success           bool   `json:"success"`            // 操作是否成功
	SourceExists      bool   `json:"source_exists"`      // 源目录是否存在
	DestinationExists bool   `json:"destination_exists"` // 目标目录是否存在
	NotAllow          bool   `json:"not_allow"`          // 是否权限不足
	ActualSource      string `json:"actual_source"`      // 实际源路径
	ActualDestination string `json:"actual_destination"` // 实际目标路径
	Error             string `json:"error"`              // 错误信息
}

// DirDeleteResponse 目录删除响应
type DirDeleteResponse struct {
	TaskID      uint64   `json:"task_id"`      // 任务ID
	Success     bool   `json:"success"`      // 操作是否成功
	NotExists   bool   `json:"not_exists"`   // 目录是否不存在
	NotAllow    bool   `json:"not_allow"`    // 是否权限不足
	NotEmpty    bool   `json:"not_empty"`    // 目录是否非空
	DeletedPath string `json:"deleted_path"` // 实际删除的路径
	Error       string `json:"error"`        // 错误信息
}

// DirCopyResponse 目录复制响应
type DirCopyResponse struct {
	TaskID       uint64   `json:"task_id"`            // 任务ID
	Success           bool   `json:"success"`            // 操作是否成功
	SourceExists      bool   `json:"source_exists"`      // 源目录是否存在
	DestinationExists bool   `json:"destination_exists"` // 目标目录是否存在
	NotAllow          bool   `json:"not_allow"`          // 是否权限不足
	CopiedFiles       int    `json:"copied_files"`       // 已复制的文件数量
	CopiedDirs        int    `json:"copied_dirs"`        // 已复制的目录数量
	SkippedFiles      int    `json:"skipped_files"`      // 跳过的文件数量
	ActualSource      string `json:"actual_source"`      // 实际源路径
	ActualDestination string `json:"actual_destination"` // 实际目标路径
	Error             string `json:"error"`              // 错误信息
}

// DiskListResponse 磁盘列表响应
type DiskListResponse struct {
	TaskID    uint64             `json:"task_id"`    // 任务ID
	Success   bool             `json:"success"`    // 操作是否成功
	DiskInfos []basic.DiskInfo `json:"disk_infos"` // 磁盘信息列表
	Error     string           `json:"error"`      // 错误信息
}
