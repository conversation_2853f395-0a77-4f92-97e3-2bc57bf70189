package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

const (
	ERROR        = 500
	UNAUTHORIZED = 401
	FORBIDDEN    = 403
	SUCCESS      = 200
)

func Result(code int, data interface{}, msg string, c *gin.Context) {
	c.JSON(code, Response{
		Code: code,
		Data: data,
		Msg:  msg,
	})
}

func Ok(c *gin.Context) {
	Result(SUCCESS, map[string]interface{}{}, "操作成功", c)
}

func OkWithMessage(message string, c *gin.Context) {
	Result(SUCCESS, map[string]interface{}{}, message, c)
}

func OkWithData(data interface{}, c *gin.Context) {
	Result(SUCCESS, data, "操作成功", c)
}

func OkWithDetailed(data interface{}, msg string, c *gin.Context) {
	Result(SUCCESS, data, msg, c)
}

func LoginFailedWithMessage(message string, c *gin.Context) {
	Result(FORBIDDEN, map[string]interface{}{}, message, c)
}

func Error(c *gin.Context) {
	Result(ERROR, map[string]interface{}{}, "操作失败", c)
}

func ErrorWithMessage(message string, c *gin.Context) {
	Result(ERROR, map[string]interface{}{}, message, c)
}

func ErrorWithData(data interface{}, c *gin.Context) {
	Result(ERROR, data, "操作失败", c)
}

func ErrorWithDetailed(data interface{}, msg string, c *gin.Context) {
	Result(ERROR, data, msg, c)
}

func NoAuth(message string, c *gin.Context) {
	c.JSON(http.StatusUnauthorized, Response{
		Code: UNAUTHORIZED,
		Data: nil,
		Msg:  message,
	})
}

type PageResult struct {
	List     interface{} `json:"list"`
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"pageSize"`
}
