package network

import "time"

// NetworkStats 网络统计信息
type NetworkStats struct {
	UploadSpeed       float64 `json:"upload_speed"`       // 上传速度 KB/s
	DownloadSpeed     float64 `json:"download_speed"`     // 下载速度 KB/s
	ActiveConnections int     `json:"active_connections"` // 活跃连接数
	PacketLoss        float64 `json:"packet_loss"`        // 丢包率 %
	TotalBytesSent    uint64  `json:"total_bytes_sent"`   // 总发送字节数
	TotalBytesRecv    uint64  `json:"total_bytes_recv"`   // 总接收字节数
	Timestamp         int64   `json:"timestamp"`          // 时间戳
}

// NetInterface 网络接口信息
type NetInterface struct {
	Name          string `json:"name"`           // 接口名称
	Type          string `json:"type"`           // 接口类型
	Status        string `json:"status"`         // 状态
	IPAddress     string `json:"ip_address"`     // IP地址
	MACAddress    string `json:"mac_address"`    // MAC地址
	Speed         uint64 `json:"speed"`          // 接口速度
	BytesSent     uint64 `json:"bytes_sent"`     // 发送字节数
	BytesReceived uint64 `json:"bytes_received"` // 接收字节数
	PacketsSent   uint64 `json:"packets_sent"`   // 发送包数
	PacketsRecv   uint64 `json:"packets_recv"`   // 接收包数
	ErrorsIn      uint64 `json:"errors_in"`      // 输入错误数
	ErrorsOut     uint64 `json:"errors_out"`     // 输出错误数
	DropsIn       uint64 `json:"drops_in"`       // 输入丢包数
	DropsOut      uint64 `json:"drops_out"`      // 输出丢包数
}

// NetworkConnection 网络连接信息
type NetworkConnection struct {
	ID              string    `json:"id"`               // 连接唯一标识
	Protocol        string    `json:"protocol"`         // 协议
	LocalAddress    string    `json:"local_address"`    // 本地地址
	LocalPort       int       `json:"local_port"`       // 本地端口
	RemoteAddress   string    `json:"remote_address"`   // 远程地址
	RemotePort      int       `json:"remote_port"`      // 远程端口
	State           string    `json:"state"`            // 连接状态
	ProcessName     string    `json:"process_name"`     // 进程名称
	PID             int       `json:"pid"`              // 进程ID
	EstablishedTime time.Time `json:"established_time"` // 建立时间
	Duration        string    `json:"duration"`         // 持续时间
	BytesSent       uint64    `json:"bytes_sent"`       // 发送字节数
	BytesReceived   uint64    `json:"bytes_received"`   // 接收字节数
}

// NetworkStatsResponse 网络统计信息响应
type NetworkStatsResponse struct {
	TaskID  uint64       `json:"task_id"`
	Success bool         `json:"success"`
	Error   string       `json:"error"`
	Stats   NetworkStats `json:"stats"`
}

// NetworkInterfacesResponse 网络接口信息响应
type NetworkInterfacesResponse struct {
	TaskID     uint64         `json:"task_id"`
	Success    bool           `json:"success"`
	Error      string         `json:"error"`
	Interfaces []NetInterface `json:"interfaces"`
}

// NetworkConnectionsResponse 网络连接信息响应
type NetworkConnectionsResponse struct {
	TaskID      uint64              `json:"task_id"`
	Success     bool                `json:"success"`
	Error       string              `json:"error"`
	Connections []NetworkConnection `json:"connections"`
	Total       int                 `json:"total"`
}

// CloseConnectionResponse 关闭网络连接响应
type CloseConnectionResponse struct {
	TaskID  uint64 `json:"task_id"`
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Message string `json:"message"`
}
