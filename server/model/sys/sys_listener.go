package sys

import (
	"server/global"
)

// Listener 模型定义
type Listener struct {
	global.DBModel
	UserID            uint   `json:"userId" gorm:"column:user_id;comment:创建者用户ID"`
	Remark            string `json:"remark" gorm:"column:remark;comment:备注"`
	Status            int    `json:"status" gorm:"column:status;default:1;comment:状态 1启用 0禁用"`
	LocalListenAddr   string `json:"localListenAddr" gorm:"column:local_listen_addr;comment:本地监听地址"`
	RemoteConnectAddr string `json:"remoteConnectAddr" gorm:"column:remote_connect_addr;comment:远程连接地址"`
	Type              string `json:"type" gorm:"column:type;comment:监听类型"`
	Key               string `json:"key" gorm:"column:key;comment:加密密钥"`
	Salt              string `json:"salt" gorm:"column:salt;comment:加密盐值"`
	PingDuration      int    `json:"pingDuration" gorm:"column:ping_duration;comment:心跳包间隔(秒)"`
	MaxTimeoutCount   int    `json:"maxTimeoutCount" gorm:"column:max_timeout_count;comment:最大超时次数"`
}

// TableName 设置表名
func (Listener) TableName() string {
	return "sys_listener"
}
