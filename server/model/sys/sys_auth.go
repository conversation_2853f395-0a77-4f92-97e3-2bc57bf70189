package sys

import (
	"server/global"

	"github.com/google/uuid"
)

// 保留基本的认证接口
type Login interface {
	GetUUID() uuid.UUID
	GetUserID() uint
	GetUsername() string
	GetUser() any
	GetRole() Role
}

// 保留基本的用户结构，用于认证
type SysUser struct {
	global.DBModel
	UUID     uuid.UUID `json:"uuid" gorm:"index;unique;not null"`
	Username string    `json:"username" gorm:"index;unique;not null"`
	Password string    `json:"-" gorm:"size:255;not null"`
	Role     Role      `json:"role" gorm:"embedded;not null"`
	Enable   int       `json:"enable" gorm:"default:1;comment:用户是否被冻结 1正常 2冻结"`
}

type Role struct {
	RoleName string `json:"roleName" gorm:"comment: 角色"`
}

func (SysUser) TableName() string {
	return "sys_user"
}

func (s *SysUser) GetRole() Role {
	return s.Role
}

func (s *SysUser) GetUUID() uuid.UUID {
	return s.UUID
}

func (s *SysUser) GetUserID() uint {
	return s.ID
}

func (s *SysUser) GetUsername() string {
	return s.Username
}

func (s *SysUser) GetUser() any {
	return s
}

type SysUserResponse struct {
	User SysUser `json:"user"`
}

type LoginResponse struct {
	User      SysUser `json:"user"`
	Token     string  `json:"token"`
	ExpiresAt int64   `json:"expiresAt"`
}
