package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"os"
	"server/config"
	"server/global"
	"strings"

	"github.com/google/uuid"
)

// AutoConfigHelper 自动配置辅助函数，用于自动检测和设置配置项
func AutoConfigHelper() {
	// 自动检测并设置磁盘列表
	AutoDetectDiskList()

	// 自动生成JWT签名密钥
	AutoGenerateJWTSigningKey()
}

// AutoDetectDiskList 自动检测系统盘符并添加到配置中
func AutoDetectDiskList() {
	// 如果磁盘列表为空，则自动检测系统盘符
	if len(global.CONFIG.DiskList) == 0 {
		global.LOG.Info("未配置磁盘列表，将自动检测系统盘符")

		// 获取系统盘符
		drives := GetSystemDrives()

		if len(drives) > 0 {
			for _, drive := range drives {
				global.CONFIG.DiskList = append(global.CONFIG.DiskList, config.DiskList{
					Disk: config.Disk{MountPoint: drive},
				})
			}
			global.LOG.Info(fmt.Sprintf("自动检测到系统盘符: %v", drives))
		} else {
			global.LOG.Warn("未能检测到系统盘符，将使用默认配置")
			// 添加一个默认的磁盘配置
			global.CONFIG.DiskList = append(global.CONFIG.DiskList, config.DiskList{
				Disk: config.Disk{MountPoint: "/"},
			})
		}
	} else {
		global.LOG.Info("已配置磁盘列表，跳过自动检测")
	}
}

// AutoGenerateJWTSigningKey 自动生成JWT签名密钥
func AutoGenerateJWTSigningKey() {
	// 如果JWT签名密钥为空，则自动生成一个
	if global.CONFIG.JWT.SigningKey == "" {
		global.LOG.Info("未配置JWT签名密钥，将自动生成")

		// 使用UUID生成随机密钥
		newKey := uuid.New().String()

		// 或者使用更安全的随机生成方式
		randomBytes := make([]byte, 32)
		_, err := rand.Read(randomBytes)
		if err == nil {
			newKey = hex.EncodeToString(randomBytes)
		}

		global.CONFIG.JWT.SigningKey = newKey
		global.LOG.Info("已自动生成JWT签名密钥")
	} else {
		global.LOG.Info("已配置JWT签名密钥，跳过自动生成")
	}
}

// GetSystemDrives 获取系统盘符
func GetSystemDrives() []string {
	var drives []string

	// Windows系统
	if os.PathSeparator == '\\' {
		for _, drive := range "ABCDEFGHIJKLMNOPQRSTUVWXYZ" {
			drivePath := string(drive) + ":\\"
			_, err := os.Stat(drivePath)
			if err == nil {
				drives = append(drives, drivePath)
			}
		}
	} else {
		// Unix/Linux系统
		// 检查常见的挂载点
		commonMounts := []string{"/", "/home", "/mnt", "/media"}
		for _, mount := range commonMounts {
			_, err := os.Stat(mount)
			if err == nil {
				drives = append(drives, mount)
			}
		}

		// 尝试读取/proc/mounts获取更多挂载点
		if data, err := os.ReadFile("/proc/mounts"); err == nil {
			lines := strings.Split(string(data), "\n")
			for _, line := range lines {
				fields := strings.Fields(line)
				if len(fields) >= 2 {
					mountPoint := fields[1]
					// 过滤掉系统挂载点
					if !strings.HasPrefix(mountPoint, "/sys") &&
						!strings.HasPrefix(mountPoint, "/proc") &&
						!strings.HasPrefix(mountPoint, "/dev") &&
						!strings.HasPrefix(mountPoint, "/run") &&
						!strings.Contains(mountPoint, "snap") {
						// 检查是否已经添加过
						alreadyAdded := false
						for _, d := range drives {
							if d == mountPoint {
								alreadyAdded = true
								break
							}
						}
						if !alreadyAdded {
							drives = append(drives, mountPoint)
						}
					}
				}
			}
		}
	}

	return drives
}
