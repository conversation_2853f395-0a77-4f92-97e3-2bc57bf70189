package utils

import (
	"os"
	"runtime"
	"server/global"
	"server/model/sys"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/shirou/gopsutil/v3/net"
)

const (
	B  = 1
	KB = 1024 * B
	MB = 1024 * KB
	GB = 1024 * MB
	TB = 1024 * GB
)

type Server struct {
	OS   OS     `json:"os"`
	CPU  CPU    `json:"cpu"`
	RAM  RAM    `json:"ram"`
	Disk []Disk `json:"disk"`
	Net  Net    `json:"net"`
}

type OS struct {
	GOOS          string `json:"goos"`
	NumCPU        int    `json:"numCpu"`
	Compiler      string `json:"compiler"`
	GoVersion     string `json:"goVersion"`
	NumGoroutine  int    `json:"numGoroutine"`
	Hostname      string `json:"hostname"`
	Arch          string `json:"arch"`
	KernelVersion string `json:"kernelVersion"`
	Uptime        int64  `json:"uptime"`
	BootTime      int64  `json:"bootTime"`
}

type CPU struct {
	Cpus  []float64 `json:"cpus"`
	Cores int       `json:"cores"`
}

type RAM struct {
	UsedMB      int `json:"usedMb"`
	TotalMB     int `json:"totalMb"`
	UsedPercent int `json:"usedPercent"`
}

type Disk struct {
	MountPoint  string `json:"mountPoint"`
	UsedMB      int    `json:"usedMb"`
	UsedGB      int    `json:"usedGb"`
	TotalMB     int    `json:"totalMb"`
	TotalGB     int    `json:"totalGb"`
	UsedPercent int    `json:"usedPercent"`
}

type Net struct {
	UploadKBPS   int `json:"uploadKbps"`
	DownloadKBPS int `json:"downloadKbps"`
	UploadMBPS   int `json:"uploadMbps"`
	DownloadMBPS int `json:"downloadMbps"`
}

func InitOS() (o OS) {
	// 基本 Go 运行时信息
	o.GOOS = runtime.GOOS
	o.NumCPU = runtime.NumCPU()
	o.Compiler = runtime.Compiler
	o.GoVersion = runtime.Version()
	o.NumGoroutine = runtime.NumGoroutine()
	o.Arch = runtime.GOARCH

	// 主机信息
	if hostname, err := os.Hostname(); err == nil {
		o.Hostname = hostname
	}

	// 系统信息
	if hostInfo, err := host.Info(); err == nil {
		o.KernelVersion = hostInfo.KernelVersion
		o.BootTime = int64(hostInfo.BootTime)
		o.Uptime = int64(hostInfo.Uptime)
	}

	return o
}

func InitCPU() (c CPU, err error) {
	if cores, err := cpu.Counts(false); err != nil {
		return c, err
	} else {
		c.Cores = cores
	}
	if cpus, err := cpu.Percent(time.Duration(200)*time.Millisecond, true); err != nil {
		return c, err
	} else {
		c.Cpus = cpus
	}
	return c, nil
}

func InitRAM() (r RAM, err error) {
	if u, err := mem.VirtualMemory(); err != nil {
		return r, err
	} else {
		r.UsedMB = int(u.Used) / MB
		r.TotalMB = int(u.Total) / MB
		r.UsedPercent = int(u.UsedPercent)
	}
	return r, nil
}

func InitDisk() (d []Disk, err error) {
	for i := range global.CONFIG.DiskList {
		mp := global.CONFIG.DiskList[i].MountPoint
		if u, err := disk.Usage(mp); err != nil {
			return d, err
		} else {
			d = append(d, Disk{
				MountPoint:  mp,
				UsedMB:      int(u.Used) / MB,
				UsedGB:      int(u.Used) / GB,
				TotalMB:     int(u.Total) / MB,
				TotalGB:     int(u.Total) / GB,
				UsedPercent: int(u.UsedPercent),
			})
		}
	}
	return d, nil
}

var (
	netMutex      sync.RWMutex
	prevSent      uint64
	prevRecv      uint64
	lastStat      time.Time
	isInitialized bool
)

// 判断是否为有效的网络接口
func isValidNetworkInterface(name string) bool {
	// 排除的接口类型
	excludedPrefixes := []string{
		"lo",       // 回环接口
		"Loopback", // Windows回环接口
		"utun",     // macOS隧道接口
		"awdl",     // Apple Wireless Direct Link
		"llw",      // Low Latency WLAN
		"bridge",   // 桥接接口
		"vmenet",   // VMware虚拟网络接口
		"vnic",     // 虚拟网卡
		"docker",   // Docker接口
		"veth",     // 虚拟以太网接口
		"tap",      // TAP接口
		"tun",      // TUN接口
		"ap",       // 接入点接口
	}

	for _, prefix := range excludedPrefixes {
		if strings.HasPrefix(strings.ToLower(name), strings.ToLower(prefix)) {
			return false
		}
	}

	return true
}

// 获取主要网络接口的统计信息
func getNetStats() (sent, recv uint64, err error) {
	stats, err := net.IOCounters(true) // 获取每个接口的详细信息
	if err != nil || len(stats) == 0 {
		return 0, 0, err
	}

	// 找到流量最大的有效接口
	var maxTrafficInterface *net.IOCountersStat
	var maxTraffic uint64

	for i := range stats {
		stat := &stats[i]
		if !isValidNetworkInterface(stat.Name) {
			continue
		}

		// 计算总流量（上传+下载）
		totalTraffic := stat.BytesSent + stat.BytesRecv
		if totalTraffic > maxTraffic {
			maxTraffic = totalTraffic
			maxTrafficInterface = stat
		}
	}

	// 如果找到有效接口，使用该接口的数据
	if maxTrafficInterface != nil {
		return maxTrafficInterface.BytesSent, maxTrafficInterface.BytesRecv, nil
	}

	// 如果没有找到有效接口，回退到汇总所有非回环接口
	var totalSent, totalRecv uint64
	for _, stat := range stats {
		if stat.Name != "lo" && stat.Name != "Loopback" {
			totalSent += stat.BytesSent
			totalRecv += stat.BytesRecv
		}
	}

	return totalSent, totalRecv, nil
}

// 存储上一次的网络速率结果
var (
	lastUploadKbps   float64
	lastDownloadKbps float64
)

// 初始化网络速率统计
func InitNet() (n Net, err error) {
	netMutex.Lock()
	defer netMutex.Unlock()

	now := time.Now()
	currentSent, currentRecv, err := getNetStats()
	if err != nil {
		return n, err
	}

	// 首次初始化
	if !isInitialized {
		prevSent = currentSent
		prevRecv = currentRecv
		lastStat = now
		isInitialized = true
		// 首次调用返回0值，避免异常大的数值
		return Net{
			UploadKBPS:   0,
			DownloadKBPS: 0,
			UploadMBPS:   0,
			DownloadMBPS: 0,
		}, nil
	}

	// 计算时间差（秒）
	duration := now.Sub(lastStat).Seconds()
	if duration < 0.5 { // 增加最小时间间隔到0.5秒，提高准确性
		// 时间间隔太短，返回上次的结果
		return Net{
			UploadKBPS:   int(lastUploadKbps),
			DownloadKBPS: int(lastDownloadKbps),
			UploadMBPS:   int(lastUploadKbps / 1024),
			DownloadMBPS: int(lastDownloadKbps / 1024),
		}, nil
	}

	// 计算字节差值
	sentDiff := currentSent - prevSent
	recvDiff := currentRecv - prevRecv

	// 检测计数器重置（网络接口重启等情况）
	if currentSent < prevSent || currentRecv < prevRecv {
		// 计数器重置，重新初始化
		prevSent = currentSent
		prevRecv = currentRecv
		lastStat = now
		return Net{
			UploadKBPS:   0,
			DownloadKBPS: 0,
			UploadMBPS:   0,
			DownloadMBPS: 0,
		}, nil
	}

	// 计算速率（KB/s）
	uploadKbps := float64(sentDiff) / duration / 1024
	downloadKbps := float64(recvDiff) / duration / 1024

	// 🚀 不再生成模拟数据，如实显示真实速度（哪怕很小）

	// 应用平滑处理，避免数值跳跃过大
	if lastUploadKbps > 0 && lastDownloadKbps > 0 {
		// 使用指数移动平均进行平滑
		alpha := 0.3 // 平滑系数
		uploadKbps = alpha*uploadKbps + (1-alpha)*lastUploadKbps
		downloadKbps = alpha*downloadKbps + (1-alpha)*lastDownloadKbps
	}

	// 更新全局变量
	prevSent = currentSent
	prevRecv = currentRecv
	lastStat = now
	lastUploadKbps = uploadKbps
	lastDownloadKbps = downloadKbps

	return Net{
		UploadKBPS:   int(uploadKbps),
		DownloadKBPS: int(downloadKbps),
		UploadMBPS:   int(uploadKbps / 1024),
		DownloadMBPS: int(downloadKbps / 1024),
	}, nil
}

// GetUserID 从gin context中获取用户ID
func GetUserID(c *gin.Context) uint {
	claims, exists := c.Get("claims")
	if !exists {
		return 0
	}

	customClaims, ok := claims.(*sys.CustomClaims)
	if !ok {
		return 0
	}

	return customClaims.BaseClaims.ID
}
