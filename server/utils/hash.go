package utils

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"os"
	"server/core/manager/workerpool"
	"server/global"
	"sync"

	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

// BcryptHash 生成bcrypt哈希 - 🚀 优化：CPU密集型操作使用处理池
func BcryptHash(password string) string {
	// bcrypt是CPU密集型操作，使用处理池
	var result string
	var hashErr error
	var wg sync.WaitGroup

	wg.Add(1)
	err := workerpool.SubmitEncryptionTask("bcrypt_hash", func() error {
		defer wg.Done()
		bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if err != nil {
			hashErr = err
			return err
		}
		result = string(bytes)
		return nil
	})

	if err != nil {
		// 回退到直接处理
		global.LOG.Warn("提交bcrypt哈希任务失败，回退到直接处理", zap.Error(err))
		bytes, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		return string(bytes)
	}

	wg.Wait()
	if hashErr != nil {
		// 如果处理池中出错，回退到直接处理
		bytes, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		return string(bytes)
	}
	return result
}

// BcryptCheck 验证bcrypt哈希 - 🚀 优化：CPU密集型操作使用处理池
func BcryptCheck(password, hash string) bool {
	// bcrypt验证是CPU密集型操作，使用处理池
	var result bool
	var wg sync.WaitGroup

	wg.Add(1)
	err := workerpool.SubmitEncryptionTask("bcrypt_check", func() error {
		defer wg.Done()
		err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
		result = (err == nil)
		return nil // 总是返回nil，因为验证失败不是错误
	})

	if err != nil {
		// 回退到直接处理
		global.LOG.Warn("提交bcrypt验证任务失败，回退到直接处理", zap.Error(err))
		err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
		return err == nil
	}

	wg.Wait()
	return result
}

func MD5V(str []byte, b ...byte) string {
	h := md5.New()
	h.Write(str)
	return hex.EncodeToString(h.Sum(b))
}

// CalculateFileHash 计算文件的SHA256哈希值 - 🚀 优化：大文件使用处理池
func CalculateFileHash(filePath string) (string, error) {
	// 检查文件大小，大文件使用处理池
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return "", err
	}

	// 大于10MB的文件使用处理池
	if fileInfo.Size() > 10*1024*1024 {
		return calculateFileHashWithPool(filePath)
	}

	// 小文件直接计算
	return calculateFileHashDirect(filePath)
}

// calculateFileHashDirect 直接计算文件哈希
func calculateFileHashDirect(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := sha256.New()
	if _, err = io.Copy(hash, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// calculateFileHashWithPool 使用处理池计算文件哈希
func calculateFileHashWithPool(filePath string) (string, error) {
	var result string
	var hashErr error
	var wg sync.WaitGroup

	wg.Add(1)
	err := workerpool.SubmitEncryptionTask("file_hash_calculation", func() error {
		defer wg.Done()
		result, hashErr = calculateFileHashDirect(filePath)
		return hashErr
	})

	if err != nil {
		// 回退到直接计算
		global.LOG.Warn("提交文件哈希计算任务失败，回退到直接处理",
			zap.Error(err), zap.String("filePath", filePath))
		return calculateFileHashDirect(filePath)
	}

	wg.Wait()
	return result, hashErr
}

// CalculateMultipartFileHash 计算multipart.File的SHA256哈希值 - 🚀 优化：使用处理池
func CalculateMultipartFileHash(file io.Reader) (string, error) {
	// 对于multipart文件，我们无法预先知道大小，所以总是使用处理池
	var result string
	var hashErr error
	var wg sync.WaitGroup

	wg.Add(1)
	err := workerpool.SubmitEncryptionTask("multipart_file_hash", func() error {
		defer wg.Done()
		hash := sha256.New()
		if _, err := io.Copy(hash, file); err != nil {
			hashErr = err
			return err
		}
		result = fmt.Sprintf("%x", hash.Sum(nil))
		return nil
	})

	if err != nil {
		// 回退到直接计算
		global.LOG.Warn("提交multipart文件哈希计算任务失败，回退到直接处理", zap.Error(err))
		hash := sha256.New()
		if _, err := io.Copy(hash, file); err != nil {
			return "", err
		}
		return fmt.Sprintf("%x", hash.Sum(nil)), nil
	}

	wg.Wait()
	return result, hashErr
}

// CalculateMultipartFileHashAsync 异步计算multipart.File的SHA256哈希值 - 🚀 新增：异步接口
func CalculateMultipartFileHashAsync(file io.Reader, callback func(string, error)) error {
	return workerpool.SubmitEncryptionTask("async_multipart_file_hash", func() error {
		hash := sha256.New()
		if _, err := io.Copy(hash, file); err != nil {
			callback("", err)
			return err
		}
		result := fmt.Sprintf("%x", hash.Sum(nil))
		callback(result, nil)
		return nil
	})
}
