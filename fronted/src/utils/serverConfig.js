import axios from 'axios'

// 默认配置，用于开发环境
const defaultConfig = {
  apiBaseUrl: import.meta.env.VITE_APP_BASE_API,
  wsBaseUrl: `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${import.meta.env.VITE_APP_BASE_IPADDR}${import.meta.env.VITE_APP_BASE_API}`,
  serverPort: 8888,
  version: '1.0.0'
}

// 服务器配置
let serverConfig = { ...defaultConfig }

/**
 * 获取服务器配置
 * 在应用启动时调用此函数获取后端配置
 * @returns {Promise<Object>} 配置对象
 */
export const fetchServerConfig = async () => {
  try {
    // 尝试从当前域名获取配置
    const currentHost = window.location.host
    const currentProtocol = window.location.protocol
    
    // 构建配置API URL
    // 如果是开发环境，使用环境变量中的地址
    // 如果是生产环境，使用当前域名
    let configUrl
    if (import.meta.env.DEV) {
      configUrl = `${import.meta.env.VITE_APP_BASE_URL}/api/config`
    } else {
      configUrl = `${currentProtocol}//${currentHost}/api/config`
    }
    
    const response = await axios.get(configUrl)
    
    if (response.data && response.data.code === 200) {
      serverConfig = response.data.data
      console.log('成功获取服务器配置:', serverConfig)
      return serverConfig
    }
    
    return defaultConfig
  } catch (error) {
    console.error('获取服务器配置失败:', error)
    console.log('使用默认配置:', defaultConfig)
    return defaultConfig
  }
}

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
export const getApiBaseUrl = () => serverConfig.apiBaseUrl

/**
 * 获取WebSocket基础URL
 * @returns {string} WebSocket基础URL
 */
export const getWsBaseUrl = () => serverConfig.wsBaseUrl

/**
 * 获取服务器端口
 * @returns {number} 服务器端口
 */
export const getServerPort = () => serverConfig.serverPort

/**
 * 获取服务器版本
 * @returns {string} 服务器版本
 */
export const getServerVersion = () => serverConfig.version

export default {
  fetchServerConfig,
  getApiBaseUrl,
  getWsBaseUrl,
  getServerPort,
  getServerVersion
}