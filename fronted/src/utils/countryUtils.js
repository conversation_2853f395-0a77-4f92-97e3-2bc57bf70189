// 中文国家名到ISO国家代码的映射
const countryNameMap = {
  // 中文名 -> ISO 3166-1 alpha-2 代码
  '中国': 'CN',
  '美国': 'US',
  '日本': 'JP',
  '韩国': 'KR',
  '德国': 'DE',
  '英国': 'GB',
  '法国': 'FR',
  '加拿大': 'CA',
  '澳大利亚': 'AU',
  '俄罗斯': 'RU',
  '印度': 'IN',
  '巴西': 'BR',
  '新加坡': 'SG',
  '香港': 'HK',
  '台湾': 'TW',
  '意大利': 'IT',
  '西班牙': 'ES',
  '荷兰': 'NL',
  '瑞士': 'CH',
  '瑞典': 'SE',
  '挪威': 'NO',
  '丹麦': 'DK',
  '芬兰': 'FI',
  '比利时': 'BE',
  '奥地利': 'AT',
  '波兰': 'PL',
  '捷克': 'CZ',
  '匈牙利': 'HU',
  '希腊': 'GR',
  '葡萄牙': 'PT',
  '土耳其': 'TR',
  '以色列': 'IL',
  '阿联酋': 'AE',
  '沙特阿拉伯': 'SA',
  '埃及': 'EG',
  '南非': 'ZA',
  '墨西哥': 'MX',
  '阿根廷': 'AR',
  '智利': 'CL',
  '哥伦比亚': 'CO',
  '秘鲁': 'PE',
  '委内瑞拉': 'VE',
  '泰国': 'TH',
  '越南': 'VN',
  '马来西亚': 'MY',
  '印度尼西亚': 'ID',
  '菲律宾': 'PH',
  '缅甸': 'MM',
  '柬埔寨': 'KH',
  '老挝': 'LA',
  '孟加拉国': 'BD',
  '巴基斯坦': 'PK',
  '斯里兰卡': 'LK',
  '尼泊尔': 'NP',
  '不丹': 'BT',
  '马尔代夫': 'MV',
  
  // 英文名保持不变
  'China': 'CN',
  'United States': 'US',
  'Japan': 'JP',
  'South Korea': 'KR',
  'Germany': 'DE',
  'United Kingdom': 'GB',
  'France': 'FR',
  'Canada': 'CA',
  'Australia': 'AU',
  'Russia': 'RU',
  'India': 'IN',
  'Brazil': 'BR',
  'Singapore': 'SG',
  'Hong Kong': 'HK',
  'Taiwan': 'TW',
  'Italy': 'IT',
  'Spain': 'ES',
  'Netherlands': 'NL',
  'Switzerland': 'CH',
  'Sweden': 'SE',
  'Norway': 'NO',
  'Denmark': 'DK',
  'Finland': 'FI',
  'Belgium': 'BE',
  'Austria': 'AT',
  'Poland': 'PL',
  'Czech Republic': 'CZ',
  'Hungary': 'HU',
  'Greece': 'GR',
  'Portugal': 'PT',
  'Turkey': 'TR',
  'Israel': 'IL',
  'United Arab Emirates': 'AE',
  'Saudi Arabia': 'SA',
  'Egypt': 'EG',
  'South Africa': 'ZA',
  'Mexico': 'MX',
  'Argentina': 'AR',
  'Chile': 'CL',
  'Colombia': 'CO',
  'Peru': 'PE',
  'Venezuela': 'VE',
  'Thailand': 'TH',
  'Vietnam': 'VN',
  'Malaysia': 'MY',
  'Indonesia': 'ID',
  'Philippines': 'PH',
  'Myanmar': 'MM',
  'Cambodia': 'KH',
  'Laos': 'LA',
  'Bangladesh': 'BD',
  'Pakistan': 'PK',
  'Sri Lanka': 'LK',
  'Nepal': 'NP',
  'Bhutan': 'BT',
  'Maldives': 'MV'
};

/**
 * 获取国家代码
 * @param {string} countryName - 国家名（中文或英文）
 * @returns {string} ISO 3166-1 alpha-2 国家代码
 */
export const getCountryCode = (countryName) => {
  if (!countryName) return null;
  return countryNameMap[countryName] || null;
};

/**
 * 获取国旗SVG路径
 * @param {string} countryName - 国家名（中文或英文）
 * @returns {string|null} 国旗SVG路径或null
 */
export const getCountryFlagSvg = (countryName) => {
  const countryCode = getCountryCode(countryName);
  if (!countryCode) return null;
  
  try {
    // 动态导入country-flag-icons的SVG
    return `/node_modules/country-flag-icons/3x2/${countryCode}.svg`;
  } catch (error) {
    console.warn('Failed to load country flag:', error);
    return null;
  }
};

/**
 * 获取国家显示名称（统一为中文）
 * @param {string} countryName - 国家名（中文或英文）
 * @returns {string} 中文国家名
 */
export const getCountryDisplayName = (countryName) => {
  if (!countryName) return '未知';
  
  // 如果已经是中文，直接返回
  const chineseNames = Object.keys(countryNameMap).filter(name => /[\u4e00-\u9fa5]/.test(name));
  if (chineseNames.includes(countryName)) {
    return countryName;
  }
  
  // 如果是英文，查找对应的中文名
  const countryCode = countryNameMap[countryName];
  if (countryCode) {
    const chineseName = Object.keys(countryNameMap).find(name => 
      /[\u4e00-\u9fa5]/.test(name) && countryNameMap[name] === countryCode
    );
    return chineseName || countryName;
  }
  
  return countryName;
};

export default {
  getCountryCode,
  getCountryFlagSvg,
  getCountryDisplayName
};
