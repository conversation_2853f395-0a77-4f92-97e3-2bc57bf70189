import { ref, reactive } from 'vue'
import { createNotificationSSE, disconnectNotificationSSE } from '@/api/notification'

/**
 * 通知管理器
 * 统一管理通知状态、SSE连接和通知显示
 */
class NotificationManager {
  constructor() {
    // 通知弹窗组件引用
    this.popupRef = ref(null)

    // 通知中心组件引用
    this.centerRef = ref(null)

    // 通知统计
    this.stats = reactive({
      unreadCount: 0,
      totalCount: 0
    })

    // SSE连接状态
    this.sseConnected = ref(false)

    // 事件监听器
    this.listeners = {
      notification: [],
      statsUpdate: [],
      connectionChange: []
    }

    // SSE连接实例
    this.sseConnection = null

    // 通知配置
    this.config = reactive({
      clientStatus: true,
      displayDuration: 5000,
      soundEnabled: true,
      maxNotifications: 20
    })

    // 不在构造函数中自动连接SSE，只在用户登录后手动连接
  }

  /**
   * 初始化通知管理器配置
   */
  init(config = {}) {
    console.log('🔧 初始化通知管理器配置:', config)

    // 更新配置
    if (config.clientStatus !== undefined) {
      this.config.clientStatus = config.clientStatus
    }
    if (config.displayDuration !== undefined) {
      this.config.displayDuration = config.displayDuration
    }
    if (config.soundEnabled !== undefined) {
      this.config.soundEnabled = config.soundEnabled
    }
    if (config.maxNotifications !== undefined) {
      this.config.maxNotifications = config.maxNotifications
    }

    console.log('✅ 通知管理器配置已更新:', this.config)
  }

  /**
   * 更新设置 - 实时生效
   */
  updateSettings(settings) {
    console.log('🔄 更新通知管理器设置:', settings)
    this.init(settings)
  }

  /**
   * 启动通知系统 - 只在用户登录后调用
   */
  start() {
    console.log('🚀 启动通知系统...')

    // 检查是否已有token
    const token = localStorage.getItem('token')
    if (!token) {
      console.warn('⚠️ 未找到登录token，跳过通知SSE连接')
      return
    }

    console.log('✅ 找到登录token，准备建立SSE连接')

    // 如果已经连接，先断开
    if (this.sseConnection) {
      console.log('🔄 检测到已有SSE连接，先断开...')
      this.disconnect()
    }

    console.log('📡 开始初始化SSE连接...')
    this.initSSEConnection()
  }

  /**
   * 初始化SSE连接 - 完全模仿dashboard实现
   */
  initSSEConnection() {
    console.log('🔧 开始创建通知SSE连接...')

    this.sseConnection = createNotificationSSE({
      onOpen: (event) => {
        console.log('✅ 通知SSE连接已建立', event)
        this.sseConnected.value = true
        this.emit('connectionChange', { connected: true })
      },

      onNotification: (data) => {
        console.log('📨 通知管理器收到通知:', data)
        this.handleNotification(data)
      },

      onError: (event) => {
        console.error('❌ 通知SSE连接错误:', event)
        this.sseConnected.value = false
        this.emit('connectionChange', { connected: false, error: event })
      },

      onClose: (event) => {
        console.log('🔌 通知SSE连接已关闭', event)
        this.sseConnected.value = false
        this.emit('connectionChange', { connected: false })
      }
    })

    console.log('📡 通知SSE连接实例已创建:', !!this.sseConnection)
  }

  /**
   * 处理收到的通知
   */
  handleNotification(data) {
    console.log('🔍 处理通知数据:', data)
    console.log('🔍 通知数据结构:', {
      hasStats: !!data.stats,
      hasNotification: !!data.notification,
      popupRefExists: !!this.popupRef.value,
      centerRefExists: !!this.centerRef.value
    })

    // 更新统计
    if (data.stats) {
      console.log('📊 更新通知统计:', data.stats)
      this.updateStats(data.stats)
    }

    // 显示弹窗通知
    if (data.notification && this.popupRef.value) {
      console.log('🎯 准备显示弹窗通知:', data.notification)
      this.showPopupNotification(data.notification)
    } else {
      console.warn('⚠️ 无法显示弹窗通知:', {
        hasNotification: !!data.notification,
        hasPopupRef: !!this.popupRef.value,
        popupRefValue: this.popupRef.value
      })
    }

    // 刷新通知中心
    if (this.centerRef.value && this.centerRef.value.loadNotifications) {
      console.log('🔄 刷新通知中心')
      this.centerRef.value.loadNotifications()
    }

    // 触发事件
    this.emit('notification', data)
  }

  /**
   * 显示弹窗通知
   */
  showPopupNotification(notification) {
    console.log('🎯 showPopupNotification 被调用:', notification)

    if (!this.popupRef.value || !this.popupRef.value.addNotification) {
      console.warn('⚠️ 通知弹窗组件未注册')
      return
    }

    // 🚨 修复：检查通知内容是否为空
    if (!notification.title && !notification.content) {
      console.warn('⚠️ 通知内容为空，跳过显示:', notification)
      return
    }

    // 检查是否启用了客户端状态通知
    if (notification.type && notification.type.includes('client') && !this.config.clientStatus) {
      console.log('📵 客户端状态通知已禁用，跳过显示')
      return
    }

    // 根据通知类型设置不同的显示配置
    const config = this.getNotificationConfig(notification)

    console.log('✅ 准备添加弹窗通知:', {
      title: notification.title,
      content: notification.content,
      type: notification.type,
      config: config
    })

    this.popupRef.value.addNotification({
      ...notification,
      ...config,
      duration: this.config.displayDuration, // 使用配置的显示时长
      soundEnabled: this.config.soundEnabled // 使用配置的声音设置
    })
  }

  /**
   * 获取通知配置
   */
  getNotificationConfig(notification) {
    const configs = {
      // 客户端事件
      client_online: {
        level: 'success',
        duration: 4000,
        clickable: true
      },
      client_offline: {
        level: 'warning',
        duration: 5000,
        clickable: true
      },
      client_deleted: {
        level: 'info',
        duration: 3000,
        clickable: false
      },
      
      // 监听器事件
      listener_created: {
        level: 'success',
        duration: 4000,
        clickable: true
      },
      listener_deleted: {
        level: 'info',
        duration: 3000,
        clickable: false
      },
      listener_closed: {
        level: 'warning',
        duration: 5000,
        clickable: true
      }
    }
    
    return configs[notification.type] || {
      level: notification.level || 'info',
      duration: 5000,
      clickable: true
    }
  }

  /**
   * 更新统计信息
   */
  updateStats(stats) {
    if (stats.unread_count !== undefined) {
      this.stats.unreadCount = stats.unread_count
    }
    if (stats.total_count !== undefined) {
      this.stats.totalCount = stats.total_count
    }
    
    this.emit('statsUpdate', this.stats)
  }

  /**
   * 注册通知弹窗组件
   */
  registerPopup(popupComponent) {
    this.popupRef.value = popupComponent
    console.log('📱 通知弹窗组件已注册')
  }

  /**
   * 注册通知中心组件
   */
  registerCenter(centerComponent) {
    this.centerRef.value = centerComponent
    console.log('🏢 通知中心组件已注册')
  }

  /**
   * 手动发送测试通知
   */
  sendTestNotification(type = 'client_online') {
    const testNotifications = {
      client_online: {
        type: 'client_online',
        level: 'success',
        title: '客户端上线',
        content: '客户端 DESKTOP-TEST (*************) 已上线',
        data: {
          client_id: 123,
          client_name: 'DESKTOP-TEST',
          client_ip: '*************'
        }
      },
      client_offline: {
        type: 'client_offline',
        level: 'warning',
        title: '客户端离线',
        content: '客户端 DESKTOP-TEST (*************) 已离线',
        data: {
          client_id: 123,
          client_name: 'DESKTOP-TEST',
          client_ip: '*************'
        }
      },
      listener_created: {
        type: 'listener_created',
        level: 'success',
        title: '监听器创建',
        content: 'TCP监听器已在端口 8080 上创建',
        data: {
          listener_id: 456,
          listener_type: 'tcp',
          port: 8080
        }
      }
    }
    
    const notification = testNotifications[type]
    if (notification) {
      this.handleNotification({ notification })
    }
  }

  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }

  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index > -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`通知事件处理错误 [${event}]:`, error)
        }
      })
    }
  }

  /**
   * 断开SSE连接 - 模仿dashboard实现
   */
  disconnect() {
    disconnectNotificationSSE()
    this.sseConnected.value = false
  }

  /**
   * 重新连接SSE
   */
  reconnect() {
    this.disconnect()
    setTimeout(() => {
      this.initSSEConnection()
    }, 1000)
  }

  /**
   * 获取连接状态
   */
  isConnected() {
    return this.sseConnected.value
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.disconnect()
    this.listeners = {
      notification: [],
      statsUpdate: [],
      connectionChange: []
    }
    this.popupRef.value = null
    this.centerRef.value = null
  }
}

// 创建全局通知管理器实例
const notificationManager = new NotificationManager()

export default notificationManager
