/**
 * 浏览器原生下载管理器
 * 支持断点续传、多文件并发下载、大文件流式处理
 */

import { getApiBaseUrl } from './serverConfig'

class DownloadManager {
  constructor() {
    this.downloads = new Map() // 存储下载任务
    this.maxConcurrent = 3 // 最大并发下载数
    this.activeDownloads = 0 // 当前活跃下载数
    this.downloadQueue = [] // 下载队列
  }

  /**
   * 开始下载文件
   * @param {string} filePath - 服务器文件路径
   * @param {string} fileName - 文件名
   * @param {Function} onProgress - 进度回调
   * @param {Function} onComplete - 完成回调
   * @param {Function} onError - 错误回调
   * @returns {string} downloadId - 下载任务ID
   */
  startDownload(filePath, fileName, onProgress, onComplete, onError) {
    const downloadId = this.generateDownloadId()
    
    const downloadTask = {
      id: downloadId,
      filePath,
      fileName,
      status: 'pending', // pending, downloading, paused, completed, error
      progress: 0,
      downloadedBytes: 0,
      totalBytes: 0,
      startTime: Date.now(),
      onProgress,
      onComplete,
      onError,
      abortController: null,
      url: null
    }

    this.downloads.set(downloadId, downloadTask)

    // 如果当前活跃下载数未达到最大值，立即开始下载
    if (this.activeDownloads < this.maxConcurrent) {
      this.executeDownload(downloadTask)
    } else {
      // 否则加入队列
      this.downloadQueue.push(downloadTask)
    }

    return downloadId
  }

  /**
   * 执行下载任务
   * @param {Object} downloadTask - 下载任务
   */
  async executeDownload(downloadTask) {
    try {
      this.activeDownloads++
      downloadTask.status = 'downloading'
      downloadTask.abortController = new AbortController()

      // 构建下载URL
      const baseUrl = getApiBaseUrl()
      const token = localStorage.getItem('token')
      const downloadUrl = `${baseUrl}/download/server-file?path=${encodeURIComponent(downloadTask.filePath)}`

      // 首先获取文件大小
      const headResponse = await fetch(downloadUrl, {
        method: 'HEAD',
        headers: {
          'X-Token': token
        },
        signal: downloadTask.abortController.signal
      })

      if (!headResponse.ok) {
        // 处理401未授权错误
        if (headResponse.status === 401) {
          localStorage.removeItem('token')
          setTimeout(() => {
            location.reload()
          }, 1500)
          throw new Error('未授权，请重新登录')
        }
        throw new Error(`HTTP ${headResponse.status}: ${headResponse.statusText}`)
      }

      const contentLength = headResponse.headers.get('content-length')
      downloadTask.totalBytes = contentLength ? parseInt(contentLength) : 0

      // 检查浏览器是否支持断点续传
      const acceptRanges = headResponse.headers.get('accept-ranges')
      const supportsRanges = acceptRanges === 'bytes'

      if (supportsRanges && downloadTask.totalBytes > 0) {
        // 使用断点续传下载
        await this.downloadWithRanges(downloadTask, downloadUrl, token)
      } else {
        // 使用普通下载
        await this.downloadNormally(downloadTask, downloadUrl, token)
      }

    } catch (error) {
      if (error.name !== 'AbortError') {
        downloadTask.status = 'error'
        downloadTask.onError?.(error.message)
      }
    } finally {
      this.activeDownloads--
      this.processQueue()
    }
  }

  /**
   * 使用断点续传下载
   * @param {Object} downloadTask - 下载任务
   * @param {string} downloadUrl - 下载URL
   * @param {string} token - 认证token
   */
  async downloadWithRanges(downloadTask, downloadUrl, token) {
    // 直接使用浏览器原生下载，避免内存消耗
    const urlWithToken = `${downloadUrl}&token=${encodeURIComponent(token)}`
    
    // 创建一个临时的下载链接
    const link = document.createElement('a')
    link.href = urlWithToken
    link.download = downloadTask.fileName
    link.style.display = 'none'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 标记为已开始下载
    downloadTask.status = 'downloading'
    downloadTask.onProgress?.({
      downloadedBytes: 0,
      totalBytes: downloadTask.totalBytes,
      progress: 0,
      speed: 0
    })

    // 立即标记为完成（浏览器会接管下载过程）
    downloadTask.status = 'completed'
    downloadTask.progress = 100
    downloadTask.onComplete?.()
  }

  /**
   * 使用普通方式下载
   * @param {Object} downloadTask - 下载任务
   * @param {string} downloadUrl - 下载URL
   * @param {string} token - 认证token
   */
  async downloadNormally(downloadTask, downloadUrl, token) {
    // 对于普通下载也使用浏览器原生下载，避免内存消耗
    const urlWithToken = `${downloadUrl}&token=${encodeURIComponent(token)}`
    
    // 创建一个临时的下载链接
    const link = document.createElement('a')
    link.href = urlWithToken
    link.download = downloadTask.fileName
    link.style.display = 'none'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 标记为已开始下载
    downloadTask.status = 'downloading'
    downloadTask.onProgress?.({
      downloadedBytes: 0,
      totalBytes: downloadTask.totalBytes,
      progress: 0,
      speed: 0
    })

    // 立即标记为完成（浏览器会接管下载过程）
    downloadTask.status = 'completed'
    downloadTask.progress = 100
    downloadTask.onComplete?.()
  }

  /**
   * 暂停下载
   * @param {string} downloadId - 下载任务ID
   */
  pauseDownload(downloadId) {
    const downloadTask = this.downloads.get(downloadId)
    if (downloadTask && downloadTask.status === 'downloading') {
      downloadTask.abortController?.abort()
      downloadTask.status = 'paused'
    }
  }

  /**
   * 恢复下载
   * @param {string} downloadId - 下载任务ID
   */
  resumeDownload(downloadId) {
    const downloadTask = this.downloads.get(downloadId)
    if (downloadTask && downloadTask.status === 'paused') {
      if (this.activeDownloads < this.maxConcurrent) {
        this.executeDownload(downloadTask)
      } else {
        this.downloadQueue.push(downloadTask)
        downloadTask.status = 'pending'
      }
    }
  }

  /**
   * 取消下载
   * @param {string} downloadId - 下载任务ID
   */
  cancelDownload(downloadId) {
    const downloadTask = this.downloads.get(downloadId)
    if (downloadTask) {
      downloadTask.abortController?.abort()
      downloadTask.status = 'cancelled'
      
      // 从队列中移除
      const queueIndex = this.downloadQueue.findIndex(task => task.id === downloadId)
      if (queueIndex !== -1) {
        this.downloadQueue.splice(queueIndex, 1)
      }
      
      this.downloads.delete(downloadId)
    }
  }

  /**
   * 获取下载任务信息
   * @param {string} downloadId - 下载任务ID
   * @returns {Object|null} 下载任务信息
   */
  getDownloadInfo(downloadId) {
    const downloadTask = this.downloads.get(downloadId)
    if (!downloadTask) return null

    return {
      id: downloadTask.id,
      fileName: downloadTask.fileName,
      status: downloadTask.status,
      progress: downloadTask.progress,
      downloadedBytes: downloadTask.downloadedBytes,
      totalBytes: downloadTask.totalBytes,
      startTime: downloadTask.startTime
    }
  }

  /**
   * 获取所有下载任务
   * @returns {Array} 下载任务列表
   */
  getAllDownloads() {
    return Array.from(this.downloads.values()).map(task => this.getDownloadInfo(task.id))
  }

  /**
   * 处理下载队列
   */
  processQueue() {
    while (this.downloadQueue.length > 0 && this.activeDownloads < this.maxConcurrent) {
      const nextTask = this.downloadQueue.shift()
      if (nextTask.status === 'pending') {
        this.executeDownload(nextTask)
      }
    }
  }

  /**
   * 生成下载任务ID
   * @returns {string} 唯一ID
   */
  generateDownloadId() {
    return `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 格式化下载速度
   * @param {number} bytesPerSecond - 每秒字节数
   * @returns {string} 格式化后的速度
   */
  static formatSpeed(bytesPerSecond) {
    return this.formatFileSize(bytesPerSecond) + '/s'
  }
}

// 创建全局下载管理器实例
export const downloadManager = new DownloadManager()
export default DownloadManager