/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小字符串
 */
export function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B';
  if (typeof bytes !== 'number' || isNaN(bytes)) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const k = 1024;
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  if (i >= units.length) {
    return (bytes / Math.pow(k, units.length - 1)).toFixed(2) + ' ' + units[units.length - 1];
  }
  
  return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + units[i];
}

/**
 * 格式化日期时间
 * @param {string|Date} date - 日期字符串或Date对象
 * @param {string} format - 格式化模式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */

export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';

  let dateObj;
  if (typeof date === 'string') {
    // 直接尝试ISO格式解析（保留时区信息）
    dateObj = new Date(date);
  } else if (date instanceof Date) {
    dateObj = date;
  } else {
    return '';
  }

  // 验证日期有效性
  if (isNaN(dateObj.getTime())) {
    return '';
  }

  // 手动构建时间组件（避免Intl的问题）
  const pad = num => String(num).padStart(2, '0');
  const year = dateObj.getFullYear();
  const month = pad(dateObj.getMonth() + 1);
  const day = pad(dateObj.getDate());
  const hours = pad(dateObj.getHours());
  const minutes = pad(dateObj.getMinutes());
  const seconds = pad(dateObj.getSeconds());

  return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
}

/**
 * 格式化字节数（formatBytes 别名）
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的字节字符串
 */
export function formatBytes(bytes) {
  return formatFileSize(bytes);
}

/**
 * 格式化时间（相对时间或绝对时间）
 * @param {string|Date|number} time - 时间
 * @param {string} type - 格式类型：'relative'(相对时间) 或 'absolute'(绝对时间)
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time, type = 'absolute') {
  if (!time) return '';

  let dateObj;
  if (typeof time === 'number') {
    // 时间戳
    dateObj = new Date(time);
  } else if (typeof time === 'string') {
    dateObj = new Date(time);
  } else if (time instanceof Date) {
    dateObj = time;
  } else {
    return '';
  }

  // 验证日期有效性
  if (isNaN(dateObj.getTime())) {
    return '';
  }

  if (type === 'relative') {
    // 相对时间
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
      return `${diffSec}秒前`;
    } else if (diffMin < 60) {
      return `${diffMin}分钟前`;
    } else if (diffHour < 24) {
      return `${diffHour}小时前`;
    } else if (diffDay < 7) {
      return `${diffDay}天前`;
    } else {
      return formatDate(dateObj, 'MM-DD HH:mm');
    }
  } else {
    // 绝对时间
    return formatDate(dateObj);
  }
}

/**
 * 格式化持续时间（秒转换为可读格式）
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的持续时间字符串
 */
export function formatDuration(seconds) {
  if (!seconds || seconds < 0) return '0秒';

  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  const parts = [];
  if (days > 0) parts.push(`${days}天`);
  if (hours > 0) parts.push(`${hours}小时`);
  if (minutes > 0) parts.push(`${minutes}分钟`);
  if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`);

  return parts.join('');
}
