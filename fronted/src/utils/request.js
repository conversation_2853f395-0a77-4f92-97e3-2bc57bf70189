import axios from 'axios'
import { message } from 'ant-design-vue'

// 导入服务器配置
import { getApiBaseUrl } from './serverConfig';

// 创建axios实例
const service = axios.create({
  // 初始baseURL设置为空，将在请求拦截器中动态设置
  baseURL: '',
  timeout: 65000, // 请求超时时间，设置为65秒以确保服务器端有足够时间处理（服务器端超时60秒）
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    // 动态设置baseURL
    config.baseURL = getApiBaseUrl();
    // 添加token认证
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['X-Token'] = token
    }
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 🔄 处理JWT自动刷新
    const newToken = response.headers['new-token']
    const newExpiresAt = response.headers['new-expires-at']

    if (newToken) {
      console.log('🔄 检测到新token，自动更新')
      localStorage.setItem('token', newToken)
      if (newExpiresAt) {
        console.log('🕒 新token过期时间:', new Date(parseInt(newExpiresAt) * 1000).toLocaleString())
      }
    }

    const res = response.data
    // 如果返回的状态码不是200，说明接口请求有误
    if (res.code !== 200) {
      console.error('业务错误 - code:', res.code, 'msg:', res.msg);
      message.error(res.msg || '请求失败')

      // 401: 未登录或token过期
      if (res.code === 401) {
        // 清除token并跳转登录页
        localStorage.removeItem('token')
        setTimeout(() => {
          location.reload()
        }, 1500)
      }
      return Promise.reject(new Error(res.msg || '请求失败'))
    } else {
      return res
    }
  },
  error => {
    console.error('响应错误:', error)
    console.error('错误详情:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      config: error.config
    });
    
    // 处理网络错误
    let errorMsg = '网络异常，请稍后重试'
    if (error.response) {
      console.log('HTTP响应状态码:', error.response.status);
      console.log('HTTP响应数据:', error.response.data);
      
      switch (error.response.status) {
        case 401:
          errorMsg = '未授权，请重新登录: ' + (error.response.data?.msg || error.response.statusText)
          // 清除token并跳转登录页
          localStorage.removeItem('token')
          setTimeout(() => {
            location.reload()
          }, 1500)
          break
        case 403:
          errorMsg = '拒绝访问: ' + (error.response.data?.msg || error.response.statusText)
          break
        case 404:
          errorMsg = '请求的资源不存在: ' + (error.response.data?.msg || error.response.statusText)
          break
        case 500:
          errorMsg = '服务器内部错误: ' + (error.response.data?.msg || error.response.statusText)
          break
        default:
          errorMsg = `请求失败: ${error.response.status} - ${error.response.statusText}`
      }
    } else if (error.request) {
      console.error('请求发送失败:', error.request);
      errorMsg = '网络连接失败，请检查网络';
    } else {
      console.error('请求配置错误:', error.message);
      errorMsg = '请求配置错误: ' + error.message;
    }
    
    message.error(errorMsg)
    return Promise.reject(error)
  }
)

// 封装GET请求
export function get(url, params) {
  return service({
    url,
    method: 'get',
    params
  })
}

// 封装POST请求
export function post(url, data, config = {}) {
  return service({
    url,
    method: 'post',
    data,
    ...config
  })
}

// 封装PUT请求
export function put(url, data) {
  return service({
    url,
    method: 'put',
    data
  })
}

// 封装DELETE请求
export function del(url, params) {
  return service({
    url,
    method: 'delete',
    params
  })
}

export default service