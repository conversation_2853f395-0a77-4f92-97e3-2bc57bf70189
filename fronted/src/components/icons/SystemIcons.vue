<template>
  <div class="system-icon" :class="iconClass">
    <!-- Windows 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-if="type === 'windows'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont Windows图标的SVG路径 -->
      <path d="M456 484V160.1l-335.9 72V484H456zM512 484h391.8V64.2l-391.8 84V484zM456 540H120.2v251.9l335.9 72V540zM512 540v335.9l391.8 84V540H512z" fill="#00adef" p-id="9641"></path>
    </svg>

    <!-- Ubuntu 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'ubuntu'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont Ubuntu图标的SVG路径 -->
      <path d="M251.716267 512c0 36.932267-29.5936 66.833067-66.116267 66.833067S119.466667 548.9152 119.466667 512c0-36.9152 29.610667-66.833067 66.133333-66.833067 36.5056 0 66.116267 29.917867 66.116267 66.833067zM627.882667 836.983467a65.7408 65.7408 0 0 0 90.333866 24.439466c31.573333-18.432 42.4448-59.306667 24.1664-91.2896a65.706667 65.706667 0 0 0-90.282666-24.439466c-31.624533 18.466133-42.461867 59.323733-24.2176 91.2896z m114.517333-583.133867c18.261333-31.9488 7.441067-72.823467-24.183467-91.2896a65.757867 65.757867 0 0 0-90.333866 24.456533c-18.261333 31.965867-7.406933 72.8576 24.200533 91.306667a65.6896 65.6896 0 0 0 90.299733-24.4736z m-223.7952 67.549867c98.542933 0 179.370667 76.356267 187.835733 173.704533L802.133333 493.602133a287.607467 287.607467 0 0 0-83.4048-185.429333c-25.361067 9.898667-54.7328 8.533333-80.0768-6.263467a93.917867 93.917867 0 0 1-45.431466-67.037866 281.770667 281.770667 0 0 0-74.581334-10.001067c-45.226667 0-88.0128 10.683733-126.0032 29.730133l46.557867 84.514134a186.026667 186.026667 0 0 1 79.428267-17.7152zM330.069333 512c0-64.494933 31.6928-121.480533 80.1792-155.989333l-49.134933-82.9952a287.505067 287.505067 0 0 0-117.1968 165.717333A94.242133 94.242133 0 0 1 278.6304 512a94.242133 94.242133 0 0 1-34.7136 73.2672 287.573333 287.573333 0 0 0 117.1968 165.717333l49.134933-82.9952A190.976 190.976 0 0 1 330.069333 512zM518.621867 702.634667a186.368 186.368 0 0 1-79.428267-17.698134l-46.557867 84.514134a280.405333 280.405333 0 0 0 125.986134 29.713066 281.258667 281.258667 0 0 0 74.5984-10.001066 93.9008 93.9008 0 0 1 45.431466-67.037867 91.989333 91.989333 0 0 1 80.0768-6.263467A287.744 287.744 0 0 0 802.133333 530.414933l-95.675733-1.536c-8.448 97.3824-89.2928 173.738667-187.835733 173.738667z" fill="#FFFFFF" p-id="10642"></path>
      <text x="512" y="512" text-anchor="middle" dominant-baseline="middle" fill="#E95420" font-size="150">Ubuntu</text>
    </svg>

    <!-- Debian 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'debian'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont Debian图标的SVG路径 -->
      <path d="M592.224 541.024c-17.056 0 3.424 8.544 25.6 11.936 5.984-4.256 11.52-9.376 16.64-14.08a125.44 125.44 0 0 1-42.848 2.048l0.608 0.064z m91.296-22.624c9.824-14.08 17.056-29.44 20.064-45.216-2.56 11.52-8.544 21.344-14.08 31.136-32 20.064-2.976-11.52 0-23.904-34.144 43.104-4.704 25.6-5.984 37.984z m33.28-87.456c2.144-30.72-5.984-21.344-8.544-9.376 2.976 1.696 5.536 21.344 8.544 9.376zM528.224 13.216c8.544 1.696 19.2 2.976 17.92 5.12 9.824-2.144 11.936-4.256-18.336-5.12z m17.92 5.12l-6.4 1.28 5.984-0.416V18.336z m282.432 424.128c0.864 27.296-8.544 40.544-16.224 64l-14.944 7.68c-11.936 23.04 1.28 14.944-7.264 33.28-18.784 16.64-57.184 52.064-69.12 55.456-8.544 0 5.984-10.656 8.096-14.496-25.184 17.056-20.48 25.6-58.464 36.256l-1.28-2.56c-94.72 44.384-226.144-43.52-224-163.84-1.28 7.264-2.976 5.536-5.12 8.544a151.424 151.424 0 0 1 84.448-148.928l0.896-0.384a141.472 141.472 0 0 1 64.416-15.296c36.384 0 69.6 13.568 94.88 35.904l-0.16-0.128a142.432 142.432 0 0 0-112.864-55.488l-3.36 0.032h0.16c-50.336 0.416-97.28 32.416-113.056 66.976-25.6 16.224-28.576 62.72-39.68 70.816-15.36 110.944 28.16 158.72 101.536 215.04 11.52 8.096 3.424 8.96 5.12 14.944a202.368 202.368 0 0 1-65.088-49.248l-0.192-0.224c9.824 14.08 20.064 28.16 34.144 38.816-23.456-7.68-54.176-55.456-63.136-57.6 39.68 70.816 161.28 124.576 224.416 98.144a263.808 263.808 0 0 1-101.28-12.48l1.856 0.544c-14.08-6.816-32.864-21.76-29.856-24.32a243.616 243.616 0 0 0 95.936 19.36 246.528 246.528 0 0 0 156.256-55.552l-0.448 0.352c18.784-14.944 39.68-40.096 45.664-40.544-8.544 13.664 1.696 6.816-5.12 18.784 18.784-30.72-8.544-12.8 19.616-52.896l10.24 14.08c-3.84-25.6 31.584-56.32 28.16-96.416 8.096-12.8 8.544 12.8 0 41.376 12.384-31.584 3.424-36.256 6.4-62.304 3.424 8.544 7.68 17.92 9.824 26.88-7.68-29.856 8.544-51.2 11.936-68.256-3.84-2.144-11.936 12.8-13.664-22.624 0-15.776 4.256-8.544 5.984-11.936-3.424-2.144-11.104-13.664-16.224-36.704 3.424-5.536 9.376 14.08 14.496 14.496-3.424-17.92-8.544-32-8.544-46.08-14.496-29.024-5.12 4.256-17.056-12.8-14.496-46.496 12.8-10.656 14.496-31.584 23.04 32.864 35.84 83.616 41.824 104.96-4.256-25.6-11.936-51.2-20.896-75.104 6.816 2.976-11.104-52.896 8.96-15.776a335.68 335.68 0 0 0-154.464-186.432l-1.696-0.864c7.68 7.264 17.92 16.64 14.08 17.92-32-19.2-26.464-20.48-31.136-28.576-26.016-10.656-27.744 0.864-45.216 0-49.504-26.464-58.88-23.456-104.096-40.544l2.144 9.824c-32.864-10.656-38.4 4.256-73.824 0-2.144-1.696 11.52-5.984 22.624-7.68-31.584 4.256-29.856-5.984-61.024 1.28 7.264-5.536 15.36-8.96 23.456-13.664-25.6 1.696-61.44 14.944-50.336 2.976-42.24 19.2-116.896 45.664-158.72 84.896l-1.28-9.376c-19.2 23.04-83.616 68.704-88.736 98.56l-5.536 1.28c-9.824 17.056-16.224 36.256-24.32 53.76-12.8 22.176-19.2 8.544-17.056 11.936-25.6 52.064-38.4 96-49.504 132.256 7.68 11.52 0 70.4 2.976 117.76-12.8 232.96 163.84 459.52 356.704 512 28.576 9.824 70.4 9.824 106.24 10.656-42.24-11.936-47.776-6.4-88.736-20.896-29.856-13.664-36.256-29.856-57.184-48.224l8.544 14.944c-41.376-14.496-24.32-17.92-58.016-28.576l8.96-11.52c-13.216-1.28-35.424-22.624-41.376-34.56l-14.496 0.416c-17.504-21.344-26.88-37.12-26.016-49.504l-4.704 8.544c-5.536-8.96-64.864-81.056-34.144-64.416-5.536-5.12-13.216-8.544-21.344-23.456l5.984-7.264c-14.944-18.784-27.296-43.52-26.464-51.2 8.544 10.24 13.664 12.8 19.2 14.08-37.536-92.576-39.68-5.12-68.256-93.856l6.4-0.864c-4.256-6.816-7.68-14.496-11.104-21.76l2.56-25.6c-26.88-31.584-7.68-132.256-3.84-187.744 2.976-23.04 22.624-46.944 37.536-84.48l-8.96-1.696c17.056-30.304 99.84-122.464 138.24-117.76 18.336-23.456-3.84 0-7.68-5.984 40.96-42.24 53.76-29.856 81.056-37.536 29.856-17.056-25.6 6.816-11.52-6.4 51.2-12.8 36.256-29.856 103.264-36.256 6.816 4.256-16.64 5.984-22.176 11.104 42.656-20.896 134.4-15.776 194.56 11.52 69.536 32.864 147.616 128.416 150.624 218.88l3.424 0.864c-1.696 36.256 5.536 77.664-7.264 115.616l8.544-17.92zM407.04 564.48l-2.144 11.936c11.104 14.944 20.064 31.136 34.144 43.104-10.24-20.064-17.92-28.16-32-55.456z m26.464-1.28c-5.984-6.4-9.376-14.496-13.216-22.176 3.424 13.664 11.104 25.6 18.336 37.536l-5.12-15.36z m466.752-101.536l-2.976 6.4a299.008 299.008 0 0 1-29.44 94.304 280.832 280.832 0 0 0 32-100.704zM531.2 5.12c11.52-4.256 28.16-2.144 40.544-5.12-15.776 1.28-31.584 2.144-46.944 4.256l6.4 0.864zM128.416 219.296c2.976 24.32-18.336 34.144 4.704 17.92 12.8-28.16-4.704-7.68-4.256-17.92z m-26.88 113.504c5.12-16.64 6.4-26.464 8.544-35.84-14.944 18.784-7.264 22.624-8.544 35.424z" fill="#A81D33" p-id="14734"></path>
    </svg>

    <!-- CentOS 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'centos'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont CentOS图标的SVG路径 -->
      <path d="M149.777067 317.678933L185.856 281.6l23.210667-23.176533 23.176533 23.176533 166.2976 166.2976h46.3872v-46.353067L278.6304 235.246933l-23.210667-23.2448 23.210667-23.176533 36.0448-36.0448H149.777067z" fill="#A4CB3E" p-id="15913"></path><path d="M444.928 355.157333v-202.410666h-83.831467L301.806933 212.036267l143.121067 143.121066z m-295.150933 8.942934v83.831466h202.410666L209.066667 304.810667l-59.2896 59.2896z" fill="#A6CD3C" p-id="15914"></path><path d="M765.610667 212.036267l-23.210667 23.210666-166.331733 166.2976v46.353067h46.3872l166.331733-166.2976 23.210667-23.176533 23.176533 23.176533 36.0448 36.078933V152.7808h-164.898133l36.078933 36.0448z" fill="#92307F" p-id="15915"></path><path d="M393.9328 119.978667h83.797333v267.9808l32.768 32.802133 32.802134-32.802133V119.978667h83.831466L510.498133 3.345067z" fill="#F6AB46" p-id="15916"></path><path d="M668.842667 447.8976h202.376533v-83.797333L811.9296 304.810667z" fill="#A3248D" p-id="15917"></path><path d="M116.974933 546.304h267.9808l32.802134-32.802133-32.802134-32.802134H116.974933v-83.831466L0.341333 513.501867l116.6336 116.6336z" fill="#A6218E" p-id="15918"></path><path d="M576.068267 671.8464v202.410667h83.8656l59.255466-59.2896-143.121066-143.121067z m92.7744-92.7744l143.086933 143.121067 59.2896-59.2896v-83.831467h-202.376533z" fill="#FFA64C" p-id="15919"></path><path d="M576.068267 355.157333l143.121066-143.121066-59.255466-59.255467h-83.8656z" fill="#A3248D" p-id="15920"></path><path d="M871.185067 709.3248l-36.0448 36.078933-23.210667 23.176534-23.176533-23.176534-166.2976-166.331733h-46.3872v46.3872l166.331733 166.331733 23.210667 23.176534-23.210667 23.210666-36.078933 36.0448h164.864z" fill="#FFA648" p-id="15921"></path><path d="M904.021333 480.699733h-267.9808l-32.768 32.802134 32.768 32.802133h267.9808v83.831467l116.6336-116.6336-116.6336-116.6336z" fill="#2B30A5" p-id="15922"></path><path d="M209.066667 722.193067l143.086933-143.121067H149.777067v83.831467z" fill="#2F3597" p-id="15923"></path><path d="M255.419733 814.933333l23.176534-23.210666 166.2976-166.263467v-46.3872h-46.353067l-166.331733 166.331733-23.176534 23.176534-23.176533-23.176534-36.078933-36.078933v164.898133H314.709333l-36.078933-36.078933-23.210667-23.210667z m371.712 92.091734h-83.831466v-268.014934l-32.802134-32.802133-32.768 32.802133v268.014934H393.898667l116.6336 116.599466 116.599466-116.599466z" fill="#2F3291" p-id="15924"></path><path d="M444.928 671.8464l-143.121067 143.086933 59.2896 59.2896h83.831467z" fill="#2F3291" p-id="15925"></path>
    </svg>

    <!-- Red Hat 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'redhat'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont Red Hat图标的SVG路径 -->
      <path d="M902.899913 590.188442c88.958652 0 217.70416-18.399769 217.70416-124.187864 0.042298-0.888265 0.042298-1.945723 0.042298-3.040191a101.49746 101.49746 0 0 0-2.411004-21.952828l0.132182 0.676773-53.00508-230.102854c-12.181916-50.6258-22.88339-73.599075-111.794457-118.054608C884.584741 58.287083 734.298814 0 689.843281 0c-41.325457 0-53.422777 53.33818-102.742616 53.33818-47.501012 0-82.735512-39.802718-127.191045-39.802718-42.679004 0-70.511297 29.058945-91.914246 88.868768 0 0-59.767524 168.643397-67.508117 193.091825a44.916849 44.916849 0 0 0-1.607336 12.055021c0 0.634475 0 1.26895 0.042298 1.861126v-0.084597c0 65.520096 258.151927 280.52245 604.019993 280.522451z m231.287207-81.212772c12.308811 58.202487 12.308811 64.293445 12.308811 72.039324 0 99.533231-111.879053 154.811847-259.034904 154.811847-332.464786 0.211492-623.76802-194.614565-623.76802-323.370647v-0.25379a130.77054 130.77054 0 0 1 11.039861-52.788302l-0.338387 0.845967C154.896443 366.218844 0.001322 387.621794 0.001322 524.241398c0 223.842703 530.420918 499.754637 950.358627 499.754637 322.017101 0 403.187575-145.633112 403.187575-260.557644 0-90.518402-78.209591-193.176422-219.232187-254.466686z" fill="#EE0000" p-id="16940"></path>
    </svg>

    <!-- Fedora 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'fedora'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont Fedora图标的SVG路径 -->
     <path d="M512.042667 0C229.376 0 0.341333 229.077333 0.170667 511.658667H0.085333v396.245333h0.085334A116.309333 116.309333 0 0 0 116.48 1024h395.733333c282.709333-0.170667 511.701333-229.205333 511.701334-511.872C1023.914667 229.333333 794.794667 0 512 0z m103.722666 210.773333c85.973333 0 167.125333 65.834667 167.125334 156.629334 0 8.405333 0.042667 16.853333-1.28 26.410666a42.752 42.752 0 0 1-48.512 38.101334 42.752 42.752 0 0 1-35.925334-50.133334 111.36 111.36 0 0 0 0.554667-14.378666c0-51.498667-42.112-71.338667-81.92-71.338667-39.850667 0-75.733333 33.450667-75.818667 71.338667 0.682667 43.818667 0 87.296 0 130.986666l73.898667-0.512c57.685333-1.194667 58.368 85.717333 0.682667 85.248l-74.581334 0.554667c-0.170667 35.242667 0.256 28.885333 0.085334 46.634667 0 0 0.64 43.093333-0.682667 75.776-8.917333 96-90.624 172.629333-188.757333 172.629333-104.021333 0-189.781333-85.034667-189.781334-189.312 3.114667-107.306667 88.661333-191.658667 196.394667-190.677333l60.117333-0.426667v85.162667l-60.117333 0.554666h-0.298667c-59.221333 1.706667-109.952 41.984-110.933333 105.386667a104.021333 104.021333 0 0 0 104.618667 104.064c57.856 0 104.149333-42.112 104.149333-103.978667l-0.042667-322.432c0-5.973333 0.213333-10.752 0.853334-17.365333 9.813333-78.848 80.341333-138.922667 160.170666-138.922667z" fill="#51A2DA" p-id="22521"></path>
    </svg>

    <!-- SUSE 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'suse'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont SUSE图标的SVG路径 -->
      <path d="M189.701368 649.702877a221.17327 221.17327 0 0 1-188.319675-234.273769c9.211288-114.834062 92.112884-206.742251 179.108386-271.22127A730.250477 730.250477 0 0 1 653.643261 1.791084a1099.3161 1099.3161 0 0 1 298.599267 50.508565c-9.211288-82.696901 101.068304-9.211288 147.022398-13.765759 59.719853 18.371403 142.416754 32.137162 160.788157 101.068303 27.582691 46.056442 41.34845 110.228418-32.137162 119.439707-87.302545 32.137162-169.948271-32.137162-243.485057-68.931142 9.211288 27.582691 13.765759 55.114209 46.056442 59.719854 68.931142 36.742806 156.182513 64.325498 229.668124 22.977047 55.114209-18.371403-13.765759 68.931142-46.056442 59.719853a574.630876 574.630876 0 0 1-280.227863 18.371403c-78.091256 0-59.719853 105.673948-27.582692 151.576869 18.371403 27.582691 32.137162 55.114209-9.211288 27.582691-68.931142-18.371403-110.228418-78.091256-160.788157-124.045351-55.114209-46.056442-151.576868-55.114209-192.925319 4.605644a244.969098 244.969098 0 0 0-9.211288 114.834063 1206.576436 1206.576436 0 0 1-78.091256-41.34845 222.043225 222.043225 0 0 0-238.879413-188.319675A136.736459 136.736459 0 0 0 97.742005 479.54991a138.169326 138.169326 0 0 0 188.319674 78.091257 81.571076 81.571076 0 0 0-32.137161-151.576869c-68.931142-18.371403-68.931142 92.112884 0 59.719853 73.485612 36.742806-46.056442 92.112884-78.091257 46.056443a89.451845 89.451845 0 0 1 18.371403-156.182513c96.462659-46.056442 206.742251 50.508565 197.530963 151.576868a168.873621 168.873621 0 0 1-202.136607 142.416754z m1001.318225-418.038973c73.485612-27.582691 46.056442-151.576868-32.137161-142.416754a75.123174 75.123174 0 0 0-41.348451 137.81111 61.869154 61.869154 0 0 0 73.485612 4.605644z m-50.508564-18.371403c-82.696901-27.582691-4.605644-147.022398 55.114209-87.302545 36.896328 41.34845-4.605644 110.279592-55.011862 87.302545z m41.34845-50.508565c22.977047-55.114209-87.302545 0-13.765759 4.605644h9.211288zM391.735627 1012.627641c-73.485612-9.211288-73.485612-96.462659-68.931141-151.576868a169.283012 169.283012 0 0 1 4.605644-68.931142c55.114209-50.508565 87.302545 32.137162 73.485612 82.6969 4.605644 18.371403-4.605644 46.056442 4.605644 64.325498 13.765759 22.977047 46.056442 9.211288 68.931142 13.765759 22.977047 0 50.508565 9.211288 64.325497-13.765759a766.123328 766.123328 0 0 1 9.211289-147.022398c78.091256-46.056442 78.091256 68.931142 68.931141 114.834062a373.568919 373.568919 0 0 1-4.605644 101.068304c-32.137162 41.34845-59.719853-13.765759-92.112884-9.211289a238.316501 238.316501 0 0 1-128.343952 13.765759z m-376.690523-4.605644c-9.211288-13.765759-22.977047-68.931142 13.765759-59.719854h156.182513c55.114209-64.325498-46.056442-73.485612-92.112884-64.325497A85.869678 85.869678 0 0 1 5.577947 769.142584c9.211288-59.719853 78.091256-73.485612 128.650995-68.879968 41.34845 4.605644 133.051944-22.977047 114.834062 50.508565 0 22.977047-36.742806 4.605644-55.114209 9.211288-41.34845 4.605644-128.650995-18.371403-114.834062 50.508565 50.508565 32.137162 133.051944-9.211288 179.108386 41.34845a112.019502 112.019502 0 0 1-36.742806 156.182513c-27.582691 13.765759-59.719853 4.605644-92.112884 9.211288H19.753096a16.017407 16.017407 0 0 0-4.605644-9.211288z m666.078501 0c-9.211288-13.765759-22.977047-68.931142 13.765759-59.719854h156.336034c55.114209-59.719853-36.742806-78.091256-82.6969-64.325497a85.409113 85.409113 0 0 1-96.46266-110.228418c9.211288-68.931142 87.302545-78.091256 142.416754-73.485612a301.311479 301.311479 0 0 1 92.112884 4.605644c18.371403 9.211288 27.582691 68.931142 0 59.719853a787.002248 787.002248 0 0 0-147.022398 4.605644c-50.508565 78.091256 78.091256 50.508565 119.439707 59.719854 68.931142 4.605644 73.485612 92.112884 55.114209 142.416754a86.637285 86.637285 0 0 1-87.302545 46.056442h-151.781563a56.854119 56.854119 0 0 1-13.765759-9.36481z m459.387424-124.045351c46.056442-4.605644 96.462659 13.765759 133.051944-13.765759 18.371403-55.114209 18.371403-142.416754-46.056443-165.393801a651.135744 651.135744 0 0 0-188.319674 4.605644c-59.719853 27.582691-46.056442 105.673948-46.056442 156.182513s-4.605644 119.439707 55.114209 142.416754a887.91703 887.91703 0 0 0 156.182513 4.605644c36.742806 4.605644 32.137162-41.34845 32.137161-64.325498H1136.058906c-50.508565 13.765759-73.485612-18.371403-64.325498-64.325497h68.931142zM1071.579887 773.748228c36.742806-18.371403 137.81111-32.137162 137.81111 32.137162v18.371403h-110.074897c-36.79398 9.211288-36.79398-22.977047-27.582692-50.508565z" fill="#81C242" p-id="20697"></path>
    </svg>

    <!-- Arch Linux 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'arch'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont Arch Linux图标的SVG路径 -->
      <path d="M511.904 0c-45.588 111.744-73.08 184.84-123.836 293.26 31.12 32.98 69.316 71.384 131.348 114.76-66.692-27.436-112.18-54.984-146.176-83.568C308.28 459.968 206.52 653 0 1024c162.316-93.688 288.14-151.452 405.4-173.492a297.056 297.056 0 0 1-7.704-69.512l0.196-5.2c2.576-103.968 56.672-183.92 120.752-178.496 64.08 5.428 113.892 94.168 111.32 198.14-0.488 19.56-2.696 38.384-6.552 55.84C739.404 873.96 863.88 931.576 1024 1024c-31.572-58.116-59.752-110.504-86.664-160.4-42.392-32.848-86.608-75.6-176.8-121.88 61.992 16.1 106.38 34.68 140.976 55.452C627.892 287.832 605.736 220.152 511.904 0z" fill="#1793D1" p-id="19106"></path>
    </svg>

    <svg  v-else-if="type === 'kali'" viewBox="0 0 1024 1024" class="icon-svg">
    <path d="M545.194667 253.568s-84.053333-5.546667-227.285334 39.253333c-145.92 45.653333-228.693333 110.378667-228.693333 110.378667s217.514667-121.472 463.018667-128.341333z m313.642666 132.053333l10.965334-0.725333s-62.634667-75.946667-182.528-112.981333c67.413333 27.392 126.037333 63.701333 171.562666 113.706666z m17.92 31.573334c1.664-2.901333 7.082667 9.258667 11.221334 14.378666 0.170667 1.024 0.426667 1.664-1.92 1.152-0.213333-1.066667-0.554667-1.365333-0.554667-1.365333s-5.76-3.413333-7.552-5.845333c-1.749333-2.432-2.090667-6.698667-1.194667-8.32z m147.114667 361.770666s13.312-152.661333-226.56-187.861333a779.818667 779.818667 0 0 0-107.690667-7.978667c-192.256 2.56-199.253333-221.738667-54.4-233.045333 60.032-4.949333 131.712 27.434667 201.813334 60.074667-0.298667 8.704 0.085333 16.426667 5.802666 23.552 5.717333 7.168 27.648 14.933333 34.688 18.986666 6.997333 4.010667 29.482667 18.346667 43.264 36.266667 2.986667-5.589333 27.904-21.845333 27.904-21.845333s-5.973333 0.128-19.84-5.077334c-13.909333-5.205333-30.421333-20.906667-30.805333-21.802666-0.426667-0.938667-0.64-2.346667 2.56-2.986667 2.517333-2.090667-3.072-8.832-5.546667-11.306667-2.474667-2.474667-18.986667-30.549333-19.370666-31.146666-0.384-0.682667-0.512-1.322667-1.706667-2.133334-3.626667-1.152-19.626667 1.706667-19.626667 1.706667s-24.533333-12.074667-33.024-38.101333c0.128 4.565333-4.224 9.557333 0 20.010666-12.8-5.418667-23.808-14.677333-32.512-37.546666-5.12 13.013333 0 21.290667 0 21.290666s-30.165333-8.448-34.986666-36.266666c-5.290667 12.501333 0 20.010667 0 20.010666s-49.194667-25.685333-130.944-26.026666c-54.741333-5.034667-66.133333-101.290667-61.013334-117.504 0 0-78.933333-41.6-234.368-59.989334-155.392-18.346667-282.794667-2.773333-282.794666-2.773333s275.2-13.226667 495.658666 76.074667c7.509333 33.493333 30.037333 89.344 42.197334 116.181333-34.773333 24.021333-73.941333 46.592-80.042667 126.72-6.101333 80.128 62.805333 150.613333 148.224 152.746667 81.066667 4.352 137.130667 4.949333 205.056 40.192 64.853333 35.84 118.016 145.066667 123.306667 243.328 5.632-72.917333-21.717333-229.674667-149.333334-277.248 178.389333 31.232 194.090667 163.498667 194.090667 163.498666zM541.013333 241.621333l-6.4-20.693333s-105.984-18.816-248.405333-8.704C143.786667 222.336 0 272.213333 0 272.213333s294.229333-74.026667 541.013333-30.592z" fill="#557C94" p-id="13721"></path>
    </svg>

    <!-- 通用Linux 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'linux'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont Linux图标的SVG路径 -->
      <path d="M525.2 198.3c-8.6 5.6-15.2 13.8-18.9 23.4-3.8 12.4-3.2 25.6 1.5 37.7 3.9 12.7 11.7 23.8 22.2 31.8 5.4 3.8 11.6 6.2 18.2 7 6.6 0.8 13.2-0.3 19.1-3.3 7-3.9 12.6-10 15.9-17.3 3.2-7.4 5-15.3 5.2-23.3 0.7-10.2-0.6-20.4-3.8-30.1-3.5-10.6-10.3-19.7-19.5-25.9-4.7-3-9.9-5-15.4-5.8-5.5-0.8-11.1-0.2-16.3 1.8-2.9 1.2-5.7 2.7-8.3 4.5" fill="#FFFFFF" p-id="17957"></path><path d="M810.2 606.5c-5.1-28.3-13.1-56-23.8-82.6-7.3-19.8-17.2-38.6-29.5-55.8-12.4-16.5-28.1-30.4-40.2-47.1-6.4-8.7-11.8-18.4-18.5-26.9-2.7-5.6-5.3-11.2-7.9-16.8-8-17.5-15.3-35.4-24.8-52-1.5-2.6-3.1-5.2-4.6-7.7-1.2-16-2.9-32-3.8-48 0.7-32.1-2-64.3-8.1-95.9-4.2-15.1-10.6-29.6-19-42.8-9.8-15.6-22.4-29.2-37.2-40.1-24.1-17.1-52.9-26.3-82.4-26.4-21.7-0.5-43.2 4.4-62.5 14.4-20.3 11.1-36.7 28.2-47 48.9-9.6 20.9-14.7 43.5-15 66.5-0.8 22.6 1.3 45 2.2 67.6 0.9 23.4 0.4 46.9 2.3 70.3 0.6 7.5 1.5 15 1.5 22.6 0 3.8-0.2 7.6-0.3 11.3l-0.3 0.8c-10.2 17.3-21.5 34-33.8 49.9-8.6 10.9-17.2 21.7-25.9 32.4-11.3 12.7-20.9 26.8-28.5 42-5.1 13.2-9.2 26.8-12.4 40.6l-0.3 1.1c-4.8 15.9-10.8 31.3-18 46.2-0.7 1.4-1.4 2.9-2 4.2-4.3 8.9-8.8 17.8-13.5 26.5l-5.4 10.1c-3.4 6.1-6.4 12.4-9 18.8-1.5 3.9-2.7 7.9-3.4 12-1.3 8.7-0.7 17.5 1.6 25.9 0.5 2.1 1.2 4.2 1.9 6.3 2.2 6.2 4.8 12.3 7.9 18.1 1.4 2.7 2.9 5.3 4.3 8l1.3 1.9c1.4 2.5 2.9 5 4.4 7.4l0.2 0.3c1.7 2.8 3.6 5.5 5.4 8.2l0.3 0.4c1.9 2.6 3.8 5.3 5.8 7.9 7.4 28.9 21 55.8 39.7 79-2.9 5.1-5.5 10.1-8.4 15.1-10.2 14.8-18.6 30.7-25.1 47.4-2.7 8.6-3.4 17.7-1.9 26.6 1.4 9 6 17.1 13 23 4.7 3.6 10.1 6.1 15.8 7.3 5.7 1.2 11.6 1.8 17.5 1.5 22.2-1.7 44.2-6.1 65.4-12.9 12.8-3.4 25.6-6.4 38.6-9 13.5-3.1 27.2-5 41-5.6 3.4 0.1 6.8-0.1 10.1-0.3 9.4 1 18.8 1.4 28.3 1l3.5-0.2c2.4 0.3 4.9 0.4 7.4 0.6 16.6 0.9 33.1 2.6 49.5 5.1 14.4 2.2 28.8 5 43 8.5 21.9 6.6 44.4 11 67.3 12.9 6 0.3 12-0.2 18-1.4 5.9-1.2 11.5-3.8 16.3-7.4 7-5.8 11.6-13.9 13.1-22.9 1.5-8.9 0.8-18-1.9-26.6-6.6-16.7-15.1-32.6-25.5-47.3-3.6-6.1-7-12.4-10.6-18.5 15.5-17.3 29.2-36.3 40.7-56.5 7 0.4 13.9-0.4 20.6-2.6 17.5-5.9 32.7-17.3 43.3-32.5 3.2-4.5 5.7-9.5 7.2-14.8 6.9-10.7 11.6-22.7 13.8-35.3 3.2-20.8 2.7-42.1-1.5-62.7h-0.2z m0 0" fill="#020204" p-id="17958"></path><path d="M425.6 323.2c-3.1 4-5.3 8.7-6.4 13.6-1.1 4.9-1.8 10-1.9 15 0.3 10.1-0.5 20.2-2.5 30.1-3.5 10.3-8.8 19.8-15.6 28.3-11.7 14.7-20.9 31.2-27.2 48.8-3.2 10.9-4.3 22.3-3.1 33.7-12.1 17.9-22.6 36.9-31.3 56.7-13.4 29.9-22 61.8-25.5 94.4-4.3 40.1 1.6 80.6 17 117.8 11.3 26.8 28.5 50.8 50.3 70.1 11.2 9.7 23.5 17.9 36.7 24.4 46.7 22.8 101.4 22.3 147.6-1.4 23.1-13.5 44.2-30.2 62.6-49.5 11.9-10.8 22.5-22.9 31.8-36.1 15.5-26.9 24.6-57.1 26.5-88.1 9.6-53.6 3.7-108.8-16.9-159.2-8.1-16.8-18.8-32.2-31.8-45.6a252.5 252.5 0 0 0-20.2-68c-7.2-15.5-15.9-30.3-22.6-46.2-2.7-6.5-5.1-13.1-8.1-19.4-2.9-6.4-6.9-12.3-11.8-17.3-5.3-4.9-11.6-8.6-18.5-10.7-6.9-2.2-14-3.4-21.2-3.6-14.4-0.7-28.9 1.1-43.1 0.6-11.5-0.5-22.8-2.5-34.3-1.8-5.7 0.3-11.4 1.4-16.7 3.5-5.4 2.1-10.1 5.5-13.8 10m4.6-125.1c-5.4 0.4-10.5 2.7-14.4 6.4-3.9 3.7-6.8 8.4-8.4 13.5-2.7 10.4-3.4 21.3-1.9 32 0.2 9.7 1.9 19.4 5.1 28.6 1.8 4.5 4.4 8.7 7.8 12.2 3.4 3.5 7.7 6.1 12.4 7.3 4.5 1.1 9.2 0.9 13.5-0.5 4.3-1.4 8.3-3.8 11.5-7 4.7-4.8 8.1-10.7 9.8-17.1 1.7-6.4 2.5-13.1 2.3-19.8 0-8.3-1.3-16.6-3.8-24.6s-6.8-15.3-12.6-21.4c-2.8-2.9-6-5.4-9.6-7.2-3.7-1.7-7.7-2.6-11.7-2.4m95 0c-8.6 5.6-15.2 13.8-18.9 23.4-3.8 12.4-3.2 25.6 1.5 37.7 3.9 12.7 11.7 23.8 22.2 31.8 5.4 3.8 11.6 6.2 18.2 7 6.6 0.8 13.2-0.3 19.1-3.3 7-3.9 12.6-10 15.9-17.3 3.2-7.4 5-15.3 5.2-23.3 0.7-10.2-0.6-20.4-3.8-30.1-3.5-10.6-10.3-19.7-19.5-25.9-4.7-3-9.9-5-15.4-5.8-5.5-0.8-11.1-0.2-16.3 1.8-2.9 1.2-5.7 2.7-8.3 4.5" fill="#FFFFFF" p-id="17959"></path><path d="M544.5 223.6c-3.2 0.2-6.2 1.2-8.9 2.9s-5 4-6.8 6.6c-3.4 5.3-5.3 11.5-5.4 17.9-0.3 4.7 0.4 9.5 1.9 14s4.3 8.5 7.9 11.5c3.8 3.1 8.4 4.9 13.3 5.2 4.9 0.2 9.7-1.1 13.7-3.9 3.2-2.3 5.8-5.2 7.6-8.7 1.8-3.4 2.9-7.2 3.4-11 1-6.8-0.2-13.8-3.2-19.9-3.1-6.2-8.4-10.9-14.8-13.4-2.8-1.1-5.7-1.5-8.7-1.4" fill="#020204" p-id="17960"></path><path d="M430.2 198.3c-5.4 0.4-10.5 2.7-14.4 6.4-3.9 3.7-6.8 8.4-8.4 13.5-2.7 10.4-3.4 21.3-1.9 32 0.2 9.7 1.9 19.4 5.1 28.6 1.8 4.6 4.4 8.7 7.8 12.2 3.4 3.5 7.7 6.1 12.4 7.3 4.5 1.1 9.2 0.9 13.5-0.5 4.3-1.4 8.3-3.8 11.5-7 4.7-4.8 8.1-10.7 9.8-17.1 1.7-6.4 2.5-13.1 2.3-19.8 0-8.3-1.3-16.6-3.8-24.6s-6.8-15.3-12.6-21.4c-2.8-2.9-6-5.4-9.6-7.2-3.7-1.7-7.7-2.6-11.7-2.4" fill="#FFFFFF" p-id="17961"></path><path d="M417.3 242.8c-1.3 6.7-1 13.7 1.1 20.2 1.6 4.3 4 8.2 7.2 11.5 2 2.2 4.3 4.1 7 5.4 2.7 1.4 5.7 1.8 8.7 1.1 2.7-0.7 5-2.3 6.7-4.5 1.7-2.2 2.9-4.7 3.7-7.3 2.3-7.8 2.1-16.1-0.4-23.9-1.6-5.7-4.7-10.9-9.1-14.8-2.1-1.8-4.7-3.2-7.4-3.9-2.8-0.7-5.7-0.5-8.4 0.7-2.8 1.4-5.1 3.7-6.5 6.5-1.4 2.8-2.3 5.8-2.7 8.9" fill="#020204" p-id="17962"></path><path d="M404.6 326.9c0.2 0.9 0.5 1.8 1 2.5 0.9 1.4 2 2.5 3.4 3.4 1.3 0.9 2.6 1.7 3.9 2.5 6.9 4.7 13 10.5 17.9 17.3 6 9.4 13.5 17.8 22 25 6.5 4.5 14.1 7.2 22 7.9 9.2 0.7 18.5-0.4 27.4-3.2 8.2-2.4 16.1-5.8 23.5-10.3 12.7-10.2 26.3-19.2 40.7-26.7 3.4-1.2 6.8-2.1 10-3.6 3.3-1.4 6.1-3.8 7.8-7 1.1-3.2 1.8-6.6 1.9-10 0.5-3.6 1.7-7.1 2.3-10.7 0.8-3.6 0.5-7.3-0.8-10.8-1.4-2.7-3.6-4.9-6.3-6.3-2.7-1.3-5.7-2.1-8.7-2.2-6.1 0.2-12.1 0.8-18 1.8-8 0.7-16-0.3-24 0-9.9 0.3-19.8 2.5-29.8 2.9-11.4 0.6-22.7-1.2-34.1-1.7-4.9-0.3-9.9-0.1-14.8 0.7-4.9 0.7-9.6 2.5-13.7 5.3-3.8 3-7.3 6.2-10.7 9.6-1.8 1.6-3.8 3-5.9 4.1-2.2 1.1-4.5 1.7-7 1.6-1.2-0.2-2.5-0.2-3.7 0-0.7 0.3-1.4 0.7-1.9 1.2l-1.5 1.8c-1 1.5-1.9 3.1-2.6 4.7" fill="#D99A03" p-id="17963"></path><path d="M429.7 301.7c-4 2.4-7.9 5-11.8 7.7-2.1 1.3-3.8 3-5.1 5.1-0.7 1.6-1 3.3-0.9 5 0.1 1.7 0.1 3.4 0 5.1-0.1 1.1-0.5 2.3-0.5 3.5 0 0.6 0 1.2 0.2 1.7 0.2 0.6 0.4 1.1 0.8 1.5 0.5 0.5 1.2 0.9 2 1.1 0.7 0.2 1.5 0.3 2.3 0.5 3.5 1 6.7 2.9 9.3 5.4 2.7 2.4 5.1 5.2 8 7.5 8 6 17.7 9.1 27.6 9 9.9-0.2 19.7-1.6 29.2-4.1 7.5-1.6 14.9-3.6 22.1-6.1 11.2-4.2 21.5-10.3 30.4-18.2 3.9-3.8 8-7.2 12.4-10.3 4-2.5 8.7-4.2 12.7-6.6 0.4-0.2 0.7-0.5 1.1-0.7 0.3-0.3 0.6-0.6 0.8-1 0.3-0.7 0.3-1.5 0-2.2-0.2-0.7-0.5-1.3-0.9-1.8-0.5-0.6-1.1-1.2-1.7-1.7-4.6-3.4-10.1-5.3-15.8-5.5-5.8-0.4-11.3 0-16.9-1.1-5.2-1.1-10.3-2.6-15.3-4.4-5.3-1.7-10.7-3-16.3-3.9-13-2.1-26.2-1.8-39.1 1-12.1 2.7-23.8 7.3-34.6 13.5" fill="#604405" p-id="17964"></path><path d="M428.4 288.1c-5.8 3.9-11 8.7-15.5 14.1-2.6 3-4.7 6.5-6.1 10.3-0.9 3-1.5 6.1-2 9.2-0.3 1.1-0.5 2.3-0.5 3.5 0 0.6 0.1 1.2 0.3 1.7 0.2 0.6 0.5 1.1 0.9 1.5 0.7 0.7 1.6 1.1 2.6 1.3 0.9 0.2 1.9 0.2 2.9 0.3 4.4 0.7 8.5 2.5 12.1 5.1 3.6 2.5 7 5.4 10.7 7.8 8.4 5 18 7.7 27.8 7.9 9.8 0.2 19.5-0.8 29-2.9 7.6-1.4 15.1-3.5 22.4-6.3 10.9-4.7 21.1-10.8 30.4-18.2 4.3-3.2 8.5-6.6 12.4-10.3 1.3-1.3 2.6-2.6 4-3.8 1.4-1.2 3-2.1 4.7-2.7 2.7-0.7 5.5-0.8 8.3-0.1 2 0.5 4.1 0.7 6.2 0.7 1.1 0 2.1-0.2 3.1-0.5 1-0.4 1.9-1 2.5-1.8 0.9-1.1 1.3-2.4 1.3-3.8s-0.4-2.7-1.1-3.9c-1.5-2.3-3.8-4.1-6.3-5.1-3.5-1.4-7.1-2.5-10.8-3.2-11.3-2.7-22.3-6.7-32.7-11.9-5.2-2.6-10.1-5.4-15.3-8.1-5.2-2.9-10.6-5.4-16.2-7.2-12.9-3.5-26.6-2.9-39.1 1.8-14 4.9-26.5 13.4-36.1 24.7" fill="#F5BD0C" p-id="17965"></path><path d="M493.5 272.2c0.7 2.3 4.3 1.9 6.4 2.9 2.1 1 3.3 2.9 5.3 3.1 2.1 0.2 5-0.7 5.3-2.6 0.4-2.6-3.4-4.2-5.8-5.1-3.2-1.5-6.8-1.6-10-0.2-0.7 0.3-1.4 1.2-1.2 1.9z m-34.4-1.2c-2.7-0.9-7.1 3.8-5.8 6.3 0.4 0.7 1.6 1.5 2.4 1.1 0.8-0.4 2.3-3.1 3.6-4 1-0.8 0.8-3.1-0.2-3.4z m0 0" fill="#CD8907" p-id="17966"></path><path d="M887.7 829.8c-2 5.2-4.9 10-8.5 14.3-8.4 9-18.6 16.2-29.8 21.2-19 8.8-37.5 18.6-55.5 29.3-11.7 7.8-22.6 16.6-32.7 26.4-8.3 8.7-17.2 16.7-26.6 24.2-9.8 7.2-21.1 12.1-33.1 14-14.7 1.9-29.6-0.4-43.1-6.5-9.7-3.7-18.1-10.2-24-18.8-5-9.2-7.3-19.5-6.8-29.9 0.6-18.3 2.8-36.5 6.6-54.5 2.6-15 5.2-30 6.8-45.1 2.8-27.6 3.1-55.3 1-82.9-0.5-4.6-0.5-9.3 0-13.9 0.6-9.4 8.5-16.6 18-16.5 4.3-0.1 8.6 0.3 12.8 1.1 10 1.2 20 2.9 29.8 5.2 6.1 1.6 12.2 3.8 18.3 5.5 10.2 3 21 3.9 31.6 2.9 11.1-2.6 22.4-4.3 33.8-5.3 4.7 0.2 9.4 1 13.8 2.4 4.6 1.3 8.9 3.6 12.4 6.9 2.5 2.7 4.5 5.8 5.8 9.2 1.9 5.1 3.1 10.4 3.5 15.8 0.2 4.8 0.6 9.6 1.2 14.4 1.7 7.7 5.4 14.9 10.6 20.9 5.3 5.8 11 11.2 17.2 16 5.9 5.2 12.1 10 18.6 14.4 3.1 2.1 6.2 4 9.1 6.3 3 2.2 5.5 5 7.4 8.2 2.4 4.4 3.2 9.5 2 14.4" fill="#F5BD0C" p-id="17967"></path><path d="M887.7 829.8c-2 5.2-4.9 10-8.5 14.3-8.4 9-18.6 16.2-29.8 21.2-19 8.8-37.5 18.6-55.5 29.3-11.7 7.8-22.6 16.6-32.7 26.4-8.3 8.7-17.2 16.7-26.6 24.2-9.8 7.2-21.1 12.1-33.1 14-14.7 1.9-29.6-0.4-43.1-6.5-9.7-3.7-18.1-10.2-24-18.8-5-9.2-7.3-19.5-6.8-29.9 0.6-18.3 2.8-36.5 6.6-54.5 2.6-15 5.2-30 6.8-45.1 2.8-27.6 3.1-55.3 1-82.9-0.5-4.6-0.5-9.3 0-13.9 0.6-9.4 8.5-16.6 18-16.5 4.3-0.1 8.6 0.3 12.8 1.1 10 1.2 20 2.9 29.8 5.2 6.1 1.6 12.2 3.8 18.3 5.5 10.2 3 21 3.9 31.6 2.9 11.1-2.6 22.4-4.3 33.8-5.3 4.7 0.2 9.4 1 13.8 2.4 4.6 1.3 8.9 3.6 12.4 6.9 2.5 2.7 4.5 5.8 5.8 9.2 1.9 5.1 3.1 10.4 3.5 15.8 0.2 4.8 0.6 9.6 1.2 14.4 1.7 7.7 5.4 14.9 10.6 20.9 5.3 5.8 11 11.2 17.2 16 5.9 5.2 12.1 10 18.6 14.4 3.1 2.1 6.2 4 9.1 6.3 3 2.2 5.5 5 7.4 8.2 2.4 4.4 3.2 9.5 2 14.4M259.4 676.3c4.9-1.9 10.2-2.4 15.4-1.4 5.2 1 10.1 3.1 14.4 6.1 8.3 6.3 15.5 14.1 21.2 22.8 14.1 19.4 27.6 39.2 39.9 59.8 10 16.7 19.1 33.9 30.6 49.6 7.5 10.2 16 19.7 23.6 29.9 7.9 10 13.9 21.4 17.6 33.5 4.4 16.1 2.6 33.2-4.9 48.1-5.4 10.4-13.5 19.1-23.4 25.1-10 6-21.5 9-33.2 8.7-18.4-2.5-36.2-8.1-52.6-16.6-34.9-13.9-72.8-18.3-108.8-29.1-11.1-3.3-21.9-7.3-33.1-10.3-5-1.2-9.9-2.7-14.7-4.7-4.7-2-8.8-5.4-11.5-9.7-2-3.5-3-7.5-2.9-11.5 0.1-4 0.9-7.9 2.3-11.5 2.7-7.5 7.1-14.2 10-21.6 4.4-12.2 6.1-25.3 5-38.2-0.6-12.9-2.9-25.8-3.6-38.7-0.6-5.8-0.4-11.6 0.6-17.3 1.5-11.4 10.4-20.5 21.9-22.2 5.3-0.9 10.6-1.3 15.9-1 5.3 0.3 10.7 0.3 16 0 5.3-0.3 10.6-1.8 15.3-4.3 4.3-2.6 8.1-6.2 11-10.4 2.9-4.2 5.5-8.5 7.9-13 2.4-4.5 5.1-8.7 8.3-12.7 3-4.1 7.1-7.2 11.8-9.4" fill="#F5BD0C" p-id="17968"></path><path d="M259.4 676.4c4.9-1.9 10.2-2.4 15.4-1.4 5.2 1 10.1 3.1 14.4 6.1 8.3 6.3 15.5 14.1 21.2 22.8 14.1 19.4 27.6 39.2 39.9 59.8 10 16.7 19.1 33.9 30.6 49.6 7.5 10.2 16 19.7 23.6 29.9 7.9 10 13.9 21.4 17.6 33.5 4.4 16.1 2.6 33.2-4.9 48.1-5.4 10.4-13.5 19.1-23.4 25.1-10 6-21.5 9-33.2 8.7-18.4-2.5-36.2-8.1-52.6-16.6-34.9-13.9-72.8-18.3-108.8-29.1-11.1-3.3-21.9-7.3-33.1-10.3-5-1.2-9.9-2.7-14.7-4.7-4.7-2-8.8-5.4-11.5-9.7-2-3.5-3-7.5-2.9-11.5 0.1-4 0.9-7.9 2.3-11.5 2.7-7.5 7.1-14.2 10-21.6 4.4-12.2 6.1-25.3 5-38.2-0.6-12.9-2.9-25.7-3.6-38.7-0.6-5.8-0.4-11.6 0.6-17.3 1.5-11.4 10.4-20.5 21.9-22.2 5.3-0.9 10.6-1.3 15.9-1 5.3 0.3 10.7 0.3 16 0 5.3-0.3 10.6-1.8 15.3-4.3 4.3-2.6 8.1-6.2 11-10.4 2.9-4.2 5.5-8.5 7.9-13 2.4-4.5 5.1-8.7 8.3-12.7 3-4.1 7.1-7.3 11.8-9.4" fill="#F5BD0C" p-id="17969"></path><path d="M267.1 684.8c4.4-1.7 9.3-2 13.9-0.9s8.9 3.2 12.6 6.2c7.1 6.2 13.1 13.6 17.6 21.9 12 19.4 23.7 39 34.6 59 7.9 15.3 16.8 30.1 26.6 44.2 6.8 9.2 14.6 17.6 21.6 26.6 7.3 8.9 12.8 19 16.2 29.9 4 14.3 2.3 29.6-4.5 42.9-5 9.4-12.5 17.3-21.7 22.6-9.2 5.4-19.8 8-30.4 7.5-16.7-2.6-32.9-7.6-48.2-14.9-30.4-11.1-63.5-12.5-94.7-21.2-11.2-3-22.1-7.1-33.4-9.9-5-1.1-10-2.5-14.8-4.3-4.8-1.8-9-5.2-11.8-9.5-1.8-3.4-2.7-7.2-2.5-11 0.2-3.8 1-7.6 2.4-11.2 2.7-7.1 7-13.6 9.7-20.7 3.8-11 5.1-22.6 3.9-34.2-0.8-11.5-2.9-22.9-3.5-34.5-0.4-5.1-0.2-10.3 0.7-15.4 0.9-5.1 3.3-9.8 6.9-13.6 4.2-3.8 9.4-6.3 15-7 5.6-0.7 11.2-0.7 16.7 0 5.6 0.7 11.2 0.9 16.8 0.8 11 0 21-6.4 25.7-16.4 2.3-4.5 4.3-9.2 5.9-13.9 1.7-4.8 4-9.3 6.7-13.6 2.8-4.3 6.8-7.7 11.5-9.7" fill="#F5BD0C" p-id="17970"></path>
    </svg>

    <!-- macOS 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'macos'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont macOS图标的SVG路径 -->
      <path d="M0 0h1024v1024H0z" fill="#FFFFFF" p-id="9207"></path><path d="M542.592 485.376c-71.893333 2.816-149.418667-10.709333-214.741333-43.392L341.333333 420.522667c63.488 29.866667 136.32 40.106667 201.216 37.333333 0 2.346667 2.773333-43.904 6.997334-69.12h-97.578667c8.874667-101.76 40.149333-192.298667 86.826667-269.354667H247.082667v444.458667h306.688c-6.997333-26.624-11.178667-79.36-11.178667-78.464z" fill="#D0E9FB" p-id="9208"></path><path d="M571.989333 455.978667c53.205333-5.12 99.882667-19.157333 128.384-37.333334l16.810667 21.461334c-39.253333 22.869333-90.112 37.802667-145.194667 43.434666 1.408 27.52 5.589333 53.674667 12.586667 80.298667h210.090667V119.381333h-223.573334c-47.658667 72.832-74.752 154.069333-84.992 239.957334h100.352c-8.405333 32.682667-13.525333 64.896-14.464 96.64zM379.648 186.624h28.501333v65.834667h-28.501333V186.624z" fill="#00ACEC" p-id="9209"></path><path d="M616.789333 186.624h28.501334v65.834667h-28.501334z" fill="#000000" p-id="9210"></path><path d="M571.989333 455.978667c53.205333-5.12 99.882667-19.157333 128.384-37.333334l16.810667 21.461334c-39.253333 22.869333-90.112 37.802667-145.194667 43.434666 2.346667 49.493333 14.506667 98.474667 38.741334 145.621334l-24.277334 13.056c-44.373333-69.973333-52.266667-164.266667-36.864-253.44h-97.578666c12.16-138.666667 65.365333-256.298667 142.848-347.818667l20.992 18.218667c-75.605333 87.765333-117.162667 189.994667-129.749334 300.16h100.352c-8.405333 32.682667-13.525333 64.896-14.464 96.64z" fill="#000000" p-id="9211"></path><path d="M542.592 457.813333v27.562667c-71.893333 2.816-149.418667-10.709333-214.741333-43.392L341.333333 420.522667c63.488 29.866667 136.32 40.106667 201.216 37.333333z" fill="#00ACEC" p-id="9212"></path><path d="M83.669333 713.216c-13.056 0.938667-19.626667 1.408-27.989333 1.408-5.12 0-10.24 0-16.341333-0.469333H29.994667c-4.693333 0-6.997333 1.834667-6.997334 6.528 0 4.181333 2.346667 6.528 7.936 6.997333l9.813334 0.938667c15.872 1.408 17.28 2.773333 17.28 16.341333 0 1.877333-0.469333 6.528-0.469334 11.648L48.64 913.92c-1.877333 31.744-2.346667 34.56-5.12 38.272-1.450667 2.346667-5.632 4.693333-12.16 5.632l-6.058667 0.938667c-8.874667 1.877333-10.282667 3.242667-10.282666 8.405333 0 3.712 2.346667 6.058667 6.058666 6.058667H23.466667l5.589333-0.426667c6.997333-0.512 17.749333-0.981333 25.685333-0.981333s24.277333 0.469333 28.928 0.938666l6.528 0.469334 2.346667 0.469333c3.754667 0.469333 6.997333-2.816 6.997333-6.997333 0-4.693333-2.304-6.528-7.936-7.466667l-7.466666-0.938667c-17.28-1.877333-19.114667-4.693333-19.114667-23.338666 0-6.058667 0-14.506667 0.426667-20.053334l5.12-153.173333L137.386667 963.413333c1.877333 5.632 2.816 6.528 5.12 6.528 2.816 0 3.285333-0.896 5.632-6.997333l68.608-200.704 0.938666 154.965333v14.933334c0 20.053333-1.877333 23.808-14.464 25.685333l-6.058666 0.938667c-6.528 0.938667-8.874667 2.816-8.874667 7.936 0 4.181333 2.346667 6.058667 6.997333 6.528h1.877334l5.12-0.938667c6.101333-0.426667 23.808-1.408 32.213333-1.408 7.466667 0 12.16 0 18.218667 0.938667 6.058667 0.469333 10.752 0.469333 12.117333 0.469333h5.632c4.693333 0 6.997333-1.877333 6.997333-6.528 0-4.181333-2.346667-6.058667-9.813333-6.997333l-6.528-0.938667c-9.813333-0.938667-13.056-4.224-14.506667-10.752-0.896-6.528-0.896-14.464-1.365333-42.453333l-0.938667-135.381334-0.426666-19.157333c0-18.645333 0.896-20.053333 15.829333-21.930667l8.874667-0.938666c6.528-0.938667 8.405333-2.816 8.405333-7.466667 0-4.224-1.408-5.589333-6.997333-6.058667h-1.408l-5.589334 0.426667c-6.997333 0-12.629333 0.512-19.157333 0.512-6.997333 0-13.056-0.469333-25.173333-0.938667l-68.693334 199.338667-66.261333-199.808z m510.293334 60.672v-26.581333H588.8l-6.528 22.4h-0.469333l-6.997334-22.4h-5.12v26.581333h3.242667v-21.930667l7.466667 21.930667h2.816l7.466666-22.4v22.4h3.242667z m-251.648 176.469333c7.466667-3.285333 15.402667-9.344 20.992-15.872a25.301333 25.301333 0 0 0 5.12-16.341333l0.981333-41.984a196.266667 196.266667 0 0 0-27.093333 15.36c-2.346667 1.408-4.224 3.285333-6.058667 4.693333-14.037333 11.648-21.034667 24.746667-21.034667 36.864 0 12.16 6.528 20.053333 15.872 20.053334a28.16 28.16 0 0 0 11.221334-2.773334z m0-74.666666c7.936-4.693333 17.28-9.386667 27.093333-13.568v-33.621334c0-22.869333-5.12-32.682667-17.749333-32.682666a30.037333 30.037333 0 0 0-9.344 1.408c-12.586667 4.693333-21.930667 17.749333-25.173334 36.906666l-1.450666 6.997334c-1.877333 8.405333-3.712 10.24-10.24 10.24h-6.570667c-5.12 0-7.936-2.773333-7.936-7.466667 0-7.936 7.936-27.050667 16.810667-37.802667 9.386667-11.178667 21.504-19.157333 34.56-22.869333 5.12-1.408 10.752-2.346667 16.341333-2.346667 13.994667 0 20.992 1.877333 27.093333 6.997334 8.832 7.466667 11.178667 14.506667 11.178667 37.376v2.773333L395.989333 913.066667c0 27.52 0 27.52 0.938667 31.274666 0.469333 3.712 3.285333 7.466667 6.997333 7.936 6.997333 0.938667 9.813333-1.877333 12.629334-6.997333 2.304-6.101333 9.813333-6.101333 8.874666 0.426667a30.250667 30.250667 0 0 1-8.405333 19.626666c-7.466667 8.405333-17.749333 10.752-28.032 10.752-13.994667 0-20.522667-8.874667-20.522667-27.562666a82.688 82.688 0 0 1-26.154666 21.461333 66.816 66.816 0 0 1-27.52 6.997333c-18.218667 0-30.378667-13.056-30.378667-33.109333 0-23.338667 20.522667-47.616 57.898667-68.181333z m203.093333-125.141334h8.832v23.338667h3.285333v-23.338667h8.874667v-3.242666h-21.034667v3.242666z m7.424 192.341334c-8.405333 11.648-20.522667 21.461333-40.106667 20.992-38.314667-1.408-53.717333-42.922667-53.717333-89.173334 0-45.226667 20.053333-78.421333 47.146667-79.786666a27.946667 27.946667 0 0 1 22.4 8.832c6.101333 5.632 10.752 13.098667 13.098666 19.157333 4.650667 13.525333 17.706667 23.338667 15.872-17.28-0.938667-14.933333-19.157333-25.685333-39.68-25.173333-51.84 1.365333-87.765333 41.984-87.765333 105.472 0 55.552 27.52 92.416 68.608 92.416 15.872 0 34.986667-0.896 48.085333-11.178667 10.24-7.466667 13.056-11.221333 14.933334-19.157333 2.346667-10.24-6.058667-8.874667-8.874667-5.12z" fill="#000000" p-id="9213"></path><path d="M741.930667 703.402667c-66.304 1.408-113.92 58.794667-113.92 139.093333 0 83.114667 46.208 140.544 112.042666 140.544h1.877334c33.621333-0.469333 59.733333-13.994667 82.133333-42.965333 21.504-27.050667 30.805333-56.96 30.805333-98.005334 0-37.845333-7.936-67.242667-23.765333-91.52-20.053333-30.336-50.901333-47.146667-87.338667-47.146666h-1.834666z m0 265.642666c-24.32 0-40.618667-9.386667-56.490667-32.682666-16.810667-24.746667-24.32-53.717333-24.32-93.397334 0-73.258667 32.725333-124.16 80.810667-125.568h1.834666c21.973333 0 39.68 9.813333 54.186667 29.866667 16.768 22.442667 24.746667 53.717333 24.746667 94.805333 0 37.802667-6.570667 66.730667-21.504 90.069334-15.872 25.685333-33.621333 36.906667-59.306667 36.906666z m253.013333-249.770666c-14.506667-9.344-27.989333-13.056-46.677333-13.056-42.026667 0-73.301333 31.274667-73.301334 74.666666 0 29.866667 13.525333 48.085333 47.616 65.365334l21.930667 10.282666c26.624 13.525333 37.802667 28.458667 37.802667 52.736 0 33.152-20.053333 56.490667-47.616 56.490667-14.464 0-27.050667-5.589333-34.986667-16.341333-6.997333-8.832-12.629333-22.869333-14.933333-34.56l-0.938667-8.832c-1.408-7.936-2.816-10.282667-7.466667-10.282667-4.224 0-6.528 3.712-6.528 11.221333l1.365334 57.856a109.653333 109.653333 0 0 0 58.368 15.872c27.562667 0 48.085333-9.344 62.08-28.458666 11.221333-14.933333 17.28-33.621333 17.28-53.674667 0-34.56-13.525333-55.552-45.269334-71.893333l-14.506666-7.466667c-26.112-13.098667-31.701333-16.810667-38.272-24.32-6.528-7.424-9.813333-17.237333-9.813334-29.397333 0-26.112 16.853333-44.8 39.253334-44.8 21.930667 0 38.741333 16.341333 42.453333 40.618666l0.938667 6.528c0.469333 5.12 2.346667 7.466667 6.101333 7.466667 4.693333 0 5.589333-0.426667 6.528-8.405333v-0.938667l-1.408-46.677333z" fill="#00ACEC" p-id="9214"></path>
    </svg>

    <!-- macOS 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'darwin'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont macOS图标的SVG路径 -->
      <path d="M0 0h1024v1024H0z" fill="#FFFFFF" p-id="9207"></path><path d="M542.592 485.376c-71.893333 2.816-149.418667-10.709333-214.741333-43.392L341.333333 420.522667c63.488 29.866667 136.32 40.106667 201.216 37.333333 0 2.346667 2.773333-43.904 6.997334-69.12h-97.578667c8.874667-101.76 40.149333-192.298667 86.826667-269.354667H247.082667v444.458667h306.688c-6.997333-26.624-11.178667-79.36-11.178667-78.464z" fill="#D0E9FB" p-id="9208"></path><path d="M571.989333 455.978667c53.205333-5.12 99.882667-19.157333 128.384-37.333334l16.810667 21.461334c-39.253333 22.869333-90.112 37.802667-145.194667 43.434666 1.408 27.52 5.589333 53.674667 12.586667 80.298667h210.090667V119.381333h-223.573334c-47.658667 72.832-74.752 154.069333-84.992 239.957334h100.352c-8.405333 32.682667-13.525333 64.896-14.464 96.64zM379.648 186.624h28.501333v65.834667h-28.501333V186.624z" fill="#00ACEC" p-id="9209"></path><path d="M616.789333 186.624h28.501334v65.834667h-28.501334z" fill="#000000" p-id="9210"></path><path d="M571.989333 455.978667c53.205333-5.12 99.882667-19.157333 128.384-37.333334l16.810667 21.461334c-39.253333 22.869333-90.112 37.802667-145.194667 43.434666 2.346667 49.493333 14.506667 98.474667 38.741334 145.621334l-24.277334 13.056c-44.373333-69.973333-52.266667-164.266667-36.864-253.44h-97.578666c12.16-138.666667 65.365333-256.298667 142.848-347.818667l20.992 18.218667c-75.605333 87.765333-117.162667 189.994667-129.749334 300.16h100.352c-8.405333 32.682667-13.525333 64.896-14.464 96.64z" fill="#000000" p-id="9211"></path><path d="M542.592 457.813333v27.562667c-71.893333 2.816-149.418667-10.709333-214.741333-43.392L341.333333 420.522667c63.488 29.866667 136.32 40.106667 201.216 37.333333z" fill="#00ACEC" p-id="9212"></path><path d="M83.669333 713.216c-13.056 0.938667-19.626667 1.408-27.989333 1.408-5.12 0-10.24 0-16.341333-0.469333H29.994667c-4.693333 0-6.997333 1.834667-6.997334 6.528 0 4.181333 2.346667 6.528 7.936 6.997333l9.813334 0.938667c15.872 1.408 17.28 2.773333 17.28 16.341333 0 1.877333-0.469333 6.528-0.469334 11.648L48.64 913.92c-1.877333 31.744-2.346667 34.56-5.12 38.272-1.450667 2.346667-5.632 4.693333-12.16 5.632l-6.058667 0.938667c-8.874667 1.877333-10.282667 3.242667-10.282666 8.405333 0 3.712 2.346667 6.058667 6.058666 6.058667H23.466667l5.589333-0.426667c6.997333-0.512 17.749333-0.981333 25.685333-0.981333s24.277333 0.469333 28.928 0.938666l6.528 0.469334 2.346667 0.469333c3.754667 0.469333 6.997333-2.816 6.997333-6.997333 0-4.693333-2.304-6.528-7.936-7.466667l-7.466666-0.938667c-17.28-1.877333-19.114667-4.693333-19.114667-23.338666 0-6.058667 0-14.506667 0.426667-20.053334l5.12-153.173333L137.386667 963.413333c1.877333 5.632 2.816 6.528 5.12 6.528 2.816 0 3.285333-0.896 5.632-6.997333l68.608-200.704 0.938666 154.965333v14.933334c0 20.053333-1.877333 23.808-14.464 25.685333l-6.058666 0.938667c-6.528 0.938667-8.874667 2.816-8.874667 7.936 0 4.181333 2.346667 6.058667 6.997333 6.528h1.877334l5.12-0.938667c6.101333-0.426667 23.808-1.408 32.213333-1.408 7.466667 0 12.16 0 18.218667 0.938667 6.058667 0.469333 10.752 0.469333 12.117333 0.469333h5.632c4.693333 0 6.997333-1.877333 6.997333-6.528 0-4.181333-2.346667-6.058667-9.813333-6.997333l-6.528-0.938667c-9.813333-0.938667-13.056-4.224-14.506667-10.752-0.896-6.528-0.896-14.464-1.365333-42.453333l-0.938667-135.381334-0.426666-19.157333c0-18.645333 0.896-20.053333 15.829333-21.930667l8.874667-0.938666c6.528-0.938667 8.405333-2.816 8.405333-7.466667 0-4.224-1.408-5.589333-6.997333-6.058667h-1.408l-5.589334 0.426667c-6.997333 0-12.629333 0.512-19.157333 0.512-6.997333 0-13.056-0.469333-25.173333-0.938667l-68.693334 199.338667-66.261333-199.808z m510.293334 60.672v-26.581333H588.8l-6.528 22.4h-0.469333l-6.997334-22.4h-5.12v26.581333h3.242667v-21.930667l7.466667 21.930667h2.816l7.466666-22.4v22.4h3.242667z m-251.648 176.469333c7.466667-3.285333 15.402667-9.344 20.992-15.872a25.301333 25.301333 0 0 0 5.12-16.341333l0.981333-41.984a196.266667 196.266667 0 0 0-27.093333 15.36c-2.346667 1.408-4.224 3.285333-6.058667 4.693333-14.037333 11.648-21.034667 24.746667-21.034667 36.864 0 12.16 6.528 20.053333 15.872 20.053334a28.16 28.16 0 0 0 11.221334-2.773334z m0-74.666666c7.936-4.693333 17.28-9.386667 27.093333-13.568v-33.621334c0-22.869333-5.12-32.682667-17.749333-32.682666a30.037333 30.037333 0 0 0-9.344 1.408c-12.586667 4.693333-21.930667 17.749333-25.173334 36.906666l-1.450666 6.997334c-1.877333 8.405333-3.712 10.24-10.24 10.24h-6.570667c-5.12 0-7.936-2.773333-7.936-7.466667 0-7.936 7.936-27.050667 16.810667-37.802667 9.386667-11.178667 21.504-19.157333 34.56-22.869333 5.12-1.408 10.752-2.346667 16.341333-2.346667 13.994667 0 20.992 1.877333 27.093333 6.997334 8.832 7.466667 11.178667 14.506667 11.178667 37.376v2.773333L395.989333 913.066667c0 27.52 0 27.52 0.938667 31.274666 0.469333 3.712 3.285333 7.466667 6.997333 7.936 6.997333 0.938667 9.813333-1.877333 12.629334-6.997333 2.304-6.101333 9.813333-6.101333 8.874666 0.426667a30.250667 30.250667 0 0 1-8.405333 19.626666c-7.466667 8.405333-17.749333 10.752-28.032 10.752-13.994667 0-20.522667-8.874667-20.522667-27.562666a82.688 82.688 0 0 1-26.154666 21.461333 66.816 66.816 0 0 1-27.52 6.997333c-18.218667 0-30.378667-13.056-30.378667-33.109333 0-23.338667 20.522667-47.616 57.898667-68.181333z m203.093333-125.141334h8.832v23.338667h3.285333v-23.338667h8.874667v-3.242666h-21.034667v3.242666z m7.424 192.341334c-8.405333 11.648-20.522667 21.461333-40.106667 20.992-38.314667-1.408-53.717333-42.922667-53.717333-89.173334 0-45.226667 20.053333-78.421333 47.146667-79.786666a27.946667 27.946667 0 0 1 22.4 8.832c6.101333 5.632 10.752 13.098667 13.098666 19.157333 4.650667 13.525333 17.706667 23.338667 15.872-17.28-0.938667-14.933333-19.157333-25.685333-39.68-25.173333-51.84 1.365333-87.765333 41.984-87.765333 105.472 0 55.552 27.52 92.416 68.608 92.416 15.872 0 34.986667-0.896 48.085333-11.178667 10.24-7.466667 13.056-11.221333 14.933334-19.157333 2.346667-10.24-6.058667-8.874667-8.874667-5.12z" fill="#000000" p-id="9213"></path><path d="M741.930667 703.402667c-66.304 1.408-113.92 58.794667-113.92 139.093333 0 83.114667 46.208 140.544 112.042666 140.544h1.877334c33.621333-0.469333 59.733333-13.994667 82.133333-42.965333 21.504-27.050667 30.805333-56.96 30.805333-98.005334 0-37.845333-7.936-67.242667-23.765333-91.52-20.053333-30.336-50.901333-47.146667-87.338667-47.146666h-1.834666z m0 265.642666c-24.32 0-40.618667-9.386667-56.490667-32.682666-16.810667-24.746667-24.32-53.717333-24.32-93.397334 0-73.258667 32.725333-124.16 80.810667-125.568h1.834666c21.973333 0 39.68 9.813333 54.186667 29.866667 16.768 22.442667 24.746667 53.717333 24.746667 94.805333 0 37.802667-6.570667 66.730667-21.504 90.069334-15.872 25.685333-33.621333 36.906667-59.306667 36.906666z m253.013333-249.770666c-14.506667-9.344-27.989333-13.056-46.677333-13.056-42.026667 0-73.301333 31.274667-73.301334 74.666666 0 29.866667 13.525333 48.085333 47.616 65.365334l21.930667 10.282666c26.624 13.525333 37.802667 28.458667 37.802667 52.736 0 33.152-20.053333 56.490667-47.616 56.490667-14.464 0-27.050667-5.589333-34.986667-16.341333-6.997333-8.832-12.629333-22.869333-14.933333-34.56l-0.938667-8.832c-1.408-7.936-2.816-10.282667-7.466667-10.282667-4.224 0-6.528 3.712-6.528 11.221333l1.365334 57.856a109.653333 109.653333 0 0 0 58.368 15.872c27.562667 0 48.085333-9.344 62.08-28.458666 11.221333-14.933333 17.28-33.621333 17.28-53.674667 0-34.56-13.525333-55.552-45.269334-71.893333l-14.506666-7.466667c-26.112-13.098667-31.701333-16.810667-38.272-24.32-6.528-7.424-9.813333-17.237333-9.813334-29.397333 0-26.112 16.853333-44.8 39.253334-44.8 21.930667 0 38.741333 16.341333 42.453333 40.618666l0.938667 6.528c0.469333 5.12 2.346667 7.466667 6.101333 7.466667 4.693333 0 5.589333-0.426667 6.528-8.405333v-0.938667l-1.408-46.677333z" fill="#00ACEC" p-id="9214"></path>
    </svg>

    <!-- Android 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'android'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont Android图标的SVG路径 -->
      <path d="M157.947546 735.493119c-45.735653 0-82.696442-37.226694-82.696442-82.696443v-253.141521c0-45.469748 36.960789-82.696442 82.696442-82.696443 22.070112 0 42.810699 8.508959 58.499091 24.197351 15.688393 15.688393 24.197351 36.428979 24.197352 58.499092v253.141521c0 22.070112-8.508959 42.810699-24.197352 58.499091-15.688393 15.422488-36.428979 24.197351-58.499091 24.197352" fill="#FFFFFF" p-id="11075"></path><path d="M157.947546 339.028824c-33.504025 0-60.360426 27.122306-60.360426 60.360426v253.141521c0 33.504025 27.122306 60.360426 60.360426 60.626331 33.504025 0 60.360426-27.122306 60.360426-60.626331v-253.141521c0-33.23812-27.122306-60.360426-60.360426-60.360426M806.489743 351.526357H217.244352v-22.336017c0-91.205401 49.724227-176.029083 131.091145-226.285121L316.692807 45.469748c-3.988574-6.647624-4.786289-15.422488-2.65905-23.133731 2.393145-7.711244 7.711244-14.624773 14.890678-18.347443C333.44482 1.329525 338.497014 0 343.549208 0c11.168008 0 21.538302 6.115814 26.856401 15.954298l33.23812 60.360425c34.30174-12.497533 70.464814-18.879252 107.957414-18.879252 38.024409 0 74.453389 6.381719 108.755128 19.145157L653.328486 15.954298c5.318099-9.838484 15.688393-15.954298 26.856401-15.954298 5.052194 0 10.104388 1.329525 14.624773 3.722669 7.445339 3.988574 12.763438 10.636198 14.890678 18.613348 2.393145 7.977149 1.329525 16.220203-2.65905 23.399636l-31.64269 57.701376C756.765515 153.693067 806.489743 238.516749 806.489743 329.72215v21.804207z" fill="#FFFFFF" p-id="11076"></path><path d="M645.351337 111.680083l42.278889-77.112438c2.12724-3.988574 0.797715-9.306674-3.19086-11.433914-4.254479-2.12724-9.306674-0.797715-11.433913 3.456765l-42.544793 77.910153c-35.89717-15.954298-76.048818-24.995066-118.327708-24.729161-42.278889 0-82.430538 8.774864-118.061802 24.729161l-42.544793-77.644248c-2.12724-3.988574-7.445339-5.584004-11.433913-3.456765s-5.584004 7.445339-3.456765 11.433914l42.278889 77.112438C295.95222 154.756687 239.846274 236.123604 239.846274 329.72215h544.573357c-0.265905-93.864451-56.105947-175.231368-139.068294-218.042067m-257.396001 119.125422c-12.497533 0-22.867827-10.104388-22.867826-22.867826 0-12.497533 10.104388-22.867827 22.867826-22.867827 12.497533 0 22.867827 10.370293 22.867827 22.867827 0 12.497533-10.104388 22.867827-22.867827 22.867826m248.089328 0c-12.497533 0-22.867827-10.104388-22.867827-22.867826 0-12.497533 10.104388-22.867827 22.867827-22.867827 12.497533 0 22.867827 10.370293 22.867826 22.867827 0 12.497533-10.370293 22.867827-22.867826 22.867826" fill="#FFFFFF" p-id="11077"></path><path d="M410.823163 1024c-45.469748 0-82.696442-36.960789-82.696443-82.696442v-111.680084h-21.804206c-23.133731 0-44.937938-9.040769-61.424046-25.260971-16.486108-16.486108-25.526876-38.024409-25.260971-61.424045V328.392625H804.894313v414.545833c0 47.862893-38.822124 86.685017-86.685017 86.685016h-21.804206v111.680084c0 45.469748-36.960789 82.696442-82.696443 82.696442-22.070112 0-42.810699-8.508959-58.499091-24.197351-15.688393-15.688393-24.197351-36.428979-24.197351-58.499091v-111.680084h-37.226695v111.680084c-0.265905 45.469748-37.492599 82.696442-82.962347 82.696442" fill="#FFFFFF" p-id="11078"></path><path d="M241.973513 742.672553c0 35.631265 28.717736 64.614905 64.614906 64.614905h43.874318V941.303558c0 33.23812 27.122306 60.360426 60.360426 60.360425 33.504025 0 60.360426-27.122306 60.360426-60.62633v-134.0161h81.632822v134.0161c0 33.23812 27.122306 60.360426 60.360426 60.360425 33.504025 0 60.360426-27.122306 60.360426-60.360425v-134.0161h44.140223c35.631265 0 64.614905-28.983641 64.614906-64.614905V349.930927H241.707608l0.265905 392.741626zM866.052454 735.227214c-45.469748 0-82.696442-36.960789-82.696443-82.696443v-253.141521c0-45.735653 36.960789-82.696442 82.696443-82.696443 45.735653 0 82.696442 36.960789 82.696442 82.696443v253.141521c0 45.735653-36.960789 82.696442-82.696442 82.696443" fill="#FFFFFF" p-id="11079"></path><path d="M866.052454 339.028824c-33.504025 0-60.360426 27.122306-60.360426 60.360426v253.141521c0 33.504025 27.122306 60.360426 60.360426 60.360426 33.504025 0 60.360426-27.122306 60.360426-60.360426v-253.141521c0-33.23812-26.856401-60.360426-60.360426-60.360426" fill="#FFFFFF" p-id="11080"></path><path d="M157.947546 339.028824c-33.504025 0-60.360426 27.122306-60.360426 60.360426v253.141521c0 33.504025 27.122306 60.360426 60.360426 60.360426 33.504025 0 60.360426-27.122306 60.360426-60.360426v-253.141521c0-33.23812-27.122306-60.360426-60.360426-60.360426M645.351337 111.680083l42.278889-77.112438c2.12724-3.988574 0.797715-9.040769-3.19086-11.433914-3.988574-2.12724-9.306674-0.797715-11.433913 3.456765l-42.544793 77.910153c-35.89717-15.954298-76.048818-24.995066-118.593612-24.995066-42.278889 0-82.430538 8.774864-118.061803 24.729161l-42.544793-77.644248c-2.12724-3.988574-7.445339-5.584004-11.433913-3.456765s-5.584004 7.445339-3.456765 11.433914l42.278889 77.112438c-82.962347 42.810699-139.068294 124.177616-139.068294 217.776162h544.573357c0-93.598546-55.840042-174.965464-138.802389-217.776162m-257.396001 119.125422c-12.497533 0-22.867827-10.104388-22.867826-22.867826 0-12.497533 10.104388-22.867827 22.867826-22.867827 12.497533 0 22.867827 10.370293 22.867827 22.867827 0 12.497533-10.104388 22.867827-22.867827 22.867826m248.089328 0c-12.497533 0-22.867827-10.104388-22.867827-22.867826 0-12.497533 10.104388-22.867827 22.867827-22.867827 12.497533 0 22.867827 10.370293 22.867826 22.867827 0 12.497533-10.370293 22.867827-22.867826 22.867826m-394.337056 119.657232v392.475721c0 35.631265 28.717736 64.614905 64.614906 64.614905h43.874318v134.016099c0 33.23812 27.122306 60.360426 60.360426 60.360426 33.504025 0 60.360426-27.122306 60.360426-60.62633v-134.0161h81.632822V941.303558c0 33.23812 27.122306 60.360426 60.360426 60.360425 33.504025 0 60.360426-27.122306 60.360426-60.360425v-134.0161h44.140223c35.631265 0 64.614905-28.717736 64.614906-64.614905V350.196832l-540.318879 0.265905z m684.971177 48.926513c0-33.504025-27.122306-60.360426-60.360426-60.360426-33.504025 0-60.360426 27.122306-60.360426 60.360426v253.141521c0 33.504025 27.122306 60.360426 60.360426 60.360426 33.504025 0 60.360426-27.122306 60.360426-60.360426v-253.141521z" fill="#A4C439" p-id="11081"></path>
    </svg>

    <!-- iOS 图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'ios'" viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont iOS图标的SVG路径 -->
      <path d="M959.9 310.4c2-43-4-86.1-17.7-127-22-52.2-66.2-91.8-120.4-108C786.4 66.6 750 62.7 713.5 64H310.4c-36.4-1.3-72.9 2.6-108.3 11.4-54.2 16.2-98.4 55.8-120.4 108-13.6 40.9-19.7 83.9-17.7 127v403.1c-1.9 43.1 4.1 86.1 17.7 127 22 52.2 66.2 91.8 120.4 108 35.4 8.8 71.8 12.7 108.3 11.4h403.1c36.4 1.3 72.9-2.6 108.3-11.4 54.2-16.2 98.4-55.8 120.4-108 13.6-40.9 19.7-83.9 17.7-127V310.4zM241.7 680h-33.3V481h33.3v199zM225 447.4c-11.4-0.1-20.6-9.4-20.5-20.9 0.1-11.4 9.4-20.6 20.9-20.5 11.3 0.1 20.5 9.3 20.5 20.7-0.1 11.5-9.4 20.8-20.9 20.7zM425.1 685c-87.9 0-143.3-62.8-143.3-162.7s55.3-162.9 143.3-162.9 143.5 63 143.5 162.9S513.3 685 425.1 685z m290.2 0c-66 0-112.4-36.4-115.7-90.8h30c3.3 37.9 38.9 63.6 88.4 63.6 47.3 0 81.2-25.8 81.2-61.2 0-29.5-20.4-47-67.2-58.8l-39.2-10.1c-58.4-14.9-84.4-39.6-84.4-81.2 0-51 45.9-87.3 108-87.3 61.6 0 106.4 36.4 108.3 86.4h-30c-2.8-35.6-34.4-59.3-79.2-59.3-43.7 0-76.1 24.3-76.1 59 0 27.1 19.6 43.1 66 54.9l34.2 8.8c63.4 16 90.1 40.3 90.1 84.2 0.1 55.5-44.6 91.8-114.4 91.8zM425.1 387.1c-69.1 0-112.6 52.3-112.6 135.2S356 657.2 425.1 657.2 538 604.9 538 522.3s-43.7-135.2-112.9-135.2z" p-id="18842"></path>
    </svg>

    <svg v-else-if="type === 'unix'" viewBox="0 0 1024 1024" class="icon-svg">
      <path d="M0 0m128 0l768 0q128 0 128 128l0 768q0 128-128 128l-768 0q-128 0-128-128l0-768q0-128 128-128Z" fill="#000161" p-id="31239"></path><path d="M42 376h63.312v145.864a144 144 0 0 0 2.344 25.88c9.64 52.784 25.16 79.176 46.56 79.176h82.168V376h63.584v270H142.496a64 64 0 0 1-15.584-1.92c-20.168-5.072-34.072-10.784-41.704-17.16-30.744-25.68-43.208-65.208-43.208-93.296V376zM339.608 376v270h62.888V397.032h70.488a32 32 0 0 1 10.232 1.68c33.376 11.256 50.056 42.064 50.056 92.416V646h64.016V503.36a96 96 0 0 0-3.792-26.736C574.272 410.352 537.152 376.816 482.136 376c-58.904-0.872-106.408-0.872-142.528 0zM631.376 376h65.736v270h-65.736zM724.208 376h64.248l82.96 116L960.208 376h21.8l-96.92 134.672L982 646h-63.216l-82.056-111.744-85.2 111.744h-27.32l97.968-128.376z" fill="#FFFFFF" p-id="31240"></path>
    </svg>

    <!-- C2 Server图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else-if="type === 'c2server'" viewBox="0 0 1024 1024" class="icon-svg c2server-icon">
      <!-- TODO: 替换为IconFont C2 Server图标的SVG路径 -->
      <g transform="translate(200, 200) scale(0.85)">
      <path d="M901.9447 811.956v181.974H122.0567v-181.974c0-82.788 67.19-149.978 149.978-149.978h479.932c82.788 0 149.978 67.188 149.978 149.978z" fill="#6F7D96" p-id="1735"></path><path d="M901.9447 811.956v181.974H512.0007V661.978h239.966c82.788 0 149.978 67.188 149.978 149.978z" fill="#66728B" p-id="1736"></path><path d="M512.0007 0.072c-214.772 0-389.944 190.372-389.944 421.94s361.75 409.74 376.748 416.94c4.196 1.798 8.996 3 13.196 3 4.2 0 9-1.202 13.196-3 14.998-7.202 376.748-185.376 376.748-416.94S726.7707 0.072 512.0007 0.072z" fill="#495A79" p-id="1737"></path><path d="M901.9447 422.012c0 231.566-361.75 409.74-376.748 416.94-4.196 1.798-8.996 3-13.196 3V0.072c214.77 0 389.944 190.374 389.944 421.94z" fill="#42516D" p-id="1738"></path><path d="M789.7607 326.022c-4.2-47.994-42.596-83.986-89.988-83.986H324.2267c-47.392 0-85.788 35.992-89.988 83.986-14.998 176.974 58.79 349.15 197.972 460.734l61.194 48.598c5.402 4.196 11.998 6.6 18.598 6.6 6.598 0 13.196-2.402 18.598-6.6l61.194-48.598c139.174-111.582 212.964-283.756 197.966-460.734z" fill="#E1E4FB" p-id="1739"></path><path d="M591.7887 786.756l-61.194 48.598c-5.402 4.196-11.998 6.6-18.598 6.6V242.038h187.774c47.392 0 85.788 35.992 89.988 83.986 15 176.976-58.79 349.15-197.97 460.732z" fill="#C5C9F7" p-id="1740"></path><path d="M658.9807 525.196c-16.8 33.596-49.794 53.994-86.988 53.994-22.796 0-43.192-8.398-59.992-22.196-16.8 13.796-37.194 22.196-59.992 22.196-37.194 0-70.188-20.4-86.988-53.994-7.2-14.398-1.198-32.394 13.8-40.192 14.398-7.2 32.394-1.202 40.192 13.796 12.596 25.198 53.392 25.198 65.988 0 5.402-10.798 16.2-16.196 26.996-16.196s21.596 5.398 26.996 16.196c12.596 25.198 53.392 25.198 65.988 0 7.798-14.998 25.796-20.994 40.192-13.796 15.006 7.796 21.006 25.794 13.808 40.192z" fill="#495A79" p-id="1741"></path><path d="M631.9847 422.012h-59.992c-16.58 0-29.996-13.416-29.996-29.996s13.416-29.996 29.996-29.996h59.992c16.58 0 29.996 13.416 29.996 29.996s-13.418 29.996-29.996 29.996z" fill="#42516D" p-id="1742"></path><path d="M452.0087 422.012h-59.992c-16.58 0-29.996-13.416-29.996-29.996s13.416-29.996 29.996-29.996h59.992c16.58 0 29.996 13.416 29.996 29.996s-13.416 29.996-29.996 29.996zM541.9967 691.974v119.982c0 16.796-13.196 29.996-29.996 29.996-16.8 0-29.996-13.2-29.996-29.996v-119.982c0-16.8 13.196-29.996 29.996-29.996 16.8 0 29.996 13.196 29.996 29.996z" fill="#495A79" p-id="1743"></path><path d="M1022.1267 984.33l-46.192-140.58c-11.998-37.194-48.196-61.79-87.186-61.79h-76.792c-85.186 0-165.576 32.996-226.17 93.586L512.0007 949.336l-73.788-73.788c-60.592-60.592-140.982-93.586-226.17-93.586H135.2527c-38.992 0-75.188 24.594-87.186 61.79L1.8747 984.332c-3.604 9-1.802 19.198 3.6 26.996 5.998 7.798 14.998 12.6 24.594 12.6h963.862c9.596 0 18.598-4.8 24.594-12.6 5.404-7.8 7.202-17.998 3.602-26.998z" fill="#495A79" p-id="1744"></path><path d="M658.9807 525.196c-16.8 33.596-49.794 53.994-86.988 53.994-22.796 0-43.192-8.398-59.992-22.196v-74.39c10.798 0 21.596 5.398 26.996 16.196 12.596 25.198 53.392 25.198 65.988 0 7.798-14.998 25.796-20.994 40.192-13.796 15.002 7.796 21.002 25.794 13.804 40.192zM541.9967 691.974v119.982c0 16.796-13.196 29.996-29.996 29.996v-179.974c16.798 0 29.996 13.196 29.996 29.996zM1018.5267 1011.326c-5.998 7.798-14.998 12.6-24.594 12.6H512.0007v-74.592l73.788-73.788c60.592-60.592 140.982-93.586 226.17-93.586h76.792c38.992 0 75.188 24.594 87.186 61.79l46.192 140.58c3.598 9 1.8 19.198-3.602 26.996z" fill="#42516D" p-id="1745"></path>      </g>
    </svg>

    <!-- 默认/未知系统图标 - 请在此处替换为IconFont的SVG代码 -->
    <svg v-else viewBox="0 0 1024 1024" class="icon-svg">
      <!-- TODO: 替换为IconFont 通用电脑图标的SVG路径 -->
      <path d="M512 0C229.248 0 0 229.248 0 512c0 282.784 229.248 512 512 512 282.784 0 512-229.216 512-512S794.784 0 512 0z m-0.352 991.68c-265.088 0-480-214.912-480-480.032s214.912-480 480-480c265.12 0 480.032 214.912 480.032 480 0 265.12-214.912 480.032-480.032 480.032z m-10.4-315.04c-8.32 0-15.36 2.88-21.088 8.608s-8.608 12.736-8.608 21.088 2.848 15.36 8.608 21.088 12.736 8.576 21.088 8.576 15.36-2.88 21.088-8.608 8.608-12.736 8.608-21.088a28.8 28.8 0 0 0-29.696-29.664z m10.624-398.752c-39.168 0-70.304 5.216-93.44 27.296S382.784 352 380.928 384h40.64c2.08 0 11.136-38.336 27.2-52.896s36.576-18.944 61.568-18.944 45.504 8.64 61.568 23.008c16 14.368 24.064 33.44 24.064 55.744 0 16.672-4.128 31.52-12.384 43.808s-22.208 25.184-42.016 38.304c-22.08 14.368-37.92 28.96-47.488 43.52-9.6 14.592-14.08 32.16-14.08 52.576V608h32v-32.928c0-17.504 8.416-31.712 15.712-42.656s23.52-22.976 43.968-36.096c22.048-14.176 39.744-29.856 50.688-47.04s17.024-37.024 17.024-59.52c0-32.064-11.776-58.752-35.84-80s-54.4-31.872-91.68-31.872z" p-id="24181"></path>
    </svg>

    <!-- 监听器类型标签 - 仅在明确需要时显示 -->
    <div v-if="listenerType && showListenerBadge" class="listener-badge">
      {{ listenerType }}
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) => [
      'windows', 'ubuntu', 'debian', 'kali', 'centos', 'redhat', 'fedora', 'suse', 'arch', 'linux',
      'macos', 'darwin', 'android', 'ios', 'unix', 'c2server', 'default'
    ].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  status: {
    type: String,
    default: 'offline',
    validator: (value) => ['online', 'offline'].includes(value)
  },
  listenerType: {
    type: String,
    default: ''
  },
  showListenerBadge: {
    type: Boolean,
    default: false
  }
})

// 计算图标样式类
const iconClass = computed(() => {
  return [
    `size-${props.size}`,
    `status-${props.status}`,
    `type-${props.type}`
  ]
})
</script>

<script>
import { computed } from 'vue'
export default {
  name: 'SystemIcons'
}
</script>

<style scoped lang="scss">
.system-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  .icon-svg {
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));

    // C2 Server图标特殊处理
    &.c2server-icon {
      transform: translateY(-5px); // 向上微调
    }
  }

  // 尺寸变体
  &.size-small {
    width: 32px;
    height: 32px;
  }

  &.size-medium {
    width: 48px;
    height: 48px;
  }

  &.size-large {
    width: 64px;
    height: 64px;
  }

  // 状态变体
  &.status-online {
    .icon-svg {
      filter: drop-shadow(0 0 8px rgba(76, 175, 80, 0.4));
    }
  }

  &.status-offline {
    .icon-svg {
      opacity: 0.6;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
  }

  // 悬停效果
  &:hover {
    .icon-svg {
      transform: scale(1.1);
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
    }

    &.status-online .icon-svg {
      filter: drop-shadow(0 0 12px rgba(76, 175, 80, 0.6));
    }
  }

  // 监听器类型标签
  .listener-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    font-size: 8px;
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 4px;
    line-height: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 10;
  }
}

// 系统特定的悬停效果
.system-icon.type-windows:hover .icon-svg {
  filter: drop-shadow(0 0 12px rgba(0, 120, 212, 0.6));
}

.system-icon.type-linux:hover .icon-svg {
  filter: drop-shadow(0 0 12px rgba(252, 198, 36, 0.6));
}

.system-icon.type-macos:hover .icon-svg {
  filter: drop-shadow(0 0 12px rgba(79, 195, 247, 0.6));
}

.system-icon.type-android:hover .icon-svg {
  filter: drop-shadow(0 0 12px rgba(76, 175, 80, 0.6));
}

.system-icon.type-ios:hover .icon-svg {
  filter: drop-shadow(0 0 12px rgba(158, 158, 158, 0.6));
}
</style>
