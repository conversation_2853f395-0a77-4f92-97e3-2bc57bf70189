<template>
  <a-modal
    :open="open"
    title="个人信息"
    :width="500"
    :footer="null"
    @cancel="handleCancel"
  >
    <a-form
      :model="formData"
      :rules="rules"
      ref="formRef"
      layout="vertical"
    >
      <a-form-item label="用户名" name="username">
        <a-input v-model:value="formData.username" placeholder="请输入用户名" />
      </a-form-item>
      
      <a-form-item label="角色" name="roleName">
        <a-input v-model:value="formData.role.roleName" disabled />
      </a-form-item>
      
      <a-form-item label="修改密码" name="password">
        <a-input-password v-model:value="formData.password" placeholder="留空表示不修改密码" />
      </a-form-item>
      
      <a-form-item label="确认密码" name="confirmPassword">
        <a-input-password v-model:value="formData.confirmPassword" placeholder="留空表示不修改密码" />
      </a-form-item>
      
      <a-form-item>
        <div class="form-buttons">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleSubmit" :loading="loading">保存</a-button>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import { message } from 'ant-design-vue';
import { updateUserInfo } from '@/api/user';

// 接收属性
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  userInfo: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits(['update:open', 'success']);

// 表单引用
const formRef = ref(null);

// 状态变量
const loading = ref(false);
const formData = reactive({
  username: '',
  role: { roleName: '' },
  password: '',
  confirmPassword: ''
});

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    {
      validator: (_, value) => {
        if (formData.password && value !== formData.password) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ]
};

// 监听用户信息变化，更新表单数据
watch(
  () => props.userInfo,
  (newVal) => {
    if (newVal) {
      formData.username = newVal.username || '';
      formData.role.roleName = newVal.role?.roleName || '';
      formData.password = '';
      formData.confirmPassword = '';
    }
  },
  { immediate: true, deep: true }
);

// 处理表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;
    
    // 准备请求数据
    const requestData = {
      username: formData.username,
      password: formData.password || '' // 如果密码为空，则不修改密码
    };
    
    try {
      // 调用API更新用户信息
      const response = await updateUserInfo(requestData);
      
      // 创建更新后的用户信息对象
      const updatedUserInfo = {
        ...props.userInfo,
        username: formData.username
      };
      
      // 更新本地存储
      localStorage.setItem('userInfo', JSON.stringify(updatedUserInfo));
      
      // 发送成功事件
      emit('success', updatedUserInfo);
      emit('update:open', false);
      
      message.success('个人信息更新成功');
    } catch (apiError) {
      message.error('更新个人信息失败: ' + (apiError.response?.data?.error || apiError.response?.data?.message || apiError.message));
      console.error('API调用失败:', apiError);
    } finally {
      loading.value = false;
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 处理取消
const handleCancel = () => {
  formRef.value.resetFields();
  emit('update:open', false);
};
</script>

<style scoped lang="scss">
.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>