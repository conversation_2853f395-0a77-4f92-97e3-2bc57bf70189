<template>
  <div class="listener-content">
    <!-- 搜索和操作栏 -->
    <div class="action-bar">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="类型">
          <a-select
            v-model:value="searchForm.type"
            style="width: 120px"
            placeholder="选择类型"
            allowClear
          >
            <a-select-option value="tcp">TCP</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            style="width: 120px"
            placeholder="选择状态"
            allowClear
          >
            <a-select-option :value="1">启用</a-select-option>
            <a-select-option :value="0">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注">
          <a-input v-model:value="searchForm.remark" placeholder="搜索备注" allowClear />
        </a-form-item>
        <a-form-item label="本地地址">
          <a-input v-model:value="searchForm.localListenAddr" placeholder="搜索本地地址" allowClear />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <template #icon><SearchOutlined /></template>
            搜索
          </a-button>
          <a-button style="margin-left: 10px" @click="resetSearch">
            <template #icon><ReloadOutlined /></template>
            重置
          </a-button>
        </a-form-item>
      </a-form>
      
      <div class="action-buttons">
        <a-button type="primary" @click="showCreateModal">
          <template #icon><PlusOutlined /></template>
          新建监听器
        </a-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <a-table
      :columns="columns"
      :tableLayout='auto'
      :data-source="listenerList"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      rowKey="id"
    >
      <!-- 状态列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="record.status === 1 ? 'green' : 'red'">
            {{ record.status === 1 ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="showEditModal(record)">
              编辑
            </a-button>
            <a-button v-if="record.type !== 'pipe'" type="link" size="small" @click="showScriptInfoBoardModal(record)">
              快速上马
            </a-button>
            <a-button 
              type="link" 
              size="small" 
              @click="handleStatusChange(record)"
              :danger="record.status === 1"
            >
              {{ record.status === 1 ? '禁用' : '启用' }}
            </a-button>
            <a-popconfirm
              title="确定要删除这个监听器吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record.id)"
            >
              <a-button type="link" danger size="small">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 创建/编辑监听器弹窗 -->
    <ListenerForm
      v-model:open="formModalVisible"
      :formData="currentListener"
      :isEdit="isEdit"
      @success="handleFormSuccess"
    />

    <ScriptInfoBoard
        v-model:open="scriptInfoBoardModalVisible"
        :listener="currentListener"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { listenerApi } from '@/api';
import ListenerForm from './ListenerForm.vue';
import ScriptInfoBoard from './ScriptInfoBoard.vue';

// 接收父组件传递的属性
const props = defineProps({
  active: {
    type: Boolean,
    default: true
  }
});

// 表格列定义
const columns = [

  {
    title: 'ID',
    align: 'center',
    dataIndex: 'id',
    width: 60,
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'type',
    width: 80,
  },
  {
    title: '本地监听地址',
    align: 'center',
    dataIndex: 'localListenAddr',
    width: 150,
  },
  {
    title: '远程连接地址',
    align: 'center',
    dataIndex: 'remoteConnectAddr',
    width: 150,
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status',
    width: 80,
  },
  {
    title: '心跳间隔(秒)',
    align: 'center',
    dataIndex: 'pingDuration',
    width: 120,
  },
  {
    title: '最大超时次数',
    align: 'center',
    dataIndex: 'maxTimeoutCount',
    width: 120,
  },
  {
    title: '加密密钥',
    align: 'center',
    dataIndex: 'key',
    width: 120
  },
  {
    title: '加密盐值',
    align: 'center',
    dataIndex: 'salt',
    width: 120
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
  },
  {
    title: '操作',
    align: 'center',
    dataIndex: 'action',
    width: 200,
    fixed: 'right',
  },
];

// 状态变量
const loading = ref(false);
const listenerList = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

const searchForm = reactive({
  type: undefined,
  status: undefined,
  remark: '',
  localListenAddr: '',
  remoteConnectAddr: '',
});

const formModalVisible = ref(false);
const scriptInfoBoardModalVisible = ref(false);
const currentListener = ref({});
const isEdit = ref(false);

// 获取监听器列表
const getListenerList = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      remark: searchForm.remark || '',
      localListenAddr: searchForm.localListenAddr || '',
      remoteConnectAddr: searchForm.remoteConnectAddr || ''
    };

    const res = await listenerApi.getListenerList(params);
    listenerList.value = res.data.list;
    pagination.total = res.data.total;
  } catch (error) {
    console.error('获取监听器列表失败:', error);
    message.error('获取监听器列表失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 处理表格变化（分页、排序等）
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  getListenerList();
};

// 处理搜索
const handleSearch = () => {
  pagination.current = 1;
  getListenerList();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'status' || key === 'type' ? undefined : '';
  });
  pagination.current = 1;
  getListenerList();
};

// 显示创建弹窗
const showCreateModal = () => {
  isEdit.value = false;
  currentListener.value = {
    type: 'tcp',
    status: 1,
    pingDuration: 30,
    maxTimeoutCount: 3,
  };
  formModalVisible.value = true;
};

// 显示编辑弹窗
const showEditModal = (record) => {
  isEdit.value = true;
  currentListener.value = { ...record };
  formModalVisible.value = true;
};

const showScriptInfoBoardModal = (record) => {
  currentListener.value = { ...record };
  scriptInfoBoardModalVisible.value = true;
}

// 处理表单提交成功
const handleFormSuccess = () => {
  formModalVisible.value = false;
  getListenerList();
};

// 处理删除
const handleDelete = async (id) => {
  try {
    loading.value = true;
    await listenerApi.deleteListener(id);
    message.success('删除成功');
    getListenerList();
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 处理状态变更
const handleStatusChange = async (record) => {
  try {
    loading.value = true;
    const newStatus = record.status === 1 ? 0 : 1;
    await listenerApi.updateListenerStatus({
      id: record.id,
      status: newStatus,
    });
    message.success(`${newStatus === 1 ? '启用' : '禁用'}成功`);
    getListenerList();
  } catch (error) {
    console.error('状态更新失败:', error);
    message.error('状态更新失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 监听active属性变化
watch(() => props.active, (newVal) => {
  if (newVal) {
    getListenerList();
  }
}, { immediate: true });

// 组件挂载时获取数据
onMounted(() => {
  if (props.active) {
    getListenerList();
  }
});
</script>

<style scoped>
.listener-content {
  padding: 16px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.action-buttons {
  margin-left: auto;
  margin-top: 8px;
}

@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
  }
  
  .action-buttons {
    margin-top: 16px;
    margin-left: 0;
  }
}
</style>