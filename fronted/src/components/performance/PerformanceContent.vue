<template>
  <div class="performance-monitor">
    <div class="header">
      <h2>性能监控</h2>
      <div class="header-actions">
        <a-button @click="refreshData" :loading="loading" type="primary" size="small">
          <ReloadOutlined />
          刷新数据
        </a-button>
        <a-button @click="resetFileTransferStatsHandler" type="primary" danger size="small">
          <DeleteOutlined />
          重置文件传输统计
        </a-button>
      </div>
    </div>

    <!-- 监听器性能统计 -->
    <a-card class="stats-card" :bordered="false">
      <template #title>
        <div class="card-header">
          <span>监听器性能统计</span>
          <a-tag :color="listenerStats.summary?.active_listeners > 0 ? 'success' : 'error'">
            活跃: {{ listenerStats.summary?.active_listeners || 0 }}/{{ listenerStats.summary?.total_listeners || 0 }}
          </a-tag>
        </div>
      </template>
      
      <a-row :gutter="20">
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ listenerStats.summary?.total_listeners || 0 }}</div>
            <div class="stat-label">总监听器数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ listenerStats.summary?.total_connections || 0 }}</div>
            <div class="stat-label">总连接数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatBytes(listenerStats.summary?.total_data_transferred) }}</div>
            <div class="stat-label">总数据传输</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ listenerStats.summary?.data_transferred_mb?.toFixed(2) || '0.00' }} MB</div>
            <div class="stat-label">传输量(MB)</div>
          </div>
        </a-col>
      </a-row>
      
      <!-- 按类型显示监听器统计 -->
      <a-divider orientation="left">按类型统计</a-divider>
      <a-row :gutter="16">
        <a-col :span="8" v-for="(listeners, type) in listenerStats.listeners_by_type" :key="type">
          <div class="listener-type-card">
            <div class="card-icon">
              <div class="icon-wrapper" :class="`icon-${type}`">
                <span class="icon-text">{{ type.charAt(0).toUpperCase() }}</span>
              </div>
            </div>
            <div class="card-content">
              <h4 class="card-title">{{ type.toUpperCase() }} 监听器</h4>
              <div class="stats-grid">
                <div class="stat-box">
                  <div class="stat-number">{{ listeners.length }}</div>
                  <div class="stat-label">总数</div>
                </div>
                <div class="stat-box">
                  <div class="stat-number active">{{ listeners.filter(l => l.Active || l.is_active).length }}</div>
                  <div class="stat-label">活跃</div>
                </div>
                <div class="stat-box">
                  <div class="stat-number">{{ listeners.reduce((sum, l) => sum + (l.TotalConnections || l.total_connections || 0), 0) }}</div>
                  <div class="stat-label">连接</div>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 文件传输统计 -->
    <a-card class="stats-card" :bordered="false">
      <template #title>
        <div class="card-header">
          <span>文件传输统计</span>
          <a-tag :color="getEfficiencyTagColor(fileTransferStats.efficiency_percentage)">
            效率: {{ fileTransferStats.efficiency_percentage?.toFixed(1) }}%
          </a-tag>
        </div>
      </template>
      
      <a-row :gutter="20">
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatBytes(fileTransferStats.total_bytes_transferred) }}</div>
            <div class="stat-label">总传输量</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ fileTransferStats.active_transfers || 0 }}</div>
            <div class="stat-label">活跃传输</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatBytes(fileTransferStats.memory_usage_bytes) }}</div>
            <div class="stat-label">内存使用</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ fileTransferStats.efficiency_ratio?.toFixed(3) || '0.000' }}</div>
            <div class="stat-label">效率比率</div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 客户端任务统计 -->
    <a-card class="stats-card" :bordered="false">
      <template #title>
        <span>客户端任务统计</span>
      </template>

      <a-row :gutter="16">
        <a-col :span="6">
          <div class="task-card file-transfer">
            <div class="task-icon">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
            </div>
            <div class="task-content">
              <h4>文件传输</h4>
              <div class="task-metrics">
                <div class="metric">
                  <span class="value">{{ clientTaskStats.task_stats?.file_transfer_tasks?.total_transfers || 0 }}</span>
                  <span class="label">总数</span>
                </div>
                <div class="metric active">
                  <span class="value">{{ clientTaskStats.task_stats?.file_transfer_tasks?.active_transfers || 0 }}</span>
                  <span class="label">活跃</span>
                </div>
                <div class="metric success">
                  <span class="value">{{ clientTaskStats.task_stats?.file_transfer_tasks?.completed_transfers || 0 }}</span>
                  <span class="label">完成</span>
                </div>
                <div class="metric error">
                  <span class="value">{{ clientTaskStats.task_stats?.file_transfer_tasks?.failed_transfers || 0 }}</span>
                  <span class="label">失败</span>
                </div>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="task-card screenshot">
            <div class="task-icon">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z"/>
              </svg>
            </div>
            <div class="task-content">
              <h4>截图任务</h4>
              <div class="task-metrics">
                <div class="metric">
                  <span class="value">{{ clientTaskStats.task_stats?.screenshot_tasks?.total_screenshots || 0 }}</span>
                  <span class="label">总数</span>
                </div>
                <div class="metric active">
                  <span class="value">{{ clientTaskStats.task_stats?.screenshot_tasks?.active_screenshots || 0 }}</span>
                  <span class="label">活跃</span>
                </div>
                <div class="metric success">
                  <span class="value">{{ clientTaskStats.task_stats?.screenshot_tasks?.completed_screenshots || 0 }}</span>
                  <span class="label">完成</span>
                </div>
                <div class="metric error">
                  <span class="value">{{ clientTaskStats.task_stats?.screenshot_tasks?.failed_screenshots || 0 }}</span>
                  <span class="label">失败</span>
                </div>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="task-card process">
            <div class="task-icon">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
              </svg>
            </div>
            <div class="task-content">
              <h4>进程任务</h4>
              <div class="task-metrics">
                <div class="metric">
                  <span class="value">{{ clientTaskStats.task_stats?.process_tasks?.total_process_tasks || 0 }}</span>
                  <span class="label">总数</span>
                </div>
                <div class="metric active">
                  <span class="value">{{ clientTaskStats.task_stats?.process_tasks?.active_process_tasks || 0 }}</span>
                  <span class="label">活跃</span>
                </div>
                <div class="metric success">
                  <span class="value">{{ clientTaskStats.task_stats?.process_tasks?.completed_process_tasks || 0 }}</span>
                  <span class="label">完成</span>
                </div>
                <div class="metric error">
                  <span class="value">{{ clientTaskStats.task_stats?.process_tasks?.failed_process_tasks || 0 }}</span>
                  <span class="label">失败</span>
                </div>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="task-card network">
            <div class="task-icon">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M17,3A2,2 0 0,1 19,5V15A2,2 0 0,1 17,17H13V19H14A1,1 0 0,1 15,20H22V22H15A1,1 0 0,1 14,23H10A1,1 0 0,1 9,22H2V20H9A1,1 0 0,1 10,19H11V17H7C5.89,17 5,16.1 5,15V5A2,2 0 0,1 7,3H17Z"/>
              </svg>
            </div>
            <div class="task-content">
              <h4>网络任务</h4>
              <div class="task-metrics">
                <div class="metric">
                  <span class="value">{{ clientTaskStats.task_stats?.network_tasks?.total_network_tasks || 0 }}</span>
                  <span class="label">总数</span>
                </div>
                <div class="metric active">
                  <span class="value">{{ clientTaskStats.task_stats?.network_tasks?.active_network_tasks || 0 }}</span>
                  <span class="label">活跃</span>
                </div>
                <div class="metric success">
                  <span class="value">{{ clientTaskStats.task_stats?.network_tasks?.completed_network_tasks || 0 }}</span>
                  <span class="label">完成</span>
                </div>
                <div class="metric error">
                  <span class="value">{{ clientTaskStats.task_stats?.network_tasks?.failed_network_tasks || 0 }}</span>
                  <span class="label">失败</span>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 内存池效率统计 -->
    <a-card class="pool-card" :bordered="false">
      <template #title>
        <span>内存池效率统计</span>
      </template>

      <a-row :gutter="16">
        <a-col :span="8" v-for="(pool, size) in poolStatus.chunk_pools" :key="size">
          <div class="pool-efficiency-card">
            <div class="pool-header">
              <div class="pool-icon">
                <svg viewBox="0 0 24 24" width="20" height="20">
                  <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4Z"/>
                </svg>
              </div>
              <h4 class="pool-title">{{ size.toUpperCase() }} 缓冲区</h4>
            </div>

            <div class="efficiency-circle">
              <a-progress
                type="circle"
                :percent="(pool.efficiency * 100)"
                :stroke-color="getProgressColor(pool.efficiency)"
                :width="80"
                :stroke-width="8"
              >
                <template #format="percent">
                  <span class="efficiency-text">{{ percent }}%</span>
                </template>
              </a-progress>
            </div>

            <div class="pool-metrics">
              <div class="pool-metric">
                <span class="metric-value">{{ pool.allocated }}</span>
                <span class="metric-label">已分配</span>
              </div>
              <div class="pool-metric">
                <span class="metric-value">{{ pool.reused }}</span>
                <span class="metric-label">已复用</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 任务执行统计 -->
    <a-card class="system-card" :bordered="false">
      <template #title>
        <span>任务执行统计</span>
      </template>

      <a-row :gutter="16">
        <a-col :span="8">
          <div class="execution-stat-card">
            <div class="stat-icon total">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ taskExecutionStats.execution_stats?.total_tasks_executed || 0 }}</div>
              <div class="stat-title">总执行任务数</div>
            </div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="execution-stat-card">
            <div class="stat-icon time">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ taskExecutionStats.execution_stats?.average_execution_time?.toFixed(2) || '0.00' }}s</div>
              <div class="stat-title">平均执行时间</div>
            </div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="execution-stat-card">
            <div class="stat-icon success">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ taskExecutionStats.execution_stats?.success_rate?.toFixed(1) || '0.0' }}%</div>
              <div class="stat-title">成功率</div>
            </div>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px;">
        <a-col :span="8">
          <div class="execution-stat-card">
            <div class="stat-icon error">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ taskExecutionStats.execution_stats?.failed_tasks || 0 }}</div>
              <div class="stat-title">失败任务数</div>
            </div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="execution-stat-card">
            <div class="stat-icon queue">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M2,6H22V8H2V6M2,10H22V12H2V10M2,14H22V16H2V14M2,18H22V20H2V18Z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ taskQueueStats.queue_stats?.queue_length || 0 }}</div>
              <div class="stat-title">队列长度</div>
            </div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="execution-stat-card">
            <div class="stat-icon rate">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M13,20H11V8L5.5,13.5L4.08,12.08L12,4.16L19.92,12.08L18.5,13.5L13,8V20Z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ taskQueueStats.queue_stats?.processing_rate?.toFixed(2) || '0.00' }}</div>
              <div class="stat-title">处理速率 (任务/秒)</div>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- Goroutine和工作池性能监控 -->
    <a-card class="stats-card" :bordered="false">
      <template #title>
        <div class="card-header">
          <span>Goroutine & 工作池监控</span>
          <a-tag :color="getHealthStatusColor(performanceOverview.health_status)">
            {{ performanceOverview.health_status || '未知' }}
          </a-tag>
        </div>
      </template>

      <a-row :gutter="20" style="margin-bottom: 20px;">
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ performanceOverview.num_goroutine || 0 }}</div>
            <div class="stat-label">当前Goroutine数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ performanceOverview.pool_summary?.total_workers || 0 }}</div>
            <div class="stat-label">工作池Worker数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatBytes(performanceOverview.memory_usage) }}</div>
            <div class="stat-label">内存使用量</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatUptime(performanceOverview.uptime) }}</div>
            <div class="stat-label">运行时间</div>
          </div>
        </a-col>
      </a-row>

      <!-- 工作池详细统计 -->
      <a-row :gutter="20">
        <a-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ performanceOverview.pool_summary?.total_tasks || 0 }}</div>
            <div class="stat-label">总任务数</div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ (performanceOverview.pool_summary?.success_rate * 100)?.toFixed(1) || '0.0' }}%</div>
            <div class="stat-label">任务成功率</div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ (performanceOverview.pool_summary?.max_queue_usage * 100)?.toFixed(1) || '0.0' }}%</div>
            <div class="stat-label">最大队列使用率</div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 泄漏检测器统计 -->
    <a-card class="stats-card" :bordered="false" v-if="leakDetectorStats.stats">
      <template #title>
        <div class="card-header">
          <span>Goroutine泄漏检测</span>
          <a-tag :color="leakDetectorStats.stats.leaks_detected > 0 ? 'error' : 'success'">
            {{ leakDetectorStats.stats.leaks_detected > 0 ? '检测到泄漏' : '正常' }}
          </a-tag>
        </div>
      </template>

      <a-row :gutter="20">
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ leakDetectorStats.stats.total_checks || 0 }}</div>
            <div class="stat-label">总检查次数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ leakDetectorStats.stats.leaks_detected || 0 }}</div>
            <div class="stat-label">检测到泄漏</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ leakDetectorStats.stats.max_goroutines || 0 }}</div>
            <div class="stat-label">最大Goroutine数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ leakDetectorStats.stats.average_goroutines?.toFixed(0) || '0' }}</div>
            <div class="stat-label">平均Goroutine数</div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 数据库连接池监控 -->
    <a-card class="stats-card" :bordered="false">
      <template #title>
        <div class="card-header">
          <span>数据库连接池监控</span>
          <a-tag :color="getDatabaseHealthColor(databaseHealth.health_status)">
            {{ databaseHealth.health_status || '未知' }}
          </a-tag>
        </div>
      </template>

      <!-- 连接池统计 -->
      <a-row :gutter="20" style="margin-bottom: 20px;">
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ databaseConnectionStats.connection_stats?.open_conns || 0 }}</div>
            <div class="stat-label">当前连接数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ databaseConnectionStats.connection_stats?.in_use_conns || 0 }}</div>
            <div class="stat-label">使用中连接</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ databaseConnectionStats.connection_stats?.idle_conns || 0 }}</div>
            <div class="stat-label">空闲连接</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ (databaseConnectionStats.connection_stats?.connection_utilization * 100)?.toFixed(1) || '0.0' }}%</div>
            <div class="stat-label">连接利用率</div>
          </div>
        </a-col>
      </a-row>

      <!-- 查询统计 -->
      <a-row :gutter="20">
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ databaseQueryStats.query_stats?.total_queries || 0 }}</div>
            <div class="stat-label">总查询数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ (databaseQueryStats.query_stats?.success_rate * 100)?.toFixed(1) || '0.0' }}%</div>
            <div class="stat-label">查询成功率</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ databaseQueryStats.query_stats?.slow_queries || 0 }}</div>
            <div class="stat-label">慢查询数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatDuration(databaseQueryStats.query_stats?.avg_query_time) }}</div>
            <div class="stat-label">平均查询时间</div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 缓存管理监控 -->
    <a-card class="stats-card" :bordered="false">
      <template #title>
        <div class="card-header">
          <span>缓存管理监控</span>
          <a-tag :color="getCacheHealthColor(cacheHealth.health_status)">
            {{ cacheHealth.health_status || '未知' }}
          </a-tag>
        </div>
      </template>

      <!-- 智能缓存统计 -->
      <a-row :gutter="20" style="margin-bottom: 20px;">
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ cacheStats.smart_cache_stats?.total_items || 0 }}</div>
            <div class="stat-label">总缓存项数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ (cacheStats.smart_cache_stats?.hit_rate * 100)?.toFixed(1) || '0.0' }}%</div>
            <div class="stat-label">缓存命中率</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatBytes(cacheStats.smart_cache_stats?.memory_usage) }}</div>
            <div class="stat-label">内存使用量</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ cacheStats.smart_cache_stats?.evict_count || 0 }}</div>
            <div class="stat-label">淘汰次数</div>
          </div>
        </a-col>
      </a-row>

      <!-- 缓存类型分布 -->
      <a-row :gutter="20" style="margin-bottom: 20px;">
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ cacheStats.smart_cache_stats?.task_items || 0 }}</div>
            <div class="stat-label">任务缓存</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ cacheStats.smart_cache_stats?.command_items || 0 }}</div>
            <div class="stat-label">命令缓存</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ cacheStats.smart_cache_stats?.stream_items || 0 }}</div>
            <div class="stat-label">流缓存</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ cacheStats.smart_cache_stats?.general_items || 0 }}</div>
            <div class="stat-label">通用缓存</div>
          </div>
        </a-col>
      </a-row>

      <!-- ResponseManager统计 -->
      <a-row :gutter="20">
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ cacheStats.response_manager_stats?.total_stores || 0 }}</div>
            <div class="stat-label">总存储次数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ (cacheStats.response_manager_stats?.hit_rate * 100)?.toFixed(1) || '0.0' }}%</div>
            <div class="stat-label">响应命中率</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ cacheStats.response_manager_stats?.wait_timeouts || 0 }}</div>
            <div class="stat-label">等待超时次数</div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ cacheStats.response_manager_stats?.command_stores || 0 }}</div>
            <div class="stat-label">命令存储次数</div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 缓存健康状态详情 -->
    <a-card class="stats-card" :bordered="false" v-if="cacheHealth.issues && cacheHealth.issues.length > 0">
      <template #title>
        <div class="card-header">
          <span>缓存健康问题</span>
          <a-tag color="warning">
            {{ cacheHealth.issues?.length || 0 }} 个问题
          </a-tag>
        </div>
      </template>

      <a-list :data-source="cacheHealth.issues" size="small">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-typography-text type="warning">
              <ExclamationCircleOutlined style="margin-right: 8px;" />
              {{ item }}
            </a-typography-text>
          </a-list-item>
        </template>
      </a-list>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import {
  getFileTransferStats,
  getFileTransferPoolStatus,
  resetFileTransferStats,
  getListenerPerformanceStats,
  getClientTaskStats,
  getTaskExecutionStats,
  getTaskQueueStats,
  getGoroutineStats,
  getWorkerPoolStats,
  getLeakDetectorStats,
  getSystemStats,
  getPerformanceOverview,
  getDatabaseStats,
  getDatabaseHealth,
  getDatabaseConnectionStats,
  getDatabaseQueryStats,
  getCacheStats,
  getCacheHealth
} from '@/api/performance'

// 响应式数据
const loading = ref(false)
const fileTransferStats = ref({})
const poolStatus = ref({
  chunk_pools: {},
  overall: {}
})
const listenerStats = ref({
  summary: {},
  listener_details: []
})
const clientTaskStats = ref({
  task_stats: {}
})
const taskExecutionStats = ref({
  execution_stats: {}
})
const taskQueueStats = ref({
  queue_stats: {}
})

// Goroutine和工作池相关数据
const goroutineStats = ref({})
const workerPoolStats = ref({})
const leakDetectorStats = ref({})
const systemStats = ref({})
const performanceOverview = ref({})

// 数据库连接池相关数据
const databaseStats = ref({})
const databaseHealth = ref({})
const databaseConnectionStats = ref({})
const databaseQueryStats = ref({})

// 缓存相关数据
const cacheStats = ref({})
const cacheHealth = ref({})

// 自动刷新定时器
let refreshTimer = null

// 获取文件传输统计
const fetchFileTransferStats = async () => {
  try {
    const response = await getFileTransferStats()
    if (response.code === 200) {
      fileTransferStats.value = response.data
    }
  } catch (error) {
    console.error('获取文件传输统计失败:', error)
  }
}

// 获取内存池状态
const fetchPoolStatus = async () => {
  try {
    const response = await getFileTransferPoolStatus()
    if (response.code === 200) {
      poolStatus.value = response.data
    }
  } catch (error) {
    console.error('获取内存池状态失败:', error)
  }
}

// 获取监听器性能统计
const fetchListenerStats = async () => {
  try {
    const response = await getListenerPerformanceStats()
    if (response.code === 200) {
      listenerStats.value = response.data
    }
  } catch (error) {
    console.error('获取监听器统计失败:', error)
  }
}

// 获取客户端任务统计
const fetchClientTaskStats = async () => {
  try {
    const response = await getClientTaskStats()
    if (response.code === 200) {
      clientTaskStats.value = response.data
    }
  } catch (error) {
    console.error('获取客户端任务统计失败:', error)
  }
}

// 获取任务执行统计
const fetchTaskExecutionStats = async () => {
  try {
    const response = await getTaskExecutionStats()
    if (response.code === 200) {
      taskExecutionStats.value = response.data
    }
  } catch (error) {
    console.error('获取任务执行统计失败:', error)
  }
}

// 获取任务队列统计
const fetchTaskQueueStats = async () => {
  try {
    const response = await getTaskQueueStats()
    if (response.code === 200) {
      taskQueueStats.value = response.data
    }
  } catch (error) {
    console.error('获取任务队列统计失败:', error)
  }
}

// 获取Goroutine统计
const fetchGoroutineStats = async () => {
  try {
    const response = await getGoroutineStats()
    if (response.code === 200) {
      goroutineStats.value = response.data
    }
  } catch (error) {
    console.error('获取Goroutine统计失败:', error)
  }
}

// 获取工作池统计
const fetchWorkerPoolStats = async () => {
  try {
    const response = await getWorkerPoolStats()
    if (response.code === 200) {
      workerPoolStats.value = response.data
    }
  } catch (error) {
    console.error('获取工作池统计失败:', error)
  }
}

// 获取泄漏检测器统计
const fetchLeakDetectorStats = async () => {
  try {
    const response = await getLeakDetectorStats()
    if (response.code === 200) {
      leakDetectorStats.value = response.data
    }
  } catch (error) {
    console.error('获取泄漏检测器统计失败:', error)
  }
}

// 获取系统统计
const fetchSystemStats = async () => {
  try {
    const response = await getSystemStats()
    if (response.code === 200) {
      systemStats.value = response.data
    }
  } catch (error) {
    console.error('获取系统统计失败:', error)
  }
}

// 获取性能概览
const fetchPerformanceOverview = async () => {
  try {
    const response = await getPerformanceOverview()
    console.log('性能概览API响应:', response) // 添加调试日志
    if (response.code === 200) {
      performanceOverview.value = response.data
      console.log('性能概览数据:', performanceOverview.value) // 添加调试日志
    } else {
      console.error('API返回错误:', response)
    }
  } catch (error) {
    console.error('获取性能概览失败:', error)
  }
}

// 获取数据库统计
const fetchDatabaseStats = async () => {
  try {
    const response = await getDatabaseStats()
    if (response.code === 200) {
      databaseStats.value = response.data
    }
  } catch (error) {
    console.error('获取数据库统计失败:', error)
  }
}

// 获取数据库健康状态
const fetchDatabaseHealth = async () => {
  try {
    const response = await getDatabaseHealth()
    if (response.code === 200) {
      databaseHealth.value = response.data
    }
  } catch (error) {
    console.error('获取数据库健康状态失败:', error)
  }
}

// 获取数据库连接统计
const fetchDatabaseConnectionStats = async () => {
  try {
    const response = await getDatabaseConnectionStats()
    if (response.code === 200) {
      databaseConnectionStats.value = response.data
    }
  } catch (error) {
    console.error('获取数据库连接统计失败:', error)
  }
}

// 获取数据库查询统计
const fetchDatabaseQueryStats = async () => {
  try {
    const response = await getDatabaseQueryStats()
    if (response.code === 200) {
      databaseQueryStats.value = response.data
    }
  } catch (error) {
    console.error('获取数据库查询统计失败:', error)
  }
}

// 获取缓存统计
const fetchCacheStats = async () => {
  try {
    const response = await getCacheStats()
    if (response.code === 200) {
      cacheStats.value = response.data
    }
  } catch (error) {
    console.error('获取缓存统计失败:', error)
  }
}

// 获取缓存健康状态
const fetchCacheHealth = async () => {
  try {
    const response = await getCacheHealth()
    if (response.code === 200) {
      cacheHealth.value = response.data
    }
  } catch (error) {
    console.error('获取缓存健康状态失败:', error)
  }
}

// 刷新所有数据
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      fetchFileTransferStats(),
      fetchPoolStatus(),
      fetchListenerStats(),
      fetchClientTaskStats(),
      fetchTaskExecutionStats(),
      fetchTaskQueueStats(),
      fetchGoroutineStats(),
      fetchWorkerPoolStats(),
      fetchLeakDetectorStats(),
      fetchSystemStats(),
      fetchPerformanceOverview(),
      fetchDatabaseStats(),
      fetchDatabaseHealth(),
      fetchDatabaseConnectionStats(),
      fetchDatabaseQueryStats(),
      fetchCacheStats(),
      fetchCacheHealth()
    ])
  } finally {
    loading.value = false
  }
}

// 重置文件传输统计
const resetFileTransferStatsHandler = async () => {
  try {
    const response = await resetFileTransferStats()
    if (response.code === 200) {
      message.success('文件传输统计信息已重置')
      await refreshData()
    } else {
      message.error('重置失败: ' + response.msg)
    }
  } catch (error) {
    message.error('重置失败: ' + error.message)
  }
}

// 格式化字节数
const formatBytes = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化运行时间
const formatUptime = (nanoseconds) => {
  if (!nanoseconds) return '0s'

  const seconds = Math.floor(nanoseconds / **********)
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  if (days > 0) {
    return `${days}天 ${hours}小时 ${minutes}分钟`
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟 ${remainingSeconds}秒`
  } else {
    return `${remainingSeconds}秒`
  }
}

// 获取健康状态颜色
const getHealthStatusColor = (status) => {
  switch (status) {
    case '健康':
      return 'success'
    case '警告':
      return 'warning'
    case '危险':
      return 'error'
    default:
      return 'default'
  }
}

// 获取数据库健康状态颜色
const getDatabaseHealthColor = (status) => {
  switch (status) {
    case '健康':
      return 'success'
    case '警告':
      return 'warning'
    case '危险':
      return 'error'
    case '未初始化':
      return 'default'
    default:
      return 'default'
  }
}

// 获取缓存健康状态颜色
const getCacheHealthColor = (status) => {
  switch (status) {
    case '健康':
      return 'success'
    case '警告':
      return 'warning'
    case '错误':
      return 'error'
    case '未初始化':
      return 'default'
    default:
      return 'default'
  }
}

// 格式化持续时间
const formatDuration = (duration) => {
  if (!duration) return '0ms'

  // duration 可能是纳秒数或者已经是字符串
  if (typeof duration === 'string') {
    return duration
  }

  // 如果是数字，假设是纳秒
  const ms = duration / 1000000
  if (ms < 1) {
    return `${(duration / 1000).toFixed(0)}μs`
  } else if (ms < 1000) {
    return `${ms.toFixed(1)}ms`
  } else {
    return `${(ms / 1000).toFixed(2)}s`
  }
}



// 获取效率标签颜色
const getEfficiencyTagColor = (efficiency) => {
  if (efficiency >= 80) return 'success'
  if (efficiency >= 60) return 'warning'
  return 'error'
}

// 获取进度条颜色
const getProgressColor = (efficiency) => {
  if (efficiency >= 0.8) return '#67c23a'
  if (efficiency >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

// 组件挂载
onMounted(() => {
  refreshData()
  // 每30秒自动刷新
  refreshTimer = setInterval(refreshData, 30000)
})

// 组件卸载
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.performance-monitor {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-card, .pool-card, .system-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.stats-card:hover, .pool-card:hover, .system-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #262626;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 500;
}

/* 内存池效率卡片样式 */
.pool-efficiency-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.pool-efficiency-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.pool-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  gap: 8px;
}

.pool-icon {
  color: #1890ff;
}

.pool-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: #262626;
}

.efficiency-circle {
  margin: 16px 0;
  display: flex;
  justify-content: center;
}

.efficiency-text {
  font-size: 14px;
  font-weight: bold;
  color: #262626;
}

.pool-metrics {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
}

.pool-metric {
  text-align: center;
}

.metric-value {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #8c8c8c;
}

.task-type-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background: #fafafa;
}

.task-type-header {
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
  text-align: center;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.task-stats {
  font-size: 12px;
  color: #606266;
}

.task-stats div {
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
}

/* 监听器类型卡片样式 */
.listener-type-card {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.listener-type-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.card-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.icon-wrapper {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 2px solid #e8e8e8;
}

.icon-wrapper.icon-tcp {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.icon-wrapper.icon-pipe {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.icon-text {
  font-size: 20px;
  font-weight: bold;
}

.card-content {
  text-align: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #262626;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
  gap: 8px;
}

.stat-box {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
  color: #262626;
}

.stat-number.active {
  color: #52c41a;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

/* 任务卡片样式 */
.task-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-left: 4px solid;
  margin-bottom: 16px;
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.task-card.file-transfer {
  border-left-color: #1890ff;
}

.task-card.screenshot {
  border-left-color: #52c41a;
}

.task-card.process {
  border-left-color: #faad14;
}

.task-card.network {
  border-left-color: #722ed1;
}

.task-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-bottom: 12px;
}

.file-transfer .task-icon {
  background: #f0f5ff;
  color: #1890ff;
}

.screenshot .task-icon {
  background: #f6ffed;
  color: #52c41a;
}

.process .task-icon {
  background: #fff7e6;
  color: #faad14;
}

.network .task-icon {
  background: #f9f0ff;
  color: #722ed1;
}

.task-content h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #262626;
}

.task-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.metric {
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  background: #fafafa;
}

.metric .value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 2px;
}

.metric .label {
  font-size: 12px;
  color: #8c8c8c;
}

.metric.active .value {
  color: #1890ff;
}

.metric.success .value {
  color: #52c41a;
}

.metric.error .value {
  color: #ff4d4f;
}

/* 任务执行统计卡片样式 */
.execution-stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.execution-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon.total {
  background: #f0f5ff;
  color: #1890ff;
}

.stat-icon.time {
  background: #fff7e6;
  color: #fa8c16;
}

.stat-icon.success {
  background: #f6ffed;
  color: #52c41a;
}

.stat-icon.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.stat-icon.queue {
  background: #f9f0ff;
  color: #722ed1;
}

.stat-icon.rate {
  background: #e6fffb;
  color: #13c2c2;
}

.stat-content {
  flex: 1;
}

.stat-content .stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stat-content .stat-title {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}
</style>
