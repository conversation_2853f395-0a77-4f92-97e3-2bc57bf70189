<template>
  <div class="settings-container">
    <a-card class="settings-card">
      <template #title>
        <div class="card-title">
          <SettingOutlined />
          <span>系统设置</span>
        </div>
      </template>
      
      <a-tabs v-model:activeKey="activeTab">
        <!-- 常规设置 -->
        <a-tab-pane key="general" tab="常规设置">
          <div class="settings-content">
            <a-form
              ref="generalFormRef"
              :model="generalForm"
              :rules="generalRules"
              layout="vertical"
              @finish="handleGeneralSave"
            >
              <!-- 基础服务配置 -->
              <div class="config-section">
                <div class="section-header">
                  <h3>基础服务配置</h3>
                  <p>配置服务器基本运行参数</p>
                </div>
                <a-row :gutter="[24, 16]">
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="服务器端口" name="port">
                      <a-input-number
                        v-model:value="generalForm.server.port"
                        :min="1"
                        :max="65535"
                        style="width: 100%"
                        placeholder="8888"
                      />
                      <div class="field-description">Web服务监听端口</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="服务器版本" name="version">
                      <a-input
                        v-model:value="generalForm.server.version"
                        placeholder="1.0.0"
                        disabled
                      />
                      <div class="field-description">当前服务器版本号</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="路由前缀" name="routerPrefix">
                      <a-input
                        v-model:value="generalForm.server.routerPrefix"
                        placeholder="/api"
                      />
                      <div class="field-description">API路由前缀</div>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>

              <!-- 安全限制配置 -->
              <div class="config-section">
                <div class="section-header">
                  <h3>安全限制配置</h3>
                  <p>配置访问频率限制和安全策略</p>
                </div>
                <a-row :gutter="[24, 16]">
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="IP限制次数" name="limitCountIP">
                      <a-input-number
                        v-model:value="generalForm.server.limitCountIP"
                        :min="1"
                        style="width: 100%"
                        placeholder="15000"
                      />
                      <div class="field-description">单个IP最大请求次数</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="IP限制时间" name="limitTimeIP">
                      <a-select
                        v-model:value="generalForm.server.limitTimeIP"
                        style="width: 100%"
                        placeholder="选择限制时间"
                      >
                        <a-select-option :value="60">1分钟</a-select-option>
                        <a-select-option :value="300">5分钟</a-select-option>
                        <a-select-option :value="600">10分钟</a-select-option>
                        <a-select-option :value="1800">30分钟</a-select-option>
                        <a-select-option :value="3600">1小时</a-select-option>
                        <a-select-option :value="7200">2小时</a-select-option>
                        <a-select-option :value="86400">24小时</a-select-option>
                      </a-select>
                      <div class="field-description">IP限制的时间窗口</div>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>

              <!-- 代理服务配置 -->
              <div class="config-section">
                <div class="section-header">
                  <h3>代理服务配置</h3>
                  <p>配置代理服务可分配的端口范围</p>
                </div>
                <a-row :gutter="[24, 16]">
                  <a-col :xs="24" :sm="12">
                    <a-form-item label="代理端口起始范围" name="serverStartPort">
                      <a-input-number
                        v-model:value="generalForm.server.serverStartPort"
                        :min="1024"
                        :max="65535"
                        style="width: 100%"
                        placeholder="20000"
                      />
                      <div class="field-description">代理服务可分配的最小端口号</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12">
                    <a-form-item label="代理端口结束范围" name="serverEndPort">
                      <a-input-number
                        v-model:value="generalForm.server.serverEndPort"
                        :min="1024"
                        :max="65535"
                        style="width: 100%"
                        placeholder="65535"
                      />
                      <div class="field-description">代理服务可分配的最大端口号</div>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>

              <!-- 文件存储配置 -->
              <div class="config-section">
                <div class="section-header">
                  <h3>文件存储配置</h3>
                  <p>配置文件上传下载存储路径</p>
                </div>
                <a-row :gutter="[24, 16]">
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="客户端程序目录" name="clientBinDir">
                      <a-input
                        v-model:value="generalForm.server.clientBinDir"
                        placeholder="./clientbin"
                      />
                      <div class="field-description">客户端程序存储目录</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="上传文件目录" name="uploadDir">
                      <a-input
                        v-model:value="generalForm.server.uploadDir"
                        placeholder="./upload"
                      />
                      <div class="field-description">文件上传存储目录</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="下载文件目录" name="downloadDir">
                      <a-input
                        v-model:value="generalForm.server.downloadDir"
                        placeholder="./download"
                      />
                      <div class="field-description">文件下载存储目录</div>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
              
              <!-- JWT认证配置 -->
              <div class="config-section">
                <div class="section-header">
                  <h3>JWT认证配置</h3>
                  <p>配置用户认证令牌相关参数</p>
                </div>
                <a-row :gutter="[24, 16]">
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="令牌过期时间" name="expiresTime">
                      <a-select
                        v-model:value="generalForm.jwt.expiresTime"
                        style="width: 100%"
                        placeholder="选择过期时间"
                      >
                        <a-select-option value="1h">1小时</a-select-option>
                        <a-select-option value="6h">6小时</a-select-option>
                        <a-select-option value="12h">12小时</a-select-option>
                        <a-select-option value="1d">1天</a-select-option>
                        <a-select-option value="3d">3天</a-select-option>
                        <a-select-option value="7d">7天</a-select-option>
                        <a-select-option value="30d">30天</a-select-option>
                      </a-select>
                      <div class="field-description">JWT令牌有效期</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="令牌缓冲时间" name="bufferTime">
                      <a-select
                        v-model:value="generalForm.jwt.bufferTime"
                        style="width: 100%"
                        placeholder="选择缓冲时间"
                      >
                        <a-select-option value="10m">10分钟</a-select-option>
                        <a-select-option value="30m">30分钟</a-select-option>
                        <a-select-option value="1h">1小时</a-select-option>
                        <a-select-option value="6h">6小时</a-select-option>
                        <a-select-option value="12h">12小时</a-select-option>
                        <a-select-option value="1d">1天</a-select-option>
                      </a-select>
                      <div class="field-description">令牌自动刷新缓冲时间</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="签名密钥" name="signingKey">
                      <a-input-password
                        v-model:value="generalForm.jwt.signingKey"
                        placeholder="请输入签名密钥"
                      />
                      <div class="field-description">JWT签名加密密钥</div>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
              
              <!-- 验证码配置 -->
              <div class="config-section">
                <div class="section-header">
                  <h3>验证码配置</h3>
                  <p>配置登录验证码相关参数</p>
                </div>
                <a-row :gutter="[24, 16]">
                  <a-col :xs="24" :sm="12" :md="6">
                    <a-form-item label="启用验证码" name="openCaptcha">
                      <a-switch
                        v-model:checked="captchaEnabled"
                        checked-children="开启"
                        un-checked-children="关闭"
                      />
                      <div class="field-description">登录时是否需要验证码</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="6">
                    <a-form-item label="验证码长度" name="keyLong">
                      <a-select
                        v-model:value="generalForm.captcha.keyLong"
                        style="width: 100%"
                        placeholder="选择长度"
                      >
                        <a-select-option :value="4">4位</a-select-option>
                        <a-select-option :value="5">5位</a-select-option>
                        <a-select-option :value="6">6位</a-select-option>
                        <a-select-option :value="7">7位</a-select-option>
                        <a-select-option :value="8">8位</a-select-option>
                      </a-select>
                      <div class="field-description">验证码字符数量</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="6">
                    <a-form-item label="图片宽度" name="imgWidth">
                      <a-input-number
                        v-model:value="generalForm.captcha.imgWidth"
                        :min="100"
                        :max="500"
                        style="width: 100%"
                        placeholder="240"
                      />
                      <div class="field-description">验证码图片宽度(px)</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="6">
                    <a-form-item label="图片高度" name="imgHeight">
                      <a-input-number
                        v-model:value="generalForm.captcha.imgHeight"
                        :min="30"
                        :max="200"
                        style="width: 100%"
                        placeholder="80"
                      />
                      <div class="field-description">验证码图片高度(px)</div>
                    </a-form-item>
                  </a-col>

                  <a-col :xs="24" :sm="12" :md="6">
                    <a-form-item label="最大失败次数" name="maxFailedAttempts">
                      <a-input-number
                        v-model:value="generalForm.captcha.maxFailedAttempts"
                        :min="1"
                        :max="100"
                        style="width: 100%"
                        placeholder="5"
                      />
                      <div class="field-description">登录失败多少次后暂时禁止登录</div>
                    </a-form-item>
                  </a-col>

                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="验证码超时时间" name="openCaptchaTimeOut">
                      <a-select
                        v-model:value="generalForm.captcha.openCaptchaTimeOut"
                        style="width: 100%"
                        placeholder="选择超时时间"
                      >
                        <a-select-option :value="60">1分钟</a-select-option>
                        <a-select-option :value="300">5分钟</a-select-option>
                        <a-select-option :value="600">10分钟</a-select-option>
                        <a-select-option :value="1800">30分钟</a-select-option>
                        <a-select-option :value="3600">1小时</a-select-option>
                      </a-select>
                      <div class="field-description">验证码有效时间</div>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>

              <!-- 数据库配置 -->
              <div class="config-section">
                <div class="section-header">
                  <h3>数据库配置</h3>
                  <p>配置SQLite数据库相关参数</p>
                </div>
                <a-row :gutter="[24, 16]">
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="数据库名称" name="dbName">
                      <a-input
                        v-model:value="generalForm.sqlite.dbName"
                        placeholder="ezdata"
                      />
                      <div class="field-description">SQLite数据库文件名</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="数据库路径" name="dbPath">
                      <a-input
                        v-model:value="generalForm.sqlite.path"
                        placeholder="./"
                      />
                      <div class="field-description">数据库文件存储路径</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="最大连接数" name="maxOpenConns">
                      <a-input-number
                        v-model:value="generalForm.sqlite.maxOpenConns"
                        :min="1"
                        :max="1000"
                        style="width: 100%"
                        placeholder="100"
                      />
                      <div class="field-description">数据库最大连接数</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="空闲连接数" name="maxIdleConns">
                      <a-input-number
                        v-model:value="generalForm.sqlite.maxIdleConns"
                        :min="1"
                        :max="100"
                        style="width: 100%"
                        placeholder="10"
                      />
                      <div class="field-description">数据库空闲连接数</div>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>

              <!-- 日志配置 -->
              <div class="config-section">
                <div class="section-header">
                  <h3>日志配置</h3>
                  <p>配置系统日志记录相关参数</p>
                </div>
                <a-row :gutter="[24, 16]">
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="日志级别" name="logLevel">
                      <a-select
                        v-model:value="generalForm.zap.level"
                        style="width: 100%"
                        placeholder="选择日志级别"
                      >
                        <a-select-option value="debug">Debug</a-select-option>
                        <a-select-option value="info">Info</a-select-option>
                        <a-select-option value="warn">Warn</a-select-option>
                        <a-select-option value="error">Error</a-select-option>
                      </a-select>
                      <div class="field-description">系统日志记录级别</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="日志目录" name="logDirector">
                      <a-input
                        v-model:value="generalForm.zap.director"
                        placeholder="log"
                      />
                      <div class="field-description">日志文件存储目录</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="日志保留天数" name="retentionDay">
                      <a-input-number
                        v-model:value="generalForm.zap.retentionDay"
                        :min="-1"
                        :max="365"
                        style="width: 100%"
                        placeholder="-1"
                      />
                      <div class="field-description">日志文件保留天数(-1为永久)</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="6">
                    <a-form-item label="控制台输出" name="logInConsole">
                      <a-switch
                        v-model:checked="generalForm.zap.logInConsole"
                        checked-children="开启"
                        un-checked-children="关闭"
                      />
                      <div class="field-description">是否在控制台输出日志</div>
                    </a-form-item>
                  </a-col>
                  
                  <a-col :xs="24" :sm="12" :md="6">
                    <a-form-item label="显示行号" name="showLine">
                      <a-switch
                        v-model:checked="generalForm.zap.showLine"
                        checked-children="开启"
                        un-checked-children="关闭"
                      />
                      <div class="field-description">日志是否显示代码行号</div>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>

              <a-divider />
              
              <div class="form-actions">
                <a-space>
                  <a-button type="primary" html-type="submit" :loading="generalSaving">
                    保存设置
                  </a-button>
                  <a-button @click="handleGeneralReset" :loading="resetting">
                    重置默认
                  </a-button>
                </a-space>
              </div>
            </a-form>
          </div>
        </a-tab-pane>
        
        <!-- 通知设置 -->
        <a-tab-pane key="notification" tab="通知设置">
          <a-form
            ref="notificationFormRef"
            :model="notificationForm"
            :rules="notificationRules"
            layout="vertical"
            @finish="handleNotificationSave"
          >
            <a-row :gutter="24">
              <a-col :span="24">
                <a-divider orientation="left">客户端状态通知</a-divider>
              </a-col>
              
              <a-col :span="12">
                <a-form-item label="启用客户端状态通知" name="clientStatus">
                  <a-switch
                    v-model:checked="notificationForm.clientStatus"
                    checked-children="开启"
                    un-checked-children="关闭"
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <a-form-item label="通知显示时长(秒)" name="displayDuration">
                  <a-input-number
                    v-model:value="notificationForm.displayDuration"
                    :min="1"
                    :max="60"
                    style="width: 100%"
                    placeholder="请输入显示时长"
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <a-form-item label="启用通知音效" name="soundEnabled">
                  <a-switch
                    v-model:checked="notificationForm.soundEnabled"
                    checked-children="开启"
                    un-checked-children="关闭"
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <a-form-item label="最大通知数量" name="maxNotifications">
                  <a-input-number
                    v-model:value="notificationForm.maxNotifications"
                    :min="1"
                    :max="10"
                    style="width: 100%"
                    placeholder="请输入最大通知数量"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-divider />
            
            <div class="form-actions">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="notificationSaving">
                  保存设置
                </a-button>
              </a-space>
            </div>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Modal } from 'ant-design-vue'
import { 
  SettingOutlined, 
  NotificationOutlined 
} from '@ant-design/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import notificationManager from '@/utils/notificationManager.js'

// 接收属性
const props = defineProps({
  active: {
    type: Boolean,
    default: false
  }
})

// 使用全局设置状态管理
const settingsStore = useSettingsStore()
const {
  loading,
  generalSaving,
  notificationSaving,
  resetting,
  generalForm,
  notificationForm,
  loadSettings,
  saveGeneralSettings,
  saveNotificationSettings,
  resetSettings
} = settingsStore

// 响应式数据
const activeTab = ref('general')
const generalFormRef = ref()
const notificationFormRef = ref()

// 验证码开关计算属性
const captchaEnabled = computed({
  get: () => {
    // 现在使用bool类型，直接返回openCaptcha的值
    return generalForm.captcha.openCaptcha
  },
  set: (value) => {
    generalForm.captcha.openCaptcha = value
  }
})

// 表单验证规则 - 简化验证，避免与嵌套数据结构冲突
const generalRules = {
  // 只保留关键字段的验证，其他由后端处理
}

const notificationRules = {
  displayDuration: [
    { required: true, message: '请输入通知显示时长' },
    { type: 'number', min: 1, max: 60, message: '显示时长必须在1-60秒之间' }
  ],
  maxNotifications: [
    { required: true, message: '请输入最大通知数量' },
    { type: 'number', min: 1, max: 10, message: '最大通知数量必须在1-10之间' }
  ]
}

// 初始化通知管理器
const initNotificationManager = async () => {
  try {
    // 加载通知配置并初始化通知管理器
    await loadSettings()
    
    // 初始化通知管理器，使用毫秒值
    notificationManager.init({
      clientStatus: notificationForm.clientStatus,
      displayDuration: notificationForm.displayDuration * 1000, // 转换为毫秒
      soundEnabled: notificationForm.soundEnabled,
      maxNotifications: notificationForm.maxNotifications
    })
  } catch (error) {
    console.error('初始化通知管理器失败:', error)
  }
}

// 保存常规设置
const handleGeneralSave = async () => {
  const success = await saveGeneralSettings()
  if (success) {
    // 可以在这里添加额外的成功处理逻辑
  }
}

// 保存通知设置
const handleNotificationSave = async () => {
  const result = await saveNotificationSettings()
  if (result.success) {
    // 更新通知管理器设置，实现实时生效
    notificationManager.updateSettings(result.data)
  }
}

// 重置配置
const handleGeneralReset = () => {
  Modal.confirm({
    title: '确认重置',
    content: '确定要将所有配置重置为默认值吗？此操作不可撤销。',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await resetSettings()
    }
  })
}

// 监听active属性变化，当组件激活时加载数据
watch(() => props.active, (newVal) => {
  if (newVal && !loading.value) {
    initNotificationManager()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (props.active && !loading.value) {
    initNotificationManager()
  }
})
</script>

<style scoped lang="scss">
.settings-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 120px);
}

.settings-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.settings-content {
  max-width: 1200px;
  margin: 0 auto;
}

.config-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.section-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;

  h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: #1890ff;
      margin-right: 8px;
      border-radius: 2px;
    }
  }

  p {
    margin: 0;
    font-size: 13px;
    color: #8c8c8c;
    line-height: 1.4;
  }
}

.field-description {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.3;
}

.form-actions {
  display: flex;
  justify-content: center;
  padding: 24px 0;
  background: #fff;
  border-radius: 8px;
  margin-top: 24px;
  border: 1px solid #e8e8e8;
}

:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 4px;
  
  label {
    font-weight: 500;
    color: #262626;
    font-size: 14px;
  }
}

:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-input),
:deep(.ant-input-number),
:deep(.ant-select-selector) {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;

  &:hover {
    border-color: #40a9ff;
  }

  &:focus,
  &.ant-input-focused,
  &.ant-select-focused .ant-select-selector {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

:deep(.ant-switch) {
  &.ant-switch-checked {
    background-color: #52c41a;
  }
}

:deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
  height: 36px;
  padding: 0 20px;
  
  &.ant-btn-primary {
    background: #1890ff;
    border-color: #1890ff;
    
    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settings-container {
    padding: 12px;
  }
  
  .config-section {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .section-header h3 {
    font-size: 14px;
  }
}
</style>