<template>
  <!-- 右键菜单 -->
  <div 
    v-if="visible" 
    class="context-menu"
    :style="{
      position: 'fixed',
      left: adjustedPosition.x + 'px',
      top: adjustedPosition.y + 'px',
      zIndex: 1000
    }"
    @click.stop
  >
    <div class="menu-container" ref="menuContainerRef">
      <div class="menu-item" @click="() => handleMenuClick({ key: 'copy' })">
        <div class="menu-icon">
          <CopyOutlined />
        </div>
        <span class="menu-text">复制</span>
        <div class="menu-shortcut">Ctrl+C</div>
      </div>
      
      <div class="menu-item" @click="() => handleMenuClick({ key: 'cut' })">
        <div class="menu-icon">
          <ScissorOutlined />
        </div>
        <span class="menu-text">剪切</span>
        <div class="menu-shortcut">Ctrl+X</div>
      </div>
      
      <div 
        class="menu-item" 
        :class="{ disabled: !clipboard.files.length }"
        @click="clipboard.files.length ? handleMenuClick({ key: 'paste' }) : null"
      >
        <div class="menu-icon">
          <CopyOutlined />
        </div>
        <span class="menu-text">粘贴</span>
        <div class="menu-badge" v-if="clipboard.files.length">{{ clipboard.files.length }}</div>
        <div class="menu-shortcut">Ctrl+V</div>
      </div>
      
      <div class="menu-divider"></div>
      
      <div 
        class="menu-item" 
        :class="{ disabled: targetFile?.type === 'directory' }"
        @click="targetFile?.type !== 'directory' ? handleMenuClick({ key: 'edit' }) : null"
      >
        <div class="menu-icon">
          <EditOutlined />
        </div>
        <span class="menu-text">编辑</span>
      </div>
      
      <div class="menu-item" @click="() => handleMenuClick({ key: 'rename' })">
        <div class="menu-icon">
          <FormOutlined />
        </div>
        <span class="menu-text">重命名</span>
        <div class="menu-shortcut">F2</div>
      </div>
      
      <div class="menu-divider"></div>
      
      <div class="menu-item" @click="() => handleMenuClick({ key: 'properties' })">
        <div class="menu-icon">
          <InfoCircleOutlined />
        </div>
        <span class="menu-text">属性</span>
      </div>
      
      <div class="menu-divider"></div>
      
      <div class="menu-item danger" @click="() => handleMenuClick({ key: 'delete' })">
        <div class="menu-icon">
          <DeleteOutlined />
        </div>
        <span class="menu-text">删除</span>
        <div class="menu-shortcut">Del</div>
      </div>
    </div>
  </div>

  <!-- 重命名弹窗 -->
  <a-modal
    v-model:open="renameModalVisible"
    title="重命名"
    @ok="confirmRename"
    @cancel="cancelRename"
  >
    <a-form>
      <a-form-item label="新名称">
        <a-input 
          v-model:value="newFileName" 
          @keyup.enter="confirmRename"
          ref="renameInput"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, watch, computed, nextTick } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { fileApi, dirApi } from '@/api';
import { buildPath } from './fileUtils.js';
import {
  CopyOutlined,
  ScissorOutlined,
  DeleteOutlined,
  FormOutlined,
  EditOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue';

// 接收属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
  targetFile: {
    type: Object,
    default: null
  },
  clipboard: {
    type: Object,
    default: () => ({ files: [], operation: null })
  },
  clientId: {
    type: [String, Number],
    required: true
  },
  currentPath: {
    type: String,
    required: true
  }
});

// 监听 clipboard prop 变化
watch(() => props.clipboard, (newClipboard) => {
  // 可以在这里处理剪贴板状态变化
}, { deep: true });

// 菜单尺寸常量
const MENU_WIDTH = 200;
const MENU_HEIGHT = 280; // 估算菜单高度
const MARGIN = 10; // 距离屏幕边缘的最小距离

// 智能调整菜单位置
const adjustedPosition = computed(() => {
  if (!props.visible) {
    return props.position;
  }

  const { x, y } = props.position;
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;
  const menuWidth = actualMenuSize.value.width;
  const menuHeight = actualMenuSize.value.height;
  
  let adjustedX = x;
  let adjustedY = y;
  
  // 智能位置调整策略
  
  // 1. 检查右边界 - 如果菜单会超出右边界，则显示在鼠标左侧
  if (x + menuWidth > windowWidth - MARGIN) {
    adjustedX = Math.max(MARGIN, x - menuWidth);
  }
  
  // 2. 检查下边界 - 如果菜单会超出下边界，则显示在鼠标上方
  if (y + menuHeight > windowHeight - MARGIN) {
    adjustedY = Math.max(MARGIN, y - menuHeight);
  }
  
  // 3. 最终边界检查，确保菜单完全在屏幕内
  if (adjustedX < MARGIN) {
    adjustedX = MARGIN;
  }
  if (adjustedX + menuWidth > windowWidth - MARGIN) {
    adjustedX = windowWidth - menuWidth - MARGIN;
  }
  
  if (adjustedY < MARGIN) {
    adjustedY = MARGIN;
  }
  if (adjustedY + menuHeight > windowHeight - MARGIN) {
    adjustedY = windowHeight - menuHeight - MARGIN;
  }
  
  return {
    x: Math.max(0, adjustedX),
    y: Math.max(0, adjustedY)
  };
});

// 更新菜单实际尺寸
const updateMenuSize = () => {
  if (menuContainerRef.value) {
    const rect = menuContainerRef.value.getBoundingClientRect();
    actualMenuSize.value = {
      width: rect.width || MENU_WIDTH,
      height: rect.height || MENU_HEIGHT
    };
  }
};

// 监听菜单显示状态，更新尺寸
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      updateMenuSize();
    });
  }
});

// 定义事件
const emit = defineEmits([
  'update:visible',
  'update:clipboard',
  'paste-files',
  'rename-file',
  'delete-file',
  'edit-file',
  'refresh-list',
  'show-properties'
]);

// 重命名相关状态
const renameModalVisible = ref(false);
const newFileName = ref('');
const currentRenameFile = ref(null);
const renameInput = ref(null);

// 菜单容器引用
const menuContainerRef = ref(null);
const actualMenuSize = ref({ width: MENU_WIDTH, height: MENU_HEIGHT });



// 处理菜单点击
const handleMenuClick = async ({ key }) => {
  const target = props.targetFile;
  emit('update:visible', false);
  
  switch (key) {
    case 'copy':
      const copyClipboard = {
        files: [{ ...target, sourcePath: props.currentPath }],
        operation: 'copy'
      };
      emit('update:clipboard', copyClipboard);
      message.success('已复制到剪切板');
      break;
    case 'cut':
      const cutClipboard = {
        files: [{ ...target, sourcePath: props.currentPath }],
        operation: 'cut'
      };
      emit('update:clipboard', cutClipboard);
      message.success('已剪切到剪切板');
      break;
    case 'paste':
      emit('paste-files');
      break;
    case 'edit':
      if (target.type !== 'directory') {
        emit('edit-file', target);
      }
      break;
    case 'rename':
      startRename(target);
      break;
    case 'delete':
      await deleteFile(target);
      break;
    case 'properties':
      emit('show-properties', target);
      break;
  }
};

// 重命名功能
const startRename = (file) => {
  currentRenameFile.value = file;
  newFileName.value = file.name;
  renameModalVisible.value = true;
};

const confirmRename = async () => {
  if (!newFileName.value.trim()) {
    message.error('文件名不能为空');
    return;
  }
  
  if (newFileName.value === currentRenameFile.value.name) {
    renameModalVisible.value = false;
    return;
  }

  try {
    const sourcePath = buildPath(props.currentPath, currentRenameFile.value.name);
    const targetPath = buildPath(props.currentPath, newFileName.value);
    let res;
    if (currentRenameFile.value.type === 'directory') {
      res = await dirApi.moveDir(props.clientId, {
        source: sourcePath,
        destination: targetPath
      });
    } else {
      res = await fileApi.moveFile(props.clientId, {
        source: sourcePath,
        destination: targetPath
      });
    }
    if (res.data.data.error !== ""){
      message.error(res.data.data.error);
    }else{
      message.success('移动成功');
    }
    renameModalVisible.value = false;
    emit('rename-file', currentRenameFile.value, newFileName.value);
    emit('refresh-list');
  } catch (error) {
    console.error('重命名失败:', error);
    message.error('重命名失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  }
};

const cancelRename = () => {
  renameModalVisible.value = false;
  newFileName.value = '';
  currentRenameFile.value = null;
};

// 删除文件
const deleteFile = async (file) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除 "${file.name}" 吗？`,
    onOk: async () => {
      try {
        const filePath = buildPath(props.currentPath, file.name);
        let res;
        if (file.type === 'directory') {
          res = await dirApi.deleteDir(props.clientId, {
            path: filePath,
            recursive: true,
            force: true
          });
        } else {
          res = await fileApi.deleteFile(props.clientId, {
            path: filePath,
            recursive: true,
            force_write: true
          });
        }

        if (res.data.data.error !== ""){
          message.error(res.data.data.error);
        }else{
          message.success('删除成功');
        }
        emit('delete-file', file);
        emit('refresh-list');
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
      }
    }
  });
};

// 监听重命名弹窗显示状态，自动聚焦输入框
watch(renameModalVisible, (newValue) => {
  if (newValue) {
    setTimeout(() => {
      if (renameInput.value) {
        renameInput.value.focus();
      }
    }, 100);
  }
});

// 暴露方法给父组件
defineExpose({
  startRename,
  confirmRename,
  cancelRename
});
</script>

<style scoped>
/* 右键菜单样式 */
.context-menu {
  position: fixed;
  z-index: 1000;
  user-select: none;
  animation: contextMenuFadeIn 0.15s ease-out;
}

@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 位置调整时的平滑过渡 */
.context-menu {
  transition: left 0.2s ease-out, top 0.2s ease-out;
}

.menu-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  min-width: 200px;
  padding: 6px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  margin: 2px 0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  font-size: 14px;
  color: #333;
  gap: 12px;
}

.menu-item:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.menu-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.menu-item.danger {
  color: #ff4d4f;
}

.menu-item.danger:hover {
  background: linear-gradient(135deg, #fff2f0 0%, #ffebe8 100%);
  color: #d32f2f;
}

.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  font-size: 14px;
  flex-shrink: 0;
}

.menu-text {
  flex: 1;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.menu-shortcut {
  font-size: 12px;
  color: #999;
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.menu-badge {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.menu-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e8e8e8 20%, #e8e8e8 80%, transparent 100%);
  margin: 6px 8px;
  opacity: 0.6;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .menu-container {
    background: rgba(40, 44, 52, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .menu-item {
    color: #e6e6e6;
  }
  
  .menu-item:hover {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  }
  
  .menu-shortcut {
    background: rgba(255, 255, 255, 0.1);
    color: #a0a0a0;
  }
  
  .menu-divider {
    background: linear-gradient(90deg, transparent 0%, #4a5568 20%, #4a5568 80%, transparent 100%);
  }
}
</style>