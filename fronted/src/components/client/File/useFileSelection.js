import { ref } from 'vue';

/**
 * 文件选择组合式函数
 * 包含多选逻辑、选择状态管理、清空选择等功能
 */
function useFileSelection() {
  // 选择状态
  const selectedFiles = ref([]);

  // 多选功能 - 处理表格选择变化
  const onSelectChange = (selectedRowKeys) => {
    selectedFiles.value = selectedRowKeys;
  };

  // 清除选择
  const clearSelection = () => {
    selectedFiles.value = [];
  };

  // 选择单个文件
  const selectFile = (fileName) => {
    if (!selectedFiles.value.includes(fileName)) {
      selectedFiles.value.push(fileName);
    }
  };

  // 取消选择单个文件
  const unselectFile = (fileName) => {
    const index = selectedFiles.value.indexOf(fileName);
    if (index > -1) {
      selectedFiles.value.splice(index, 1);
    }
  };

  // 切换文件选择状态
  const toggleFileSelection = (fileName) => {
    if (selectedFiles.value.includes(fileName)) {
      unselectFile(fileName);
    } else {
      selectFile(fileName);
    }
  };

  // 全选文件
  const selectAllFiles = (fileList) => {
    selectedFiles.value = fileList.map(file => file.name);
  };

  // 反选文件
  const invertSelection = (fileList) => {
    const allFileNames = fileList.map(file => file.name);
    selectedFiles.value = allFileNames.filter(name => !selectedFiles.value.includes(name));
  };

  // 检查文件是否被选中
  const isFileSelected = (fileName) => {
    return selectedFiles.value.includes(fileName);
  };

  // 获取选中文件数量
  const getSelectedCount = () => {
    return selectedFiles.value.length;
  };

  // 检查是否有文件被选中
  const hasSelectedFiles = () => {
    return selectedFiles.value.length > 0;
  };

  // 获取选中的文件对象列表
  const getSelectedFileObjects = (fileList) => {
    return fileList.filter(file => selectedFiles.value.includes(file.name));
  };

  return {
    // 状态
    selectedFiles,
    
    // 基础选择方法
    onSelectChange,
    clearSelection,
    selectFile,
    unselectFile,
    toggleFileSelection,
    
    // 批量选择方法
    selectAllFiles,
    invertSelection,
    
    // 查询方法
    isFileSelected,
    getSelectedCount,
    hasSelectedFiles,
    getSelectedFileObjects
  };
}

export { useFileSelection };