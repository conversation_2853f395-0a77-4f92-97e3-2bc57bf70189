/**
 * 文件操作工具函数
 * 提供通用的文件路径处理、文件类型判断等功能
 */

/**
 * 通用路径处理函数，避免双斜杠问题
 * @param {string} basePath - 基础路径
 * @param {string} fileName - 文件名
 * @returns {string} 处理后的完整路径
 */
export const buildPath = (basePath, fileName) => {
  if (basePath === '/' || basePath === '\\' || basePath === '') {
    return `/${fileName}`;
  }
  // 确保basePath不以/结尾，避免双斜杠
  const cleanPath = basePath.endsWith('/') ? basePath.slice(0, -1) : basePath;
  return `${cleanPath}/${fileName}`;
};

/**
 * 判断文件类型
 * @param {string} fileName - 文件名
 * @returns {string} 文件类型
 */
export const getFileType = (fileName) => {
  if (!fileName || typeof fileName !== 'string') {
    return 'unknown';
  }
  
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  // 图片文件
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico'];
  if (imageExtensions.includes(extension)) {
    return 'image';
  }
  
  // 视频文件
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', '3gp'];
  if (videoExtensions.includes(extension)) {
    return 'video';
  }
  
  // 音频文件
  const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'];
  if (audioExtensions.includes(extension)) {
    return 'audio';
  }
  
  // 文档文件
  const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'];
  if (documentExtensions.includes(extension)) {
    return 'document';
  }
  
  // 代码文件
  const codeExtensions = ['js', 'ts', 'jsx', 'tsx', 'vue', 'html', 'css', 'scss', 'sass', 'less', 'json', 'xml', 'yaml', 'yml', 'md', 'py', 'java', 'c', 'cpp', 'h', 'hpp', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'sh', 'bat', 'ps1'];
  if (codeExtensions.includes(extension)) {
    return 'code';
  }
  
  // 压缩文件
  const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'z'];
  if (archiveExtensions.includes(extension)) {
    return 'archive';
  }
  
  // 可执行文件
  const executableExtensions = ['exe', 'msi', 'dmg', 'pkg', 'deb', 'rpm', 'appimage'];
  if (executableExtensions.includes(extension)) {
    return 'executable';
  }
  
  return 'file';
};

/**
 * 检查文件是否为隐藏文件
 * @param {string} fileName - 文件名
 * @returns {boolean} 是否为隐藏文件
 */
export const isHiddenFile = (fileName) => {
  if (!fileName || typeof fileName !== 'string') {
    return false;
  }
  return fileName.startsWith('.');
};

/**
 * 获取文件扩展名
 * @param {string} fileName - 文件名
 * @returns {string} 文件扩展名（小写）
 */
export const getFileExtension = (fileName) => {
  if (!fileName || typeof fileName !== 'string') {
    return '';
  }
  const parts = fileName.split('.');
  return parts.length > 1 ? parts.pop().toLowerCase() : '';
};

/**
 * 获取不带扩展名的文件名
 * @param {string} fileName - 文件名
 * @returns {string} 不带扩展名的文件名
 */
export const getFileNameWithoutExtension = (fileName) => {
  if (!fileName || typeof fileName !== 'string') {
    return '';
  }
  const parts = fileName.split('.');
  return parts.length > 1 ? parts.slice(0, -1).join('.') : fileName;
};

/**
 * 验证文件名是否合法
 * @param {string} fileName - 文件名
 * @returns {boolean} 文件名是否合法
 */
export const isValidFileName = (fileName) => {
  if (!fileName || typeof fileName !== 'string') {
    return false;
  }
  
  // 检查长度
  if (fileName.length === 0 || fileName.length > 255) {
    return false;
  }
  
  // 检查非法字符
  const invalidChars = /[\\/:*?"<>|]/;
  if (invalidChars.test(fileName)) {
    return false;
  }
  
  // 检查保留名称（Windows）
  const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
  const nameWithoutExt = getFileNameWithoutExtension(fileName).toUpperCase();
  if (reservedNames.includes(nameWithoutExt)) {
    return false;
  }
  
  return true;
};

/**
 * 格式化路径，确保路径格式统一
 * @param {string} path - 路径
 * @returns {string} 格式化后的路径
 */
export const normalizePath = (path) => {
  if (!path || typeof path !== 'string') {
    return '/';
  }
  
  // 将反斜杠替换为正斜杠
  let normalizedPath = path.replace(/\\/g, '/');
  
  // 移除重复的斜杠
  normalizedPath = normalizedPath.replace(/\/+/g, '/');
  
  // 确保以斜杠开头
  if (!normalizedPath.startsWith('/')) {
    normalizedPath = '/' + normalizedPath;
  }
  
  // 移除末尾的斜杠（除非是根路径）
  if (normalizedPath.length > 1 && normalizedPath.endsWith('/')) {
    normalizedPath = normalizedPath.slice(0, -1);
  }
  
  return normalizedPath;
};

/**
 * 获取父目录路径
 * @param {string} path - 当前路径
 * @returns {string} 父目录路径
 */
export const getParentPath = (path) => {
  const normalizedPath = normalizePath(path);
  
  if (normalizedPath === '/') {
    return '/';
  }
  
  const parts = normalizedPath.split('/').filter(part => part.length > 0);
  if (parts.length <= 1) {
    return '/';
  }
  
  return '/' + parts.slice(0, -1).join('/');
};

/**
 * 检查路径是否为根路径
 * @param {string} path - 路径
 * @returns {boolean} 是否为根路径
 */
export const isRootPath = (path) => {
  const normalizedPath = normalizePath(path);
  return normalizedPath === '/' || normalizedPath === '';
};

/**
 * 从文件扩展名获取语言类型
 * @param {string} filename - 文件名
 * @returns {string} 语言类型
 */
export function getLanguageFromExtension(filename) {
  if (!filename) return 'plaintext';
  
  const ext = filename.split('.').pop()?.toLowerCase();
  
  const languageMap = {
    // JavaScript/TypeScript
    'js': 'javascript',
    'jsx': 'javascript',
    'ts': 'typescript',
    'tsx': 'typescript',
    'mjs': 'javascript',
    'cjs': 'javascript',
    
    // Web
    'html': 'html',
    'htm': 'html',
    'css': 'css',
    'scss': 'scss',
    'sass': 'sass',
    'less': 'less',
    'vue': 'vue',
    
    // Python
    'py': 'python',
    'pyw': 'python',
    'pyi': 'python',
    
    // Java
    'java': 'java',
    'class': 'java',
    
    // C/C++
    'c': 'c',
    'cpp': 'cpp',
    'cxx': 'cpp',
    'cc': 'cpp',
    'h': 'c',
    'hpp': 'cpp',
    'hxx': 'cpp',
    
    // C#
    'cs': 'csharp',
    
    // PHP
    'php': 'php',
    'php3': 'php',
    'php4': 'php',
    'php5': 'php',
    'phtml': 'php',
    
    // Go
    'go': 'go',
    
    // Rust
    'rs': 'rust',
    
    // Ruby
    'rb': 'ruby',
    'rbw': 'ruby',
    
    // Swift
    'swift': 'swift',
    
    // Kotlin
    'kt': 'kotlin',
    'kts': 'kotlin',
    
    // Shell
    'sh': 'shell',
    'bash': 'shell',
    'zsh': 'shell',
    'fish': 'shell',
    
    // PowerShell
    'ps1': 'powershell',
    'psm1': 'powershell',
    'psd1': 'powershell',
    
    // SQL
    'sql': 'sql',
    
    // JSON
    'json': 'json',
    'jsonc': 'jsonc',
    
    // XML
    'xml': 'xml',
    'xsl': 'xml',
    'xsd': 'xml',
    
    // YAML
    'yaml': 'yaml',
    'yml': 'yaml',
    
    // Markdown
    'md': 'markdown',
    'markdown': 'markdown',
    
    // Text
    'txt': 'plaintext',
    'text': 'plaintext',
    
    // Config files
    'ini': 'ini',
    'cfg': 'ini',
    'conf': 'ini',
    'toml': 'toml',
    
    // Docker
    'dockerfile': 'dockerfile'
  };
  
  return languageMap[ext] || 'plaintext';
}

/**
 * 获取语言的显示名称
 * @param {string} language - 语言类型
 * @returns {string} 显示名称
 */
export function getLanguageDisplayName(language) {
  const displayNames = {
    'javascript': 'JavaScript',
    'typescript': 'TypeScript',
    'html': 'HTML',
    'css': 'CSS',
    'scss': 'SCSS',
    'sass': 'Sass',
    'less': 'Less',
    'vue': 'Vue',
    'python': 'Python',
    'java': 'Java',
    'c': 'C',
    'cpp': 'C++',
    'csharp': 'C#',
    'php': 'PHP',
    'go': 'Go',
    'rust': 'Rust',
    'ruby': 'Ruby',
    'swift': 'Swift',
    'kotlin': 'Kotlin',
    'shell': 'Shell Script',
    'powershell': 'PowerShell',
    'sql': 'SQL',
    'json': 'JSON',
    'jsonc': 'JSON with Comments',
    'xml': 'XML',
    'yaml': 'YAML',
    'markdown': 'Markdown',
    'plaintext': 'Plain Text',
    'ini': 'INI',
    'toml': 'TOML',
    'dockerfile': 'Dockerfile'
  };
  
  return displayNames[language] || language.charAt(0).toUpperCase() + language.slice(1);
}

/**
 * 检查文件是否为二进制文件
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为二进制文件
 */
export function isBinaryFile(filename) {
  // 移除所有文件类型限制，允许所有文件在编辑器中打开
  // 所有文件都被视为文本文件，可以在编辑器中编辑
  return false;
}

/**
 * 检查文件是否支持预览（需要显示"打开"按钮）
 * @param {string} fileName - 文件名
 * @returns {boolean} 是否支持预览
 */
export function isPreviewableFile(fileName) {
  if (!fileName || typeof fileName !== 'string') {
    return false;
  }

  const extension = fileName.split('.').pop()?.toLowerCase();

  // PDF文件
  const pdfExtensions = ['pdf'];
  if (pdfExtensions.includes(extension)) {
    return true;
  }

  // Office文档
  const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
  if (officeExtensions.includes(extension)) {
    return true;
  }

  // 图片文件
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico'];
  if (imageExtensions.includes(extension)) {
    return true;
  }

  // 视频文件
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', '3gp'];
  if (videoExtensions.includes(extension)) {
    return true;
  }

  // 音频文件
  const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'];
  if (audioExtensions.includes(extension)) {
    return true;
  }

  return false;
}

/**
 * 获取文件的预览类型
 * @param {string} fileName - 文件名
 * @returns {string} 预览类型
 */
export function getPreviewType(fileName) {
  if (!fileName || typeof fileName !== 'string') {
    return 'unknown';
  }

  const extension = fileName.split('.').pop()?.toLowerCase();

  // PDF文件
  if (extension === 'pdf') {
    return 'pdf';
  }

  // Office文档
  const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
  if (officeExtensions.includes(extension)) {
    return 'office';
  }

  // 图片文件
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico'];
  if (imageExtensions.includes(extension)) {
    return 'image';
  }

  // 视频文件
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', '3gp'];
  if (videoExtensions.includes(extension)) {
    return 'video';
  }

  // 音频文件
  const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'];
  if (audioExtensions.includes(extension)) {
    return 'audio';
  }

  return 'unknown';
}