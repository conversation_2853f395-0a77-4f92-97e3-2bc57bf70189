<template>
  <div class="status-bar">
    <!-- 左侧状态信息 -->
    <div class="status-left">
      <div class="status-item" v-if="currentFile">
        <span class="status-text">{{ currentFile.path }}</span>
      </div>
    </div>
    
    <!-- 右侧状态信息 -->
    <div class="status-right">
      <!-- 行结束符 -->
      <div class="status-item clickable" @click="toggleLineEnding">
        <span class="status-text">{{ lineEnding }}</span>
      </div>
      
      <!-- 行列信息 -->
      <div class="status-item" v-if="cursorPosition">
        <span class="status-text">行 {{ cursorPosition.line }}，列 {{ cursorPosition.column }}</span>
      </div>
      
      <!-- 制表符大小 -->
      <div class="status-item clickable" @click="changeTabSize">
        <span class="status-text">制表符: {{ tabSize }}</span>
      </div>
      
      <!-- 编码 -->
      <div class="status-item clickable" @click="changeEncoding">
        <span class="status-text">{{ encoding }}</span>
      </div>
      
      <!-- 语言模式 -->
      <div class="status-item clickable" @click="changeLanguage" v-if="language">
        <span class="status-text">{{ language }}</span>
      </div>
      
      <!-- 缩进类型 -->
      <div class="status-item clickable" @click="toggleIndentType">
        <span class="status-text">{{ indentType === 'space' ? '空格' : '制表符' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  currentFile: {
    type: Object,
    default: null
  },
  cursorPosition: {
    type: Object,
    default: () => ({ line: 1, column: 1 })
  },
  lineEnding: {
    type: String,
    default: 'LF'
  },
  tabSize: {
    type: Number,
    default: 4
  },
  encoding: {
    type: String,
    default: 'UTF-8'
  },
  language: {
    type: String,
    default: ''
  },
  indentType: {
    type: String,
    default: 'space'
  }
});

// Emits
const emit = defineEmits([
  'toggle-line-ending',
  'change-tab-size',
  'change-encoding',
  'change-language',
  'toggle-indent-type'
]);

// 方法
const toggleLineEnding = () => {
  emit('toggle-line-ending');
};

const changeTabSize = () => {
  emit('change-tab-size');
};

const changeEncoding = () => {
  emit('change-encoding');
};

const changeLanguage = () => {
  emit('change-language');
};

const toggleIndentType = () => {
  emit('toggle-indent-type');
};
</script>

<style scoped>
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 22px;
  background: #007acc;
  color: #ffffff;
  font-size: 12px;
  padding: 0 8px;
  border-top: 1px solid #0e639c;
}

.status-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 6px;
  white-space: nowrap;
}

.status-item.clickable {
  cursor: pointer;
  transition: background-color 0.2s;
}

.status-item.clickable:hover {
  background: rgba(255, 255, 255, 0.1);
}

.status-text {
  color: #ffffff;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* 左侧文件路径样式 */
.status-left .status-item {
  padding-left: 0;
}

.status-left .status-text {
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>