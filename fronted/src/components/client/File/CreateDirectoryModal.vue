<template>
  <a-modal
    v-model:open="visible"
    title="新建目录"
    @ok="handleCreateDirectory"
    @cancel="handleCancel"
    :confirm-loading="loading"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item
        label="目录名称"
        name="dirName"
      >
        <a-input
          v-model:value="formData.dirName"
          placeholder="请输入目录名称"
          @press-enter="handleCreateDirectory"
          :maxlength="255"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { dirApi } from '@/api';
import { buildPath } from './fileUtils.js';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  clientId: {
    type: [String, Number],
    required: true
  },
  currentPath: {
    type: String,
    default: '/'
  }
});

// Emits
const emit = defineEmits([
  'update:modelValue',
  'create-complete'
]);

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const formRef = ref(null);

// 表单数据
const formData = reactive({
  dirName: ''
});

// 表单验证规则
const rules = {
  dirName: [
    {
      required: true,
      message: '请输入目录名称',
      trigger: 'blur'
    },
    {
      min: 1,
      max: 255,
      message: '目录名称长度应在1-255个字符之间',
      trigger: 'blur'
    },
    {
      pattern: /^[^\\/:*?"<>|]+$/,
      message: '目录名称不能包含以下字符：\\ / : * ? " < > |',
      trigger: 'blur'
    }
  ]
};



// 创建目录
const handleCreateDirectory = async () => {
  try {
    // 表单验证
    await formRef.value.validate();
    
    loading.value = true;
    
    const dirPath = buildPath(props.currentPath, formData.dirName.trim());
    const res = await dirApi.createDir(props.clientId, { path: dirPath });
    
    if (res.code === 200) {
      if (res.data.data.error !== ""){
        message.error(res.data.data.error);
      }else{
        message.success('创建目录成功');
      }
      handleCancel();
      emit('create-complete');
    } else {
      console.log(res.data);
      message.error(res.data?.error || '创建目录失败');
    }
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误，不显示错误消息
      return;
    }
    
    console.error('创建目录失败:', error);
    message.error('创建目录失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
  formData.dirName = '';
  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    visible.value = newValue;
  },
  { immediate: true }
);

// 监听 visible 变化
watch(
  () => visible.value,
  (newValue) => {
    emit('update:modelValue', newValue);
    if (!newValue) {
      // 弹窗关闭时重置表单
      formData.dirName = '';
      if (formRef.value) {
        formRef.value.clearValidate();
      }
    }
  }
);
</script>

<style scoped>
/* 表单样式优化 */
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-input) {
  border-radius: 6px;
}

:deep(.ant-input:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 字符计数样式 */
:deep(.ant-input-show-count-suffix) {
  color: #8c8c8c;
}
</style>