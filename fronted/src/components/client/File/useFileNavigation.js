import { ref } from 'vue';
import { buildPath } from './fileUtils.js';

/**
 * 文件导航组合式函数
 * 包含目录进入、返回上级、路径历史管理等功能
 */
function useFileNavigation(props, { getFileList, currentDisk }) {
  // 导航相关状态
  const currentPath = ref('/');
  const pathHistory = ref(['/']);

  // 进入目录
  const enterDirectory = (dir) => {
    if (dir.type === 'directory') {
      let newPath;
      if (currentPath.value === '/' || (currentDisk.value && currentPath.value === currentDisk.value.name + '/')) {
        if (currentDisk.value) {
          // 使用buildPath函数确保路径格式一致
          const diskPath = currentDisk.value.name.endsWith('/') ? currentDisk.value.name.slice(0, -1) : currentDisk.value.name;
          newPath = buildPath(diskPath, dir.name);
        } else {
          newPath = buildPath('/', dir.name);
        }
      } else {
        newPath = buildPath(currentPath.value, dir.name);
      }
      
      pathHistory.value.push(newPath);
      getFileList(newPath);
    }
  };

  // 返回上级目录
  const goBack = () => {
    if (pathHistory.value.length > 1) {
      pathHistory.value.pop();
      const parentPath = pathHistory.value[pathHistory.value.length - 1];
      getFileList(parentPath);
    }
  };

  // 处理面包屑导航事件
  const handleNavigateToPath = (targetPath) => {
    // 更新当前路径和历史
    currentPath.value = targetPath;
    pathHistory.value.push(targetPath);
    
    // 获取文件列表
    getFileList(targetPath);
  };

  const handleNavigateToSegment = ({ targetPath, index }) => {
    // 避免重复添加相同路径到历史记录
    if (pathHistory.value[pathHistory.value.length - 1] !== targetPath) {
      pathHistory.value.push(targetPath);
    }
    
    currentPath.value = targetPath;
    
    // 获取文件列表
    getFileList(targetPath);
  };

  // 处理磁盘切换事件
  const handleDiskChanged = ({ disk, path }) => {
    if (currentDisk) {
      currentDisk.value = disk;
    }
    currentPath.value = path;
    pathHistory.value = [path];
    getFileList(path);
  };

  // 设置当前路径（用于外部更新路径）
  const setCurrentPath = (path) => {
    currentPath.value = path;
  };

  // 重置路径历史
  const resetPathHistory = (initialPath = '/') => {
    pathHistory.value = [initialPath];
    currentPath.value = initialPath;
  };

  return {
    // 状态
    currentPath,
    pathHistory,
    
    // 方法
    buildPath,
    enterDirectory,
    goBack,
    handleNavigateToPath,
    handleNavigateToSegment,
    handleDiskChanged,
    setCurrentPath,
    resetPathHistory
  };
}

export { useFileNavigation };