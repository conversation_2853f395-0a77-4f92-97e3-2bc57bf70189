<template>
  <!-- 文件上传弹窗 -->
  <a-modal
    v-model:open="visible"
    title="上传文件"
    width="1200px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="upload-container">
      <!-- 左侧：本地文件上传 -->
      <div
        class="upload-section"
        @dragover.prevent="handleUploadSectionDragOver"
        @dragleave.prevent="handleUploadSectionDragLeave"
        @drop.prevent="handleUploadSectionDrop"
      >
        <div class="section-title">
          <CloudUploadOutlined />
          <span>本地文件上传</span>
        </div>
        <div class="upload-wrapper" :class="{ 'drag-active': isServerFileDragOver }">
          <FileUploader
            :client-id="clientId"
            :current-path="currentPath"
            @upload-complete="handleUploadComplete"
          />

          <!-- 统一拖拽覆盖层 -->
          <div v-if="isServerFileDragOver" class="unified-drop-overlay">
            <div class="drop-message">
              <CloudUploadOutlined />
              <p>传输文件到客户端</p>
              <p class="drop-path">目标路径: {{ currentPath }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：服务器文件浏览器 -->
      <div class="server-section">
        <div class="section-title">
          <HddOutlined />
          <span>服务器文件</span>
        </div>
        <ServerFileBrowser
          :client-id="clientId"
          @file-drag-start="handleServerFileDragStart"
          @transfer-file="handleTransferServerFile"
        />

        <!-- 拖拽目标区域 -->
        <div
          class="drop-zone"
          :class="{ 'drag-over': isDragOver }"
          @dragover.prevent="handleDragOver"
          @dragleave.prevent="handleDragLeave"
          @drop.prevent="handleDrop"
        >
          <div class="drop-message">
            <DownloadOutlined />
            <p>拖拽服务器文件到此处传输到客户端</p>
            <p class="drop-path">目标路径: {{ currentPath }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 传输确认弹窗 -->
    <a-modal
      v-model:open="transferConfirmVisible"
      title="确认文件传输"
      width="500px"
      @ok="confirmTransfer"
      @cancel="cancelTransfer"
    >
      <div class="transfer-confirm">
        <p><strong>源文件:</strong> {{ pendingTransfer.serverPath }}</p>
        <p><strong>目标路径:</strong> {{ pendingTransfer.destinationPath }}</p>
        <p><strong>文件大小:</strong> {{ formatFileSize(pendingTransfer.fileSize) }}</p>

        <a-alert
          v-if="pendingTransfer.fileExists"
          message="目标文件已存在"
          description="传输将覆盖现有文件，请确认是否继续。"
          type="warning"
          show-icon
          style="margin-top: 16px"
        />

        <div style="margin-top: 16px">
          <a-checkbox v-model:checked="forceOverwrite">
            强制覆盖现有文件
          </a-checkbox>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, watch, reactive } from 'vue';
import { message } from 'ant-design-vue';
import {
  CloudUploadOutlined,
  HddOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue';
import FileUploader from './FileUploader.vue';
import ServerFileBrowser from './ServerFileBrowser.vue';
import * as fileApi from '@/api/file';

// 接收属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  clientId: {
    type: [String, Number],
    required: true
  },
  currentPath: {
    type: String,
    default: '/'
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'upload-complete']);

// 内部可见性状态
const visible = ref(props.modelValue);

// 拖拽状态
const isDragOver = ref(false);
const isServerFileDragOver = ref(false);
const draggedFile = ref(null);

// 传输确认弹窗
const transferConfirmVisible = ref(false);
const forceOverwrite = ref(false);
const pendingTransfer = reactive({
  serverPath: '',
  destinationPath: '',
  fileSize: 0,
  fileExists: false
});

// 监听外部modelValue变化
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue;
});

// 监听内部visible变化，同步到外部
watch(visible, (newValue) => {
  emit('update:modelValue', newValue);
});

// 处理取消
const handleCancel = () => {
  visible.value = false;
};

// 处理上传完成
const handleUploadComplete = (result) => {
  emit('upload-complete', result);
};

// 处理服务器文件拖拽开始
const handleServerFileDragStart = (data) => {
  draggedFile.value = data;
};

// 处理拖拽悬停
const handleDragOver = (event) => {
  if (draggedFile.value) {
    isDragOver.value = true;
  }
};

// 处理拖拽离开
const handleDragLeave = (event) => {
  isDragOver.value = false;
};

// 处理拖拽放置
const handleDrop = (event) => {
  isDragOver.value = false;

  if (!draggedFile.value) return;

  try {
    const dataText = event.dataTransfer.getData('application/json');
    if (dataText) {
      const data = JSON.parse(dataText);
      if (data.type === 'server-file') {
        showTransferConfirm(data.serverPath, data.file);
      }
    }
  } catch (error) {
    console.error('解析拖拽数据失败:', error);
  }

  draggedFile.value = null;
};

// 显示传输确认弹窗
const showTransferConfirm = (serverPath, file) => {
  pendingTransfer.serverPath = serverPath;
  pendingTransfer.destinationPath = props.currentPath + '/' + file.name;
  pendingTransfer.fileSize = file.size;
  pendingTransfer.fileExists = false; // 这里可以添加检查逻辑

  forceOverwrite.value = false;
  transferConfirmVisible.value = true;
};

// 确认传输
const confirmTransfer = async () => {
  try {
    const response = await fileApi.transferServerFileToClient(props.clientId, {
      server_file_path: pendingTransfer.serverPath,
      destination_path: pendingTransfer.destinationPath,
      force: forceOverwrite.value,
      chunk_size: 65536
    });

    message.success(`开始传输文件: ${response.data.message}`);
    transferConfirmVisible.value = false;

    // 通知父组件刷新文件列表
    emit('upload-complete', {
      type: 'server-transfer',
      taskId: response.data.task_id
    });

  } catch (error) {
    console.error('传输文件失败:', error);
    message.error('传输文件失败: ' + (error.response?.data?.message || error.message));
  }
};

// 取消传输
const cancelTransfer = () => {
  transferConfirmVisible.value = false;
  pendingTransfer.serverPath = '';
  pendingTransfer.destinationPath = '';
  pendingTransfer.fileSize = 0;
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 处理服务器文件传输
const handleTransferServerFile = (data) => {
  showTransferConfirm(data.serverPath, data.file);
};

// 处理上传区域的拖拽
const handleUploadSectionDragOver = (event) => {
  // 检查是否是服务器文件拖拽（有application/json类型且没有Files）
  const types = event.dataTransfer.types;
  const hasJsonData = types.includes('application/json');
  const hasFiles = types.includes('Files');

  // 只有当拖拽的是服务器文件（有JSON数据但没有本地文件）时才显示覆盖层
  if (hasJsonData && !hasFiles) {
    isServerFileDragOver.value = true;
    event.preventDefault();
  }
};

const handleUploadSectionDragLeave = (event) => {
  // 检查是否真的离开了上传区域
  const rect = event.currentTarget.getBoundingClientRect();
  const x = event.clientX;
  const y = event.clientY;

  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isServerFileDragOver.value = false;
  }
};

const handleUploadSectionDrop = (event) => {
  isServerFileDragOver.value = false;

  try {
    const dataText = event.dataTransfer.getData('application/json');
    if (dataText) {
      const data = JSON.parse(dataText);
      if (data.type === 'server-file') {
        showTransferConfirm(data.serverPath, data.file);
      }
    }
  } catch (error) {
    console.error('解析拖拽数据失败:', error);
  }
};
</script>

<style scoped>
.upload-container {
  display: flex;
  gap: 24px;
  height: 600px;
}

.upload-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 500px;
}

.server-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 500px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.drop-zone {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(24, 144, 255, 0.05);
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  z-index: 10;
}

.drop-zone.drag-over {
  opacity: 1;
  border-color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.drop-message {
  text-align: center;
  color: #1890ff;
}

.drop-message p {
  margin: 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.drop-path {
  font-size: 14px !important;
  color: #8c8c8c !important;
  font-weight: normal !important;
}

.transfer-confirm {
  padding: 16px 0;
}

.transfer-confirm p {
  margin-bottom: 12px;
  line-height: 1.6;
}

.transfer-confirm strong {
  color: #262626;
  font-weight: 500;
}

.upload-wrapper {
  position: relative;
  transition: all 0.3s ease;
}

.upload-wrapper.drag-active {
  opacity: 0.3;
}

.unified-drop-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(24, 144, 255, 0.95);
  border: 3px dashed #ffffff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  pointer-events: none;
}

.unified-drop-overlay .drop-message {
  text-align: center;
  color: #ffffff;
}

.unified-drop-overlay .drop-message p {
  margin: 12px 0;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.unified-drop-overlay .drop-path {
  font-size: 14px !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: normal !important;
}
</style>