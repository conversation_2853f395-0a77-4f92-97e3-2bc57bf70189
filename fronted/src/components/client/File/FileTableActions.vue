<template>
  <div class="file-table-actions">
    <!-- 返回上级行不显示操作按钮 -->
    <span v-if="isBackButton"></span>
    <!-- 正常文件项显示操作按钮 -->
    <a-space v-else>
      <!-- 预览按钮 - 只对特定文件类型显示 -->
      <a-button
        v-if="isPreviewableFile(record.name)"
        type="link"
        size="small"
        @click="handlePreviewFile"
        :disabled="record.type === 'directory'"
      >
        <template #icon>
          <EyeOutlined />
        </template>
        打开
      </a-button>

      <a-button
        type="link"
        size="small"
        @click="handleDownloadFile"
        :disabled="record.type === 'directory'"
      >
        <template #icon>
          <DownloadOutlined />
        </template>
        下载
      </a-button>
      <a-button
        type="link"
        size="small"
        @click="handleEditFile"
        :disabled="record.type === 'directory'"
      >
        <template #icon>
          <EditOutlined />
        </template>
        编辑
      </a-button>
      <a-button 
        type="link" 
        size="small"
        @click="handleStartRename"
      >
        <template #icon>
          <FormOutlined />
        </template>
        重命名
      </a-button>
      <a-dropdown>
        <template #overlay>
          <a-menu @click="({ key }) => handleMenuClick(key)">
            <a-menu-item key="copy">
              <template #icon>
                <CopyOutlined />
              </template>
              复制
            </a-menu-item>
            <a-menu-item key="cut">
              <template #icon>
                <ScissorOutlined />
              </template>
              剪切
            </a-menu-item>
            <a-menu-item key="rename">
              <template #icon>
                <FormOutlined />
              </template>
              重命名
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="delete" class="danger-menu-item">
              <template #icon>
                <DeleteOutlined />
              </template>
              删除
            </a-menu-item>
          </a-menu>
        </template>
        <a-button type="link" size="small">
          <template #icon>
            <MoreOutlined />
          </template>
          更多
        </a-button>
      </a-dropdown>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import {
  EditOutlined,
  DownloadOutlined,
  MoreOutlined,
  CopyOutlined,
  ScissorOutlined,
  DeleteOutlined,
  FormOutlined,
  EyeOutlined
} from '@ant-design/icons-vue';
import { isPreviewableFile } from './fileUtils.js';

// 定义 props
interface Props {
  record: any;
  isBackButton: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  record: () => ({}),
  isBackButton: false
});

// 定义 emits
interface Emits {
  downloadFile: [record: any];
  editFile: [record: any];
  previewFile: [record: any];
  startRename: [record: any];
  menuClick: [key: string, record: any];
}

const emit = defineEmits<Emits>();

// 事件处理函数
const handleDownloadFile = () => {
  emit('downloadFile', props.record);
};

const handleEditFile = () => {
  emit('editFile', props.record);
};

const handlePreviewFile = () => {
  emit('previewFile', props.record);
};

const handleStartRename = () => {
  emit('startRename', props.record);
};

const handleMenuClick = (key: string) => {
  emit('menuClick', key, props.record);
};
</script>

<style scoped>
.file-table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.danger-menu-item {
  color: #ff4d4f;
}

.danger-menu-item:hover {
  background-color: #fff2f0;
}
</style>