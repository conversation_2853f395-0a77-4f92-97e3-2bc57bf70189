import {
  FolderOutlined,
  FileOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileZipOutlined
} from '@ant-design/icons-vue';

// 文件管理器表格列定义
export const FILE_COLUMNS = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: '25%'
  },
  {
    title: '大小',
    dataIndex: 'size',
    key: 'size',
    width: '10%'
  },
  {
    title: '权限',
    dataIndex: 'permissions',
    key: 'permissions',
    width: '11%'
  },
  {
    title: '拥有者',
    dataIndex: 'owner',
    key: 'owner',
    width: '10%'
  },
  {
    title: '组',
    dataIndex: 'group',
    key: 'group',
    width: '10%'
  },
  {
    title: '修改时间',
    dataIndex: 'modifiedTime',
    key: 'modifiedTime',
    width: '20%'
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: '14%'
  }
];

// 文件图标映射
export const FILE_ICON_MAP = {
  directory: FolderOutlined,
  file: FileOutlined,
  text: FileTextOutlined,
  pdf: FilePdfOutlined,
  image: FileImageOutlined,
  zip: FileZipOutlined
};

// 文件图标颜色映射
export const FILE_ICON_COLOR_MAP = {
  directory: '#1890ff',
  file: '#666',
  text: '#52c41a',
  pdf: '#f5222d',
  image: '#722ed1',
  zip: '#fa8c16'
};

// 获取文件图标
export const getFileIcon = (type) => {
  return FILE_ICON_MAP[type] || FileOutlined;
};

// 获取文件图标颜色
export const getFileIconColor = (type) => {
  return FILE_ICON_COLOR_MAP[type] || '#666';
};

// 默认路径常量
export const DEFAULT_PATH = '/';

// 剪切板操作类型
export const CLIPBOARD_OPERATIONS = {
  COPY: 'copy',
  CUT: 'cut'
};

// 文件类型常量
export const FILE_TYPES = {
  DIRECTORY: 'directory',
  FILE: 'file',
  TEXT: 'text',
  PDF: 'pdf',
  IMAGE: 'image',
  ZIP: 'zip'
};

// 客户端状态常量
export const CLIENT_STATUS = {
  ONLINE: 1,
  OFFLINE: 0
};