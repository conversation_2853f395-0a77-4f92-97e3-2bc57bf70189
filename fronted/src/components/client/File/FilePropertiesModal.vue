<template>
  <a-modal
    v-model:open="visible"
    title="文件属性"
    :footer="null"
    width="500px"
    @cancel="handleCancel"
  >
    <div class="file-properties">
      <a-spin :spinning="loading">
        <div v-if="fileInfo" class="properties-content">
          <!-- 文件基本信息 -->
          <div class="property-section">
            <h4 class="section-title">基本信息</h4>
            <div class="property-grid">
              <div class="property-item">
                <span class="property-label">名称:</span>
                <span class="property-value">{{ fileInfo.name }}</span>
              </div>
              <div class="property-item">
                <span class="property-label">类型:</span>
                <span class="property-value">{{ fileInfo.is_dir ? '文件夹' : '文件' }}</span>
              </div>
              <div class="property-item">
                <span class="property-label">路径:</span>
                <span class="property-value" :title="fileInfo.path">{{ fileInfo.path }}</span>
              </div>
              <div class="property-item" v-if="!fileInfo.is_dir">
                <span class="property-label">大小:</span>
                <span class="property-value">{{ formatFileSize(fileInfo.size) }} ({{ fileInfo.size }} 字节)</span>
              </div>
              <div class="property-item">
                <span class="property-label">修改时间:</span>
                <span class="property-value">{{ formatDateTime(fileInfo.mod_time) }}</span>
              </div>
            </div>
          </div>

          <!-- 权限信息 -->
          <div class="property-section">
            <h4 class="section-title">权限信息</h4>
            <div class="property-grid">
              <div class="property-item">
                <span class="property-label">权限:</span>
                <span class="property-value">{{ fileInfo.permissions || '-' }}</span>
              </div>
              <div class="property-item">
                <span class="property-label">所有者:</span>
                <span class="property-value">{{ fileInfo.owner || '-' }}</span>
              </div>
              <div class="property-item">
                <span class="property-label">用户组:</span>
                <span class="property-value">{{ fileInfo.group || '-' }}</span>
              </div>
            </div>
          </div>

          <!-- 扩展信息 -->
          <div class="property-section" v-if="!fileInfo.is_dir">
            <h4 class="section-title">扩展信息</h4>
            <div class="property-grid">
              <div class="property-item">
                <span class="property-label">文件扩展名:</span>
                <span class="property-value">{{ getFileExtension(fileInfo.name) || '无' }}</span>
              </div>
              <div class="property-item" v-if="fileInfo.mime_type">
                <span class="property-label">MIME类型:</span>
                <span class="property-value">{{ fileInfo.mime_type }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else-if="error" class="error-content">
          <a-result
            status="error"
            title="获取文件信息失败"
            :sub-title="error"
          />
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { fileApi } from '@/api';
import { buildPath } from './fileUtils.js';

// 接收属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  file: {
    type: Object,
    default: null
  },
  clientId: {
    type: [String, Number],
    required: true
  },
  currentPath: {
    type: String,
    required: true
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue']);

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const fileInfo = ref(null);
const error = ref('');

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue;
  if (newValue && props.file) {
    getFileInfo();
  }
});

// 监听visible变化
watch(visible, (newValue) => {
  emit('update:modelValue', newValue);
});

// 获取文件信息
const getFileInfo = async () => {
  if (!props.file) return;
  
  try {
    loading.value = true;
    error.value = '';
    fileInfo.value = null;
    
    const filePath = buildPath(props.currentPath, props.file.name);
    const response = await fileApi.getFileInfo(props.clientId, { path: filePath,  is_full: true });
    
    if (response.code === 200) {
      if (response.data.data.error === '') {
        fileInfo.value = response.data.data;
      } else {
        error.value = response.data.data.error;
      }
    } else {
      error.value = response.data.data.error || response.data.error || '获取文件信息失败';
    }
  } catch (err) {
    console.error('获取文件信息失败:', err);
    error.value = err.response?.data?.error || err.response?.data?.message || err.message || '获取文件信息失败';
  } finally {
    loading.value = false;
  }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-';
  try {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (e) {
    return dateTimeStr;
  }
};

// 获取文件扩展名
const getFileExtension = (fileName) => {
  if (!fileName || typeof fileName !== 'string') {
    return '';
  }
  const parts = fileName.split('.');
  return parts.length > 1 ? parts.pop().toLowerCase() : '';
};

// 处理取消
const handleCancel = () => {
  visible.value = false;
};
</script>

<style scoped>
.file-properties {
  padding: 8px 0;
}

.properties-content {
  max-height: 500px;
  overflow-y: auto;
}

.property-section {
  margin-bottom: 24px;
}

.property-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.property-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.property-item {
  display: flex;
  align-items: flex-start;
  min-height: 24px;
}

.property-label {
  flex: 0 0 80px;
  font-weight: 500;
  color: #595959;
  font-size: 13px;
}

.property-value {
  flex: 1;
  color: #262626;
  font-size: 13px;
  word-break: break-all;
  line-height: 1.4;
}

.error-content {
  text-align: center;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .property-item {
    flex-direction: column;
    gap: 4px;
  }
  
  .property-label {
    flex: none;
  }
}
</style>