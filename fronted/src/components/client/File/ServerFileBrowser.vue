<template>
  <div class="server-file-browser">
    <!-- 标题栏 -->
    <div class="browser-header">
      <div class="header-title">
        <FolderOpenOutlined />
        <span>服务器上传目录</span>
      </div>
      <div class="header-actions">
        <a-button size="small" @click="refreshFiles">
          <ReloadOutlined />
        </a-button>
      </div>
    </div>

    <!-- 路径导航 -->
    <div class="path-navigation" v-if="currentPath !== ''">
      <a-breadcrumb>
        <a-breadcrumb-item>
          <a @click="navigateToPath('')">
            <HomeOutlined />
            根目录
          </a>
        </a-breadcrumb-item>
        <a-breadcrumb-item
          v-for="(segment, index) in pathSegments"
          :key="index"
        >
          <a @click="navigateToSegment(index)">
            {{ segment }}
          </a>
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <!-- 文件网格视图 -->
    <div class="file-grid" v-if="!loading">
      <!-- 返回上级目录 -->
      <div 
        v-if="currentPath !== ''" 
        class="file-item directory-item back-item"
        @click="navigateUp"
        @dragover.prevent
        @drop.prevent
      >
        <div class="file-icon">
          <ArrowUpOutlined />
        </div>
        <div class="file-name">返回上级</div>
      </div>

      <!-- 目录 -->
      <div
        v-for="dir in directories"
        :key="'dir-' + dir.name"
        class="file-item directory-item"
        @dblclick="enterDirectory(dir)"
        @dragover.prevent
        @drop.prevent="handleDrop($event, getFullPath(dir.name))"
      >
        <div class="file-icon">
          <FolderOutlined />
        </div>
        <div class="file-name">{{ dir.name }}</div>
      </div>

      <!-- 文件 -->
      <div 
        v-for="file in files" 
        :key="'file-' + file.name"
        class="file-item file-item-draggable"
        :class="{ 'selected': selectedFiles.includes(file.path) }"
        :draggable="true"
        @click="toggleFileSelection(file.path)"
        @dragstart="handleDragStart($event, file)"
      >
        <div class="file-icon">
          <component :is="getFileIcon(file.name)" />
        </div>
        <div class="file-name">{{ file.name }}</div>
        <div class="file-size">{{ formatFileSize(file.size) }}</div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
      <p>加载文件列表...</p>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && files.length === 0 && directories.length === 0" class="empty-state">
      <InboxOutlined />
      <p>此目录为空</p>
    </div>

    <!-- 拖拽提示 -->
    <div v-if="dragOverlay" class="drag-overlay">
      <div class="drag-message">
        <CloudUploadOutlined />
        <p>拖拽文件到客户端目录</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  FolderOpenOutlined,
  FolderOutlined,
  FileOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileZipOutlined,
  ReloadOutlined,
  HomeOutlined,
  ArrowUpOutlined,
  InboxOutlined,
  CloudUploadOutlined
} from '@ant-design/icons-vue'
import * as fileApi from '@/api/file'

const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['file-drag-start', 'transfer-file'])

// 响应式数据
const loading = ref(false)
const currentPath = ref('')
const files = ref([])
const directories = ref([])
const selectedFiles = ref([])
const dragOverlay = ref(false)

// 计算属性
const pathSegments = computed(() => {
  return currentPath.value ? currentPath.value.split('/').filter(Boolean) : []
})

// 获取文件列表
const fetchFiles = async (path = '') => {
  loading.value = true
  try {
    // 使用正确的API调用方式，传递path参数
    const response = await fileApi.getUploadedFiles(path)
    const fileList = response.data || []

    console.log('获取到的文件列表:', fileList) // 调试日志
    console.log('当前路径:', path) // 调试日志

    // 分离文件和目录，使用服务器返回的is_dir字段
    files.value = fileList.filter(item => !item.is_dir)
    directories.value = fileList.filter(item => item.is_dir)

    console.log('文件:', files.value.length, '目录:', directories.value.length) // 调试日志
    console.log('目录详情:', directories.value) // 调试目录数据
    console.log('文件详情:', files.value) // 调试文件数据
  } catch (error) {
    console.error('获取文件列表失败:', error)
    message.error('获取文件列表失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 刷新文件列表
const refreshFiles = () => {
  fetchFiles(currentPath.value)
}

// 导航到指定路径
const navigateToPath = (path) => {
  currentPath.value = path
  selectedFiles.value = []
  fetchFiles(path)
}

// 进入目录（双击文件夹）
const enterDirectory = (dir) => {
  if (!dir.is_dir) return

  // 构建新路径
  const newPath = currentPath.value ? `${currentPath.value}/${dir.name}` : dir.name
  console.log('进入目录:', dir.name, '新路径:', newPath)
  navigateToPath(newPath)
}

// 返回上级目录
const navigateUp = () => {
  const segments = pathSegments.value
  if (segments.length > 0) {
    segments.pop()
    navigateToPath(segments.join('/'))
  }
}

// 获取到指定索引的路径
const getPathUpToIndex = (index) => {
  return pathSegments.value.slice(0, index + 1).join('/')
}

// 导航到路径片段（面包屑导航）
const navigateToSegment = (index) => {
  const segments = pathSegments.value.slice(0, index + 1)
  const newPath = segments.join('/')
  console.log('导航到片段:', newPath)
  navigateToPath(newPath)
}

// 获取完整路径
const getFullPath = (name) => {
  return currentPath.value ? `${currentPath.value}/${name}` : name
}

// 切换文件选择
const toggleFileSelection = (filePath) => {
  const index = selectedFiles.value.indexOf(filePath)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(filePath)
  }
}

// 获取文件图标
const getFileIcon = (fileName) => {
  const ext = fileName.split('.').pop()?.toLowerCase()

  const iconMap = {
    // 文档类
    'pdf': FilePdfOutlined,
    'doc': FileTextOutlined,
    'docx': FileTextOutlined,
    'txt': FileTextOutlined,
    'md': FileTextOutlined,
    'rtf': FileTextOutlined,

    // 图片类
    'jpg': FileImageOutlined,
    'jpeg': FileImageOutlined,
    'png': FileImageOutlined,
    'gif': FileImageOutlined,
    'bmp': FileImageOutlined,
    'svg': FileImageOutlined,
    'webp': FileImageOutlined,
    'ico': FileImageOutlined,

    // 压缩包类
    'zip': FileZipOutlined,
    'rar': FileZipOutlined,
    '7z': FileZipOutlined,
    'tar': FileZipOutlined,
    'gz': FileZipOutlined,
    'bz2': FileZipOutlined,

    // 代码类
    'js': FileTextOutlined,
    'ts': FileTextOutlined,
    'html': FileTextOutlined,
    'css': FileTextOutlined,
    'json': FileTextOutlined,
    'xml': FileTextOutlined,
    'py': FileTextOutlined,
    'java': FileTextOutlined,
    'cpp': FileTextOutlined,
    'c': FileTextOutlined,
    'go': FileTextOutlined,
    'php': FileTextOutlined,
    'rb': FileTextOutlined,
    'swift': FileTextOutlined,
    'kt': FileTextOutlined,

    // 其他
    'exe': FileOutlined,
    'dmg': FileOutlined,
    'app': FileOutlined,
    'deb': FileOutlined,
    'rpm': FileOutlined
  }

  return iconMap[ext] || FileOutlined
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 处理拖拽开始
const handleDragStart = (event, file) => {
  event.dataTransfer.setData('application/json', JSON.stringify({
    type: 'server-file',
    file: file,
    serverPath: getFullPath(file.name)
  }))
  
  emit('file-drag-start', {
    file: file,
    serverPath: getFullPath(file.name)
  })
}

// 处理拖拽放置（文件夹）
const handleDrop = (event, targetPath) => {
  // 这里可以实现服务器端文件移动功能
  console.log('Drop to folder:', targetPath)
}

// 监听路径变化
watch(() => currentPath.value, (newPath, oldPath) => {
  console.log('路径变化:', oldPath, '->', newPath)
  fetchFiles(newPath)
})

// 组件挂载时获取文件列表
onMounted(() => {
  fetchFiles()
})

// 暴露方法给父组件
defineExpose({
  refreshFiles,
  getSelectedFiles: () => selectedFiles.value
})
</script>

<style scoped>
.server-file-browser {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border: 1px solid #E5E5E7;
  border-radius: 8px;
  overflow: hidden;
}

.browser-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #E5E5E7;
  background: #F2F2F7;
  min-height: 44px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1D1D1F;
  font-size: 13px;
}

.path-navigation {
  padding: 8px 16px;
  background: #F2F2F7;
  border-bottom: 1px solid #E5E5E7;
}

.file-grid {
  flex: 1;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  grid-auto-rows: max-content;
  gap: 20px;
  overflow-y: auto;
  max-height: 400px;
  background: #ffffff;
  align-content: start;
}

.file-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.15s ease;
  background: transparent;
  border: 1px solid transparent;
  position: relative;
  width: 100px;
  height: auto;
  max-height: 120px;
  justify-content: flex-start;
  align-self: start;
}

.file-item:hover {
  background: rgba(0, 0, 0, 0.04);
  border-radius: 8px;
}

.file-item.selected {
  background: #007AFF;
  border-radius: 8px;
}

.file-item.selected .file-name {
  color: white;
}

.file-item.selected .file-size {
  color: rgba(255, 255, 255, 0.8);
}

.file-item-draggable {
  cursor: grab;
}

.file-item-draggable:active {
  cursor: grabbing;
}

.directory-item {
  border: none;
  cursor: pointer;
}

.directory-item:hover {
  background: rgba(0, 0, 0, 0.04);
  transform: scale(1.02);
}

.directory-item:active {
  transform: scale(0.98);
}

.back-item {
  background: transparent;
  border: none;
}

.back-item:hover {
  background: rgba(0, 0, 0, 0.04);
}

.file-icon {
  font-size: 40px;
  margin-bottom: 6px;
  color: #1890ff;
  flex-shrink: 0;
}

.directory-item .file-icon {
  color: #007AFF;
  font-size: 44px;
}

.back-item .file-icon {
  color: #8E8E93;
  font-size: 40px;
}

.file-name {
  font-size: 12px;
  text-align: center;
  word-break: break-word;
  line-height: 1.3;
  max-width: 90px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: #1D1D1F;
  font-weight: 500;
  margin-top: 4px;
  flex-shrink: 0;
}

.file-size {
  font-size: 10px;
  color: #8E8E93;
  margin-top: 2px;
  text-align: center;
  flex-shrink: 0;
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  background: #ffffff;
  color: #8E8E93;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #8E8E93;
  font-size: 48px;
  background: #ffffff;
}

.empty-state p {
  margin-top: 16px;
  font-size: 14px;
  color: #8E8E93;
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(24, 144, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.drag-message {
  text-align: center;
  color: #1890ff;
  font-size: 24px;
}

.drag-message p {
  margin-top: 8px;
  font-size: 16px;
}
</style>
