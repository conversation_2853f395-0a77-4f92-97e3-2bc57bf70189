<template>
  <a-dropdown
    v-if="clientInfo && shouldShowDiskSelector(clientInfo.os)"
    :disabled="!clientInfo || clientInfo.status !== 1"
    @click="toggleDiskPanel"
    placement="bottomLeft"
  >
    <template #overlay>
      <div class="tech-disk-menu">
        <div class="tech-disk-header">
          <div class="tech-disk-title">
            <component :is="getSystemIcon(clientInfo.os)" class="system-icon" />
            <span>{{ getSystemName(clientInfo.os) }} 存储设备</span>
          </div>
          <div class="tech-disk-subtitle">选择要访问的磁盘驱动器</div>
        </div>
        <div class="tech-disk-list">
          <div 
            v-for="disk in diskList" 
            :key="disk.name" 
            class="tech-disk-item"
            :class="{ 'active': currentDisk && currentDisk.name === disk.name }"
            @click="switchDisk(disk)"
          >
            <div class="tech-disk-icon-container">
              <div class="tech-disk-icon-bg">
                <span class="tech-disk-icon">{{ disk.icon }}</span>
              </div>
              <div class="tech-disk-status" :class="getDiskStatusClass(disk)"></div>
            </div>
            <div class="tech-disk-content">
              <div class="tech-disk-main">
                <div class="tech-disk-name">{{ disk.displayName }}</div>
                <div class="tech-disk-path">{{ disk.name }}</div>
              </div>
              <div class="tech-disk-stats">
                <div class="tech-disk-size">{{ disk.sizeFormatted }}</div>
                <div class="tech-disk-usage">
                  <div class="usage-bar">
                    <div class="usage-fill" :style="{ width: disk.usedPercentage + '%' }"></div>
                  </div>
                  <span class="usage-text">{{ disk.usedPercentage }}% 已用</span>
                </div>
              </div>
            </div>
            <div class="tech-disk-arrow">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M6 4l4 4-4 4V4z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="tech-disk-trigger" :class="{ 'disabled': !clientInfo || clientInfo.status !== 1 }">
      <div class="tech-trigger-icon">
        <component :is="getSystemIcon(clientInfo?.os)" class="system-icon-small" />
      </div>
      <div class="tech-trigger-content">
        <div class="tech-trigger-title">{{ currentDisk ? currentDisk.displayName : '选择磁盘' }}</div>
        <div class="tech-trigger-subtitle">{{ currentDisk ? currentDisk.name : '点击选择存储设备' }}</div>
      </div>
      <div class="tech-trigger-arrow">
        <DownOutlined />
      </div>
    </div>
  </a-dropdown>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { dirApi } from '@/api';
import { formatFileSize } from '@/utils/format';
import {
  DownOutlined,
  DatabaseOutlined,
  WindowsOutlined,
  AppleOutlined,
  AndroidOutlined
} from '@ant-design/icons-vue';

// 接收属性
const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  },
  clientInfo: {
    type: Object,
    default: null
  },
  currentDisk: {
    type: Object,
    default: null
  }
});

// 发射事件
const emit = defineEmits(['disk-changed']);

// 状态变量
const diskList = ref([]);
const diskLoading = ref(false);
const showDiskPanel = ref(false);

// 获取磁盘列表
const getDiskList = async () => {
  try {
    diskLoading.value = true;
    const res = await dirApi.listDisks(props.clientId, { include_details: true });
    
    if (res.code === 200) {
      const rawDiskList = res.data?.data?.disk_infos || [];
      
      // 映射磁盘信息
      diskList.value = rawDiskList.map(disk => {
        return {
          ...disk,
          name: disk.mount_point || disk.device,
          displayName: disk.label || disk.mount_point || disk.device,
          icon: getDiskIcon(disk.icon),
          sizeFormatted: formatFileSize(disk.total_size),
          freeFormatted: formatFileSize(disk.avail_size),
          usedPercentage: disk.total_size > 0 ? Math.round(((disk.total_size - disk.avail_size) / disk.total_size) * 100) : 0
        };
      });
      
      // 设置默认磁盘
      if (diskList.value.length > 0 && !props.currentDisk) {
        let defaultDisk;
        if (props.clientInfo?.os === 'windows') {
          // Windows系统优先选择C盘
          defaultDisk = diskList.value.find(disk => disk.name && disk.name.toLowerCase().startsWith('c:')) || diskList.value[0];
        } else if (props.clientInfo?.os === 'linux' || props.clientInfo?.os === 'darwin') {
          // Linux和macOS系统优先选择根目录
          defaultDisk = diskList.value.find(disk => disk.name === '/') || diskList.value[0];
        } else {
          // 其他系统选择第一个磁盘
          defaultDisk = diskList.value[0];
        }
        
        if (defaultDisk && defaultDisk.name) {
          switchDisk(defaultDisk);
        }
      }
    } else {
      message.error(res.message || '获取磁盘列表失败');
    }
  } catch (error) {
    console.error('获取磁盘列表失败:', error);
    message.error('获取磁盘列表失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    diskLoading.value = false;
  }
};

// 获取磁盘图标
const getDiskIcon = (icon) => {
  // 如果服务器直接返回图标字符，直接使用
  if (icon && typeof icon === 'string' && icon.length <= 2) {
    return icon;
  }
  
  // 否则根据类型映射
  const iconMap = {
    'system': '💾',
    'disk': '💽',
    'floppy': '💿',
    'fixed': '💾',
    'removable': '💿',
    'network': '🌐',
    'ram': '⚡',
    'cdrom': '💿',
    'unknown': '💽'
  };
  return iconMap[icon] || '💽';
};

// 获取系统图标
const getSystemIcon = (os) => {
  if (!os) return DatabaseOutlined;

  const osLower = os.toLowerCase();

  // Windows系统
  if (osLower.includes('windows')) return WindowsOutlined;

  // macOS/Darwin系统
  if (osLower.includes('darwin') || osLower.includes('macos') || osLower.includes('mac')) return AppleOutlined;

  // Linux发行版和通用Linux/Unix
  if (osLower.includes('linux') ||
      osLower.includes('ubuntu') ||
      osLower.includes('debian') ||
      osLower.includes('kali') ||
      osLower.includes('centos') ||
      osLower.includes('redhat') ||
      osLower.includes('fedora') ||
      osLower.includes('suse') ||
      osLower.includes('arch') ||
      osLower.includes('unix')) {
    return DatabaseOutlined;
  }

  // 移动端
  if (osLower.includes('android')) return AndroidOutlined;

  return DatabaseOutlined;
};

// 判断是否应该显示磁盘选择器
const shouldShowDiskSelector = (os) => {
  if (!os) return false;

  const osLower = os.toLowerCase();

  // Windows系统
  if (osLower.includes('windows')) return true;

  // macOS/Darwin系统
  if (osLower.includes('darwin') || osLower.includes('macos') || osLower.includes('mac')) return true;

  // Linux发行版
  if (osLower.includes('linux') ||
      osLower.includes('ubuntu') ||
      osLower.includes('debian') ||
      osLower.includes('kali') ||
      osLower.includes('centos') ||
      osLower.includes('redhat') ||
      osLower.includes('fedora') ||
      osLower.includes('suse') ||
      osLower.includes('arch')) {
    return true;
  }

  // Unix系统
  if (osLower.includes('unix')) return true;

  return false;
};

// 获取系统名称
const getSystemName = (os) => {
  if (!os) return '未知系统';

  const osLower = os.toLowerCase();

  // Windows系统
  if (osLower.includes('windows')) return 'Windows';

  // macOS/Darwin系统
  if (osLower.includes('darwin') || osLower.includes('macos') || osLower.includes('mac')) return 'macOS';

  // Linux发行版
  if (osLower.includes('ubuntu')) return 'Ubuntu';
  if (osLower.includes('debian')) return 'Debian';
  if (osLower.includes('kali')) return 'Kali Linux';
  if (osLower.includes('centos')) return 'CentOS';
  if (osLower.includes('redhat') || osLower.includes('rhel')) return 'Red Hat';
  if (osLower.includes('fedora')) return 'Fedora';
  if (osLower.includes('suse') || osLower.includes('opensuse')) return 'SUSE';
  if (osLower.includes('arch')) return 'Arch Linux';

  // 通用Linux/Unix
  if (osLower.includes('linux')) return 'Linux';
  if (osLower.includes('unix')) return 'Unix';

  // 移动端
  if (osLower.includes('android')) return 'Android';
  if (osLower.includes('ios')) return 'iOS';

  // 返回原始值（首字母大写）
  return os.charAt(0).toUpperCase() + os.slice(1);
};

// 获取磁盘状态样式类
const getDiskStatusClass = (disk) => {
  if (disk.usedPercentage >= 90) {
    return 'status-critical';
  } else if (disk.usedPercentage >= 75) {
    return 'status-warning';
  } else {
    return 'status-normal';
  }
};

// 切换磁盘
const switchDisk = (disk) => {
  showDiskPanel.value = false;
  // 确保磁盘路径格式正确，避免双斜杠
  const newPath = disk.name.endsWith('/') ? disk.name : disk.name + '/';
  emit('disk-changed', { disk, path: newPath });
};

// 切换磁盘面板显示状态
const toggleDiskPanel = () => {
  showDiskPanel.value = !showDiskPanel.value;
  if (showDiskPanel.value && diskList.value.length === 0) {
    getDiskList();
  }
};

// 暴露方法给父组件
defineExpose({
  getDiskList
});

// 组件挂载时获取磁盘列表
onMounted(() => {
  if (props.clientInfo?.status === 1) {
    getDiskList();
  }
});
</script>

<style scoped>
/* 科技感磁盘选择器样式 */
.tech-disk-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-width: 200px;
  height: 32px;
}

.tech-disk-trigger:hover {
  border-color: #40a9ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.tech-disk-trigger.disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25);
}

.tech-trigger-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.system-icon-small {
  font-size: 14px;
  color: #595959;
}

.tech-trigger-content {
  flex: 1;
  text-align: left;
}

.tech-trigger-title {
  font-size: 14px;
  font-weight: 400;
  color: #262626;
  line-height: 1;
}

.tech-trigger-subtitle {
  display: none;
}

.tech-trigger-arrow {
  color: #8c8c8c;
  font-size: 12px;
  transition: transform 0.2s;
}

.tech-disk-trigger:hover .tech-trigger-arrow {
  color: #40a9ff;
}

.tech-disk-menu {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #d9d9d9;
  overflow: hidden;
  min-width: 300px;
}

.tech-disk-header {
  background: #fafafa;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.tech-disk-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #262626;
}

.system-icon {
  font-size: 16px;
  color: #595959;
}

.tech-disk-subtitle {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0;
}

.tech-disk-list {
  padding: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.tech-disk-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 0;
  position: relative;
  background: #fff;
}

.tech-disk-item:hover {
  background: #f5f5f5;
}

.tech-disk-item.active {
  background: #e6f7ff;
  color: #1890ff;
}

.tech-disk-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-disk-icon-bg {
  width: 32px;
  height: 32px;
  background: #f0f0f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tech-disk-icon {
  font-size: 16px;
}

.tech-disk-status {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid #fff;
}

.status-normal {
  background: #52c41a;
}

.status-warning {
  background: #faad14;
}

.status-critical {
  background: #ff4d4f;
}

.tech-disk-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tech-disk-main {
  display: flex;
  flex-direction: column;
}

.tech-disk-name {
  font-size: 14px;
  font-weight: 400;
  color: #262626;
  margin: 0;
  line-height: 1.4;
}

.tech-disk-path {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.2;
}

.tech-disk-stats {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2px;
}

.tech-disk-size {
  font-size: 12px;
  color: #8c8c8c;
}

.tech-disk-usage {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.usage-bar {
  flex: 1;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: #1890ff;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.usage-text {
  font-size: 12px;
  color: #8c8c8c;
  min-width: 50px;
  text-align: right;
}

.tech-disk-arrow {
  color: #bfbfbf;
  font-size: 12px;
}

.tech-disk-item:hover .tech-disk-arrow {
  color: #8c8c8c;
}

.tech-disk-item.active .tech-disk-arrow {
  color: #1890ff;
}

/* 下拉菜单样式 */
:deep(.ant-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0;
  border: none;
  background: transparent;
}

:deep(.ant-dropdown-menu-item) {
  padding: 0;
  margin: 0;
  border: none;
  background: transparent;
}

:deep(.ant-dropdown-menu-item:hover) {
  background-color: transparent;
}

/* 确保科技感菜单不受Ant Design样式影响 */
:deep(.ant-dropdown) {
  z-index: 9999;
}

:deep(.ant-dropdown .tech-disk-menu) {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  min-width: 400px;
  backdrop-filter: blur(20px);
}
</style>