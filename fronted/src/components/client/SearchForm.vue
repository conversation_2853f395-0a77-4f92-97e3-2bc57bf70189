<template>
  <div class="search-form">
    <a-form layout="inline" :model="searchForm">
      <a-form-item label="监听器类型">
        <a-select
          v-model:value="searchForm.listenerType"
          style="width: 120px"
          placeholder="选择类型"
          allowClear
        >
          <a-select-option v-for="type in listenerTypes" :key="type" :value="type">
            {{ type.toUpperCase() }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="状态">
        <a-select
          v-model:value="searchForm.status"
          style="width: 120px"
          placeholder="选择状态"
          allowClear
        >
          <a-select-option :value="1">在线</a-select-option>
          <a-select-option :value="0">离线</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="远程地址">
        <a-input v-model:value="searchForm.remoteAddr" placeholder="搜索远程地址" allowClear @pressEnter="handleSearch" />
      </a-form-item>
      <a-form-item label="备注">
        <a-input v-model:value="searchForm.remark" placeholder="搜索备注" allowClear @pressEnter="handleSearch" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleSearch">
          <template #icon><SearchOutlined /></template>
          搜索
        </a-button>
        <a-button style="margin-left: 10px" @click="resetSearch">
          <template #icon><ReloadOutlined /></template>
          重置
        </a-button>
        <a-button 
          type="primary" 
          danger 
          style="margin-left: 10px; float: right" 
          @click="handleClearOffline"
          :loading="clearLoading"
        >
          <template #icon><DeleteOutlined /></template>
          清除离线客户端
        </a-button>
      </a-form-item>

    </a-form>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { SearchOutlined, ReloadOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { listenerApi, clientApi } from '@/api';

// 定义属性
const props = defineProps({
  searchForm: {
    type: Object,
    required: true
  }
});

// 定义事件
const emit = defineEmits(['search', 'reset', 'refresh']);

// 状态变量
const listenerTypes = ref(['pipe', 'tcp']);
const clearLoading = ref(false);

// 获取监听器类型列表
const getListenerTypes = async () => {
  try {
    const response = await listenerApi.getListenerList({
      page: 1,
      pageSize: 1000,
      remark: '',
      localListenAddr: '',
      remoteConnectAddr: ''
    });
    if (response.code === 200 && response.data.list) {
      const types = [...new Set(response.data.list.map(item => item.type))];
      if (types.length > 0) {
        listenerTypes.value = types;
      }
    }
  } catch (error) {
    console.error('获取监听器类型失败:', error);
    // 使用默认类型
  }
};

// 处理搜索
const handleSearch = () => {
  emit('search');
};

// 重置搜索
const resetSearch = () => {
  emit('reset');
};

// 清除离线客户端
const handleClearOffline = () => {
  Modal.confirm({
    title: '确认清除',
    content: '确定要清除所有离线客户端吗？此操作不可撤销。',
    okText: '确定',
    cancelText: '取消',
    type: 'warning',
    onOk: async () => {
      try {
        clearLoading.value = true;
        const response = await clientApi.clearOfflineClients();
        if (response.code === 200) {
          console.log(response);
          message.success(response.msg || '清除离线客户端成功');
          emit('refresh'); // 刷新客户端列表
        } else {
          message.error(response.msg || '清除离线客户端失败');
        }
      } catch (error) {
        console.error('清除离线客户端失败:', error);
        message.error('清除离线客户端失败: ' + (error.response?.msg || error.response?.data?.data?.error || error.message));
      } finally {
        clearLoading.value = false;
      }
    }
  });
};

// 组件挂载时获取监听器类型
onMounted(() => {
  getListenerTypes();
});

</script>

<style scoped>
.search-form {
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
  }
}
</style>