<template>
  <div class="remark-modal">
    <a-modal
      :open="visible"
      title="编辑备注"
      @ok="handleSubmit"
      :confirmLoading="submitLoading"
      @update:open="(val) => emit('update:visible', val)"
    >
      <a-form :model="currentClient">
        <a-form-item label="备注">
          <a-input v-model:value="currentClient.remark" placeholder="请输入备注" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  client: {
    type: Object,
    default: () => ({})
  },
  submitLoading: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'submit']);

// 当前客户端数据
const currentClient = ref({...props.client});

// 监听client属性变化
watch(() => props.client, (newVal) => {
  currentClient.value = {...newVal};
}, { deep: true });

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时，更新当前客户端数据
    currentClient.value = {...props.client};
  }
});

// 处理提交
const handleSubmit = () => {
  emit('submit', currentClient.value);
};
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>