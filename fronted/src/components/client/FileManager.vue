<template>
  <div class="file-manager">
    <div class="file-manager-header">
      <div class="header-left">
        <h3>文件管理器</h3>
        <a-tag v-if="clientInfo" :color="clientInfo.status === 1 ? 'green' : 'red'">
          {{ clientInfo.status === 1 ? '在线' : '离线' }}
        </a-tag>
      </div>
      <!-- 文件管理器工具栏 -->
      <FileManagerToolbar
        ref="toolbarRef"
        :client-id="clientId"
        :client-info="clientInfo"
        :current-disk="currentDisk"
        :current-path="currentPath"
        :show-hidden="showHidden"
        :clipboard-files="clipboard.files"
        @disk-changed="handleDiskChanged"
        @upload="uploadModalVisible = true"
        @create-dir="createDirModalVisible = true"
        @create-file="createFileModalVisible = true"
        @refresh="refreshFileList"
        @paste="() => pasteFiles(currentPath)"
        @show-transfer-tasks="showTransferTasks"
        @show-hidden-change="handleShowHiddenChange"
        @go-back="goBack"
        @open-code-editor="openCodeEditor"
      />
    </div>

    <div class="file-manager-content">
      <!-- 面包屑导航 -->
      <BreadcrumbNavigation
        :current-path="currentPath"
        :current-disk="currentDisk"
        :client-info="clientInfo"
        :path-history="pathHistory"
        @navigate-to-path="handleNavigateToPath"
        @navigate-to-segment="handleNavigateToSegment"
      />

      <!-- 批量操作工具栏 -->
      <BatchOperationsBar
        v-if="selectedFiles.length > 0"
        :selected-files="selectedFiles"
        :can-paste="clipboard.files.length > 0"
        @copy="() => copySelectedFiles(selectedFiles, fileList, currentPath)"
        @cut="() => cutSelectedFiles(selectedFiles, fileList, currentPath)"
        @paste="() => pasteFiles(currentPath)"
        @delete="() => confirmDeleteSelected(selectedFiles, fileList, currentPath)"
        @clear-selection="clearSelection"
      />

      <!-- 文件列表表格 -->
      <FileTable
        :columns="fileColumns"
        :data-source="fileList"
        :loading="loading"
        :selected-files="selectedFiles"
        :show-back-button="pathHistory.length > 1"
        @selection-change="onSelectChange"
        @go-back="goBack"
        @enter-directory="enterDirectory"
        @context-menu="showContextMenu"
        @download-file="(file) => downloadFile(file, currentPath, fileTransferModalRef)"
        @edit-file="editFile"
        @preview-file="previewFile"
        @start-rename="startRename"
        @menu-click="handleMenuClickWrapper"
      />
      
      <!-- 分页和排序控件 -->
      <div class="file-pagination-container">
        <div class="pagination-info">
          <span class="file-count">共 {{ totalCount }} 个项目</span>
          <a-select 
            v-model:value="pageSize" 
            size="small" 
            style="width: 100px; margin-left: 16px;"
            @change="handlePageSizeChange"
          >
            <a-select-option :value="25">25/页</a-select-option>
            <a-select-option :value="50">50/页</a-select-option>
            <a-select-option :value="100">100/页</a-select-option>
            <a-select-option :value="200">200/页</a-select-option>
          </a-select>
        </div>
        
        <div class="sort-controls">
          <span style="margin-right: 8px;">排序:</span>
          <a-select 
            v-model:value="sortBy" 
            size="small" 
            style="width: 100px; margin-right: 8px;"
            @change="handleSortChange"
          >
            <a-select-option value="name">名称</a-select-option>
            <a-select-option value="size">大小</a-select-option>
            <a-select-option value="mod_time">修改时间</a-select-option>
            <a-select-option value="type">类型</a-select-option>
          </a-select>
          <a-select 
            v-model:value="sortOrder" 
            size="small" 
            style="width: 80px;"
            @change="handleSortChange"
          >
            <a-select-option value="asc">升序</a-select-option>
            <a-select-option value="desc">降序</a-select-option>
          </a-select>
        </div>
        
        <a-pagination
          v-model:current="currentPage"
          :total="totalCount"
          :page-size="pageSize"
          :show-size-changer="false"
          :show-quick-jumper="true"
          :show-total="(total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 项`"
          size="small"
          @change="handlePageChange"
        />
      </div>

    <!-- 文件上传弹窗 -->
    <FileUploadModal
      v-model="uploadModalVisible"
      :client-id="clientId"
      :current-path="currentPath"
      @upload-complete="handleUploadComplete"
    />



    <!-- VSCode风格代码编辑器 - 可拖拽可调整大小浮动窗口 -->
    <div v-if="codeEditorVisible" class="draggable-code-editor-overlay">
      <div
        ref="codeEditorWindow"
        class="draggable-code-editor-window"
        :style="codeEditorWindowStyle"
        :class="{
          'maximized': isCodeEditorMaximized,
          'minimized': isCodeEditorMinimized,
          'dragging': isCodeEditorDragging,
          'resizing': isCodeEditorResizing
        }"
      >
        <!-- 编辑器标题栏 -->
        <div class="code-editor-title-bar" @mousedown="startCodeEditorDrag">
          <div class="title-bar-left">
            <div class="window-icon">
              <svg viewBox="0 0 16 16" width="16" height="16">
                <path d="M14.5 3h-13c-.3 0-.5.2-.5.5v9c0 .3.2.5.5.5h13c.3 0 .5-.2.5-.5v-9c0-.3-.2-.5-.5-.5zM14 12H2V6h12v6zM2 5V4h12v1H2z" fill="currentColor"/>
              </svg>
            </div>
            <span class="editor-title">EZCoder 代码编辑器</span>
          </div>
          <div class="title-bar-right">
            <button class="title-bar-btn minimize-btn" @click="minimizeCodeEditor" @mousedown.stop>
              <svg viewBox="0 0 16 16" width="12" height="12">
                <path d="M14 8v1H3V8h11z" fill="currentColor"/>
              </svg>
            </button>
            <button class="title-bar-btn maximize-btn" @click="toggleCodeEditorMaximize" @mousedown.stop>
              <svg viewBox="0 0 16 16" width="12" height="12">
                <path v-if="!isCodeEditorMaximized" d="M3 3v10h10V3H3zm9 9H4V4h8v8z" fill="currentColor"/>
                <path v-else d="M3 5v8h8v-8H3zm7 7H4V6h6v6z M5 2v1h8v8h1V2H5z" fill="currentColor"/>
              </svg>
            </button>
            <button class="title-bar-btn close-btn" @click="closeCodeEditor" @mousedown.stop>
              <svg viewBox="0 0 16 16" width="12" height="12">
                <path d="m8.746 8 3.1-3.1a.527.527 0 1 0-.746-.746L8 7.254l-3.1-3.1a.527.527 0 1 0-.746.746L7.254 8l-3.1 3.1a.527.527 0 1 0 .746.746L8 8.746l3.1 3.1a.527.527 0 1 0 .746-.746L8.746 8z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- 编辑器内容区域 -->
        <div class="code-editor-content-area" v-if="!isCodeEditorMinimized">
          <CodeEditor
            :client-id="clientId"
            :initial-file="currentEditingFile"
            :initial-file-path="currentEditingPath"
            :root-path="currentDisk?.path || '/'"
          />
        </div>

        <!-- 调整大小手柄 -->
        <div v-if="!isCodeEditorMaximized && !isCodeEditorMinimized" class="resize-handles">
          <!-- 右边缘 -->
          <div class="resize-handle resize-handle-right" @mousedown="startCodeEditorResize($event, 'right')"></div>
          <!-- 底边缘 -->
          <div class="resize-handle resize-handle-bottom" @mousedown="startCodeEditorResize($event, 'bottom')"></div>
          <!-- 左边缘 -->
          <div class="resize-handle resize-handle-left" @mousedown="startCodeEditorResize($event, 'left')"></div>
          <!-- 顶边缘 -->
          <div class="resize-handle resize-handle-top" @mousedown="startCodeEditorResize($event, 'top')"></div>
          <!-- 右下角 -->
          <div class="resize-handle resize-handle-bottom-right" @mousedown="startCodeEditorResize($event, 'bottom-right')"></div>
          <!-- 左下角 -->
          <div class="resize-handle resize-handle-bottom-left" @mousedown="startCodeEditorResize($event, 'bottom-left')"></div>
          <!-- 右上角 -->
          <div class="resize-handle resize-handle-top-right" @mousedown="startCodeEditorResize($event, 'top-right')"></div>
          <!-- 左上角 -->
          <div class="resize-handle resize-handle-top-left" @mousedown="startCodeEditorResize($event, 'top-left')"></div>
        </div>


      </div>
    </div>

    <!-- 新建目录弹窗 -->
    <CreateDirectoryModal
      v-model="createDirModalVisible"
      :client-id="clientId"
      :current-path="currentPath"
      @create-complete="handleCreateComplete"
    />

    <!-- 新建文件弹窗 -->
    <CreateFileModal
      v-model="createFileModalVisible"
      :client-id="clientId"
      :current-path="currentPath"
      @create-complete="handleCreateComplete"
    />

    <!-- 重命名弹窗 -->
    <RenameModal
      v-model="renameModalVisible"
      :client-id="clientId"
      :current-path="currentPath"
      :rename-file="renameFile"
      @rename-complete="handleRenameComplete"
    />

    <!-- 传输任务管理弹窗 -->
    <FileTransferModal
      ref="fileTransferModalRef"
      v-model="transferTasksVisible"
    />

    <!-- 文件属性弹窗 -->
    <FilePropertiesModal
      v-model="propertiesModalVisible"
      :file="currentPropertiesFile"
      :client-id="clientId"
      :current-path="currentPath"
    />

    <!-- 文件预览弹窗 -->
    <FilePreviewModal
      v-model:visible="previewVisible"
      :file="currentPreviewFile"
      :client-id="clientId"
      :current-path="currentPath"
      @download="(file) => downloadFile(file, currentPath, fileTransferModalRef)"
    />
</div>

    <!-- 右键菜单组件 -->
     <FileContextMenu
       v-model:visible="contextMenuVisible"
       :position="contextMenuPosition"
       :target-file="contextMenuTarget"
       v-model:clipboard="clipboard"
       :client-id="clientId"
       :current-path="currentPath"
       @paste-files="() => pasteFiles(currentPath)"
       @rename-file="startRename"
       @delete-file="(file) => deleteFile(file, currentPath)"
       @edit-file="editFile"
       @refresh-list="refreshFileList"
       @show-properties="showFileProperties"
     />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, onBeforeUnmount, nextTick } from 'vue';
import CodeEditor from './File/Editor/CodeEditor.vue';
import { useFileOperations } from './File/useFileOperations.js';
import { useFileNavigation } from './File/useFileNavigation.js';
import { useFileSelection } from './File/useFileSelection.js';
import { useFileManagerStore } from './File/fileManagerStore.js';
import FileUploadModal from './File/FileUploadModal.vue';
import FileTransferModal from './File/FileTransferModal.vue';
import CreateDirectoryModal from './File/CreateDirectoryModal.vue';
import CreateFileModal from './File/CreateFileModal.vue';
import RenameModal from './File/RenameModal.vue';
import DiskSelector from './File/DiskSelector.vue';
import BreadcrumbNavigation from './File/BreadcrumbNavigation.vue';
import FileContextMenu from './File/FileContextMenu.vue';
import FileManagerToolbar from './File/FileManagerToolbar.vue';
import BatchOperationsBar from './File/BatchOperationsBar.vue';
import FileTable from './File/FileTable.vue';
import FilePropertiesModal from './File/FilePropertiesModal.vue';
import FilePreviewModal from './File/FilePreviewModal.vue';
import {
  FILE_COLUMNS,
} from './File/constants.js';

// 接收属性
const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  }
});
// 注册组件
const components = {
  FileUploadModal,
  FileTransferModal,
  CreateDirectoryModal,
  RenameModal,
  DiskSelector,
  BreadcrumbNavigation,
  FileContextMenu,
  FileManagerToolbar,
  BatchOperationsBar,
  FileTable,
  FilePropertiesModal,
  FilePreviewModal
};

// 使用文件管理器状态管理
const {
  loading,
  fileList,
  clientInfo,
  showHidden,
  currentPage,
  pageSize,
  totalCount,
  totalPages,
  hasMore,
  sortBy,
  sortOrder,
  currentDisk,
  contextMenuVisible,
  contextMenuPosition,
  contextMenuTarget,
  uploadModalVisible,
  createDirModalVisible,
  createFileModalVisible,
  renameModalVisible,
  transferTasksVisible,
  renameFile,
  fileTransferModalRef,
  toolbarRef,
  getFileList: storeGetFileList,
  fetchClientInfo,
  handleShowHiddenChange: storeHandleShowHiddenChange,
  setPageSize,
  setSorting,
  goToPage,
  nextPage,
  prevPage,
  showContextMenu,
  startRename,
  showTransferTasks,
  handleUploadComplete: storeHandleUploadComplete,
  handleCreateComplete: storeHandleCreateComplete,
  handleRenameComplete: storeHandleRenameComplete
} = useFileManagerStore();

// 获取目录列表（使用 store 中的方法）
const getFileList = async (path = currentPath.value, page = null) => {
  await storeGetFileList(path, props.clientId, currentPath, page);
};

// 刷新文件列表
const refreshFileList = () => {
  getFileList();
};

// 分页事件处理
const handlePageChange = (page) => {
  goToPage(page);
  getFileList(currentPath.value, page);
};

// 页面大小变化处理
const handlePageSizeChange = (size) => {
  setPageSize(size);
  getFileList();
};

// 排序变化处理
const handleSortChange = () => {
  setSorting(sortBy.value, sortOrder.value);
  getFileList();
};

// 使用文件选择组合式函数
const {
  selectedFiles,
  onSelectChange,
  clearSelection,
} = useFileSelection();

// 使用文件操作组合式函数
const {
  clipboard,
  copySelectedFiles,
  cutSelectedFiles,
  pasteFiles,
  deleteFile,
  confirmDeleteSelected,
  downloadFile,
  handleMenuClick
} = useFileOperations(props, { getFileList, refreshFileList, clearSelection });

// 监听 clipboard 状态变化
watch(clipboard, (newClipboard) => {
  // 剪贴板状态变化处理
}, { deep: true });

// 使用文件导航组合式函数
const {
  currentPath,
  pathHistory,
  enterDirectory: originalEnterDirectory,
  goBack: originalGoBack,
  handleNavigateToPath: originalHandleNavigateToPath,
  handleNavigateToSegment: originalHandleNavigateToSegment,
  handleDiskChanged: originalHandleDiskChanged
} = useFileNavigation(props, { getFileList, currentDisk });

// 包装导航函数，确保重置分页状态
const enterDirectory = (path) => {
  goToPage(1); // 重置到第一页
  originalEnterDirectory(path);
};

const goBack = () => {
  goToPage(1); // 重置到第一页
  originalGoBack();
};

const handleNavigateToPath = (path) => {
  goToPage(1); // 重置到第一页
  originalHandleNavigateToPath(path);
};

const handleNavigateToSegment = (path) => {
  goToPage(1); // 重置到第一页
  originalHandleNavigateToSegment(path);
};

const handleDiskChanged = (disk) => {
  goToPage(1); // 重置到第一页
  originalHandleDiskChanged(disk);
};

// 表格列定义（从常量文件导入）
const fileColumns = FILE_COLUMNS;

// 处理显示隐藏文件切换
const handleShowHiddenChange = (checked) => {
  goToPage(1); // 重置到第一页
  storeHandleShowHiddenChange(checked, getFileList);
};

// 右键菜单事件处理函数已移动到FileContextMenu组件中

const handleMenuClickWrapper = async (key, record) => {
  await handleMenuClick(key, record, currentPath.value, startRename);
};

// 处理重命名完成
const handleRenameComplete = () => {
  storeHandleRenameComplete(refreshFileList);
};

// 处理创建目录完成
const handleCreateComplete = () => {
  storeHandleCreateComplete(refreshFileList);
};

// 编辑文件
const editFile = async (file) => {
  // 移除文件类型限制，所有文件都使用VSCode风格编辑器
  console.log('编辑文件:', file.name);

  // 设置当前编辑的文件信息
  currentEditingFile.value = file;
  currentEditingPath.value = currentPath.value;

  // 所有文件都使用VSCode风格编辑器，不再区分文件类型
  codeEditorVisible.value = true;
};

// 预览文件状态
const previewVisible = ref(false);
const currentPreviewFile = ref(null);

// 预览文件
const previewFile = async (file) => {
  console.log('预览文件:', file.name);

  // 设置当前预览的文件信息
  currentPreviewFile.value = file;
  previewVisible.value = true;
};



// 处理上传完成
const handleUploadComplete = () => {
  storeHandleUploadComplete(refreshFileList);
};

// 打开代码编辑器
const openCodeEditor = () => {
  // 以当前磁盘为根目录打开代码编辑器
  const rootPath = currentDisk.value?.path || '/';
  codeEditorVisible.value = true;

  // 设置编辑器的初始路径为当前磁盘根目录
  nextTick(() => {
    // 这里可以传递初始路径给CodeEditor组件
    console.log('打开代码编辑器，根目录:', rootPath);
  });
};

// 关闭代码编辑器
const closeCodeEditor = () => {
  codeEditorVisible.value = false;
  // 重置窗口状态
  isCodeEditorMaximized.value = false;
  isCodeEditorMinimized.value = false;
  isCodeEditorDragging.value = false;
  isCodeEditorResizing.value = false;
};

// 代码编辑器拖拽相关方法
const startCodeEditorDrag = (event) => {
  if (isCodeEditorMaximized.value || isCodeEditorMinimized.value) return;

  isCodeEditorDragging.value = true;
  codeEditorDragStart.value = {
    x: event.clientX,
    y: event.clientY,
    windowX: codeEditorPosition.value.x,
    windowY: codeEditorPosition.value.y
  };

  // 使用高频率事件监听，确保实时跟随
  document.addEventListener('mousemove', handleCodeEditorDrag);
  document.addEventListener('mouseup', stopCodeEditorDrag);

  // 禁用文本选择和其他默认行为
  document.body.style.userSelect = 'none';
  document.body.style.webkitUserSelect = 'none';

  event.preventDefault();
  event.stopPropagation();
};

const handleCodeEditorDrag = (event) => {
  if (!isCodeEditorDragging.value) return;

  // 直接更新位置，不使用 requestAnimationFrame，确保实时跟随鼠标
  const deltaX = event.clientX - codeEditorDragStart.value.x;
  const deltaY = event.clientY - codeEditorDragStart.value.y;

  const newX = Math.max(0, Math.min(window.innerWidth - codeEditorSize.value.width, codeEditorDragStart.value.windowX + deltaX));
  const newY = Math.max(0, Math.min(window.innerHeight - codeEditorSize.value.height, codeEditorDragStart.value.windowY + deltaY));

  codeEditorPosition.value = { x: newX, y: newY };
};

const stopCodeEditorDrag = () => {
  isCodeEditorDragging.value = false;
  document.removeEventListener('mousemove', handleCodeEditorDrag);
  document.removeEventListener('mouseup', stopCodeEditorDrag);

  // 恢复文本选择
  document.body.style.userSelect = '';
  document.body.style.webkitUserSelect = '';
};

// 代码编辑器调整大小相关方法
const startCodeEditorResize = (event, direction) => {
  if (isCodeEditorMaximized.value || isCodeEditorMinimized.value) return;

  isCodeEditorResizing.value = true;
  codeEditorResizeDirection.value = direction;
  codeEditorResizeStart.value = {
    x: event.clientX,
    y: event.clientY,
    width: codeEditorSize.value.width,
    height: codeEditorSize.value.height,
    windowX: codeEditorPosition.value.x,
    windowY: codeEditorPosition.value.y
  };

  document.addEventListener('mousemove', handleCodeEditorResize);
  document.addEventListener('mouseup', stopCodeEditorResize);

  // 禁用文本选择
  document.body.style.userSelect = 'none';
  document.body.style.webkitUserSelect = 'none';

  event.preventDefault();
  event.stopPropagation();
};

const handleCodeEditorResize = (event) => {
  if (!isCodeEditorResizing.value) return;

  const deltaX = event.clientX - codeEditorResizeStart.value.x;
  const deltaY = event.clientY - codeEditorResizeStart.value.y;
  const direction = codeEditorResizeDirection.value;

  let newWidth = codeEditorResizeStart.value.width;
  let newHeight = codeEditorResizeStart.value.height;
  let newX = codeEditorResizeStart.value.windowX;
  let newY = codeEditorResizeStart.value.windowY;

  // 最小尺寸限制
  const minWidth = 400;
  const minHeight = 300;

  // 根据调整方向计算新的尺寸和位置
  switch (direction) {
    case 'right':
      newWidth = Math.max(minWidth, codeEditorResizeStart.value.width + deltaX);
      break;
    case 'left':
      const proposedWidth = codeEditorResizeStart.value.width - deltaX;
      if (proposedWidth >= minWidth) {
        newWidth = proposedWidth;
        newX = codeEditorResizeStart.value.windowX + deltaX;
      }
      break;
    case 'bottom':
      newHeight = Math.max(minHeight, codeEditorResizeStart.value.height + deltaY);
      break;
    case 'top':
      const proposedHeight = codeEditorResizeStart.value.height - deltaY;
      if (proposedHeight >= minHeight) {
        newHeight = proposedHeight;
        newY = codeEditorResizeStart.value.windowY + deltaY;
      }
      break;
    case 'bottom-right':
      newWidth = Math.max(minWidth, codeEditorResizeStart.value.width + deltaX);
      newHeight = Math.max(minHeight, codeEditorResizeStart.value.height + deltaY);
      break;
    case 'bottom-left':
      const proposedWidthBL = codeEditorResizeStart.value.width - deltaX;
      if (proposedWidthBL >= minWidth) {
        newWidth = proposedWidthBL;
        newX = codeEditorResizeStart.value.windowX + deltaX;
      }
      newHeight = Math.max(minHeight, codeEditorResizeStart.value.height + deltaY);
      break;
    case 'top-right':
      newWidth = Math.max(minWidth, codeEditorResizeStart.value.width + deltaX);
      const proposedHeightTR = codeEditorResizeStart.value.height - deltaY;
      if (proposedHeightTR >= minHeight) {
        newHeight = proposedHeightTR;
        newY = codeEditorResizeStart.value.windowY + deltaY;
      }
      break;
    case 'top-left':
      const proposedWidthTL = codeEditorResizeStart.value.width - deltaX;
      const proposedHeightTL = codeEditorResizeStart.value.height - deltaY;
      if (proposedWidthTL >= minWidth) {
        newWidth = proposedWidthTL;
        newX = codeEditorResizeStart.value.windowX + deltaX;
      }
      if (proposedHeightTL >= minHeight) {
        newHeight = proposedHeightTL;
        newY = codeEditorResizeStart.value.windowY + deltaY;
      }
      break;
  }

  // 确保窗口不超出屏幕边界
  newX = Math.max(0, Math.min(window.innerWidth - newWidth, newX));
  newY = Math.max(0, Math.min(window.innerHeight - newHeight, newY));

  codeEditorSize.value = { width: newWidth, height: newHeight };
  codeEditorPosition.value = { x: newX, y: newY };
};

const stopCodeEditorResize = () => {
  isCodeEditorResizing.value = false;
  codeEditorResizeDirection.value = '';

  // 恢复文本选择
  document.body.style.userSelect = '';
  document.body.style.webkitUserSelect = '';

  document.removeEventListener('mousemove', handleCodeEditorResize);
  document.removeEventListener('mouseup', stopCodeEditorResize);
};

// 代码编辑器窗口控制方法
const minimizeCodeEditor = () => {
  isCodeEditorMinimized.value = !isCodeEditorMinimized.value;
  if (isCodeEditorMaximized.value) {
    isCodeEditorMaximized.value = false;
  }
};

const toggleCodeEditorMaximize = () => {
  if (isCodeEditorMaximized.value) {
    // 恢复到最大化前的状态
    codeEditorPosition.value = { ...beforeCodeEditorMaximize.value };
    codeEditorSize.value = {
      width: beforeCodeEditorMaximize.value.width,
      height: beforeCodeEditorMaximize.value.height
    };
    isCodeEditorMaximized.value = false;
  } else {
    // 保存当前状态
    beforeCodeEditorMaximize.value = {
      x: codeEditorPosition.value.x,
      y: codeEditorPosition.value.y,
      width: codeEditorSize.value.width,
      height: codeEditorSize.value.height
    };
    isCodeEditorMaximized.value = true;
  }

  if (isCodeEditorMinimized.value) {
    isCodeEditorMinimized.value = false;
  }
};

// 文件属性弹窗状态
const propertiesModalVisible = ref(false);
const currentPropertiesFile = ref(null);

// VSCode风格代码编辑器状态
const codeEditorVisible = ref(false);
const currentEditingFile = ref(null);
const currentEditingPath = ref('');

// 代码编辑器窗口相关
const codeEditorWindow = ref(null);
const isCodeEditorDragging = ref(false);
const isCodeEditorResizing = ref(false);
const codeEditorResizeDirection = ref('');
const isCodeEditorMaximized = ref(false);
const isCodeEditorMinimized = ref(false);

// 代码编辑器窗口位置和大小 - 纵向更长，横向更短
const codeEditorPosition = ref({
  x: 100,
  y: 30
});

const codeEditorSize = ref({
  width: 1000,  // 缩短横向长度（原来是75%可能很宽）
  height: 1000   // 增加纵向长度，使纵向比横向长
});

// 保存最大化前的状态
const beforeCodeEditorMaximize = ref({
  x: 100,
  y: 30,
  width: 1000,
  height: 1000
});

// 拖拽起始位置
const codeEditorDragStart = ref({
  x: 0,
  y: 0,
  windowX: 0,
  windowY: 0
});

// 调整大小起始状态
const codeEditorResizeStart = ref({
  x: 0,
  y: 0,
  width: 0,
  height: 0,
  windowX: 0,
  windowY: 0
});

// 显示文件属性
const showFileProperties = (file) => {
  currentPropertiesFile.value = file;
  propertiesModalVisible.value = true;
};

// 代码编辑器窗口样式计算属性
const codeEditorWindowStyle = computed(() => {
  if (isCodeEditorMaximized.value) {
    return {
      position: 'fixed',
      top: '0px',
      left: '0px',
      width: '100vw',
      height: '100vh',
      zIndex: 9999
    };
  }

  if (isCodeEditorMinimized.value) {
    return {
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      width: '300px',
      height: '40px',
      zIndex: 9999
    };
  }

  return {
    position: 'fixed',
    top: `${codeEditorPosition.value.y}px`,
    left: `${codeEditorPosition.value.x}px`,
    width: `${codeEditorSize.value.width}px`,
    height: `${codeEditorSize.value.height}px`,
    zIndex: 9999
  };
});

// 监听客户端状态变化
watch(() => clientInfo.value?.status, async (newStatus, oldStatus) => {
  if (newStatus === 1 && oldStatus !== 1) {
    // 通过ref获取DiskSelector组件的getDiskList方法
    if (toolbarRef.value) {
      await toolbarRef.value.getDiskList();
    }
    getFileList();
  }
});

// 监听clientId变化，重新获取客户端信息
watch(() => props.clientId, async (newClientId) => {
  if (newClientId) {
    await fetchClientInfo(props.clientId);
    if (clientInfo.value?.status === 1) {
      // 通过ref获取DiskSelector组件的getDiskList方法
      if (toolbarRef.value) {
        await toolbarRef.value.getDiskList();
      }
      getFileList();
    }
  }
}, { immediate: true });

// 组件挂载时获取客户端信息和文件列表
onMounted(async () => {
  await fetchClientInfo(props.clientId);
  if (clientInfo.value?.status === 1) {
    getFileList();
  }
});

// 组件卸载时清理事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleCodeEditorDrag);
  document.removeEventListener('mouseup', stopCodeEditorDrag);
  document.removeEventListener('mousemove', handleCodeEditorResize);
  document.removeEventListener('mouseup', stopCodeEditorResize);
});
</script>

<style scoped>
.file-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fafafa;
  border-radius: 8px;
  overflow: hidden;
}

.file-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h3 {
  margin: 0;
  color: #262626;
  font-weight: 600;
}



.file-manager-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #fff;
  margin: 0 8px 8px 8px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

/* 分页容器样式 */
.file-pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 0 0 6px 6px;
}

.pagination-info {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

.file-count {
  font-weight: 500;
}

.sort-controls {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

/* 分页组件样式优化 */
:deep(.ant-pagination) {
  margin: 0;
}

:deep(.ant-pagination-item) {
  border-radius: 4px;
}

:deep(.ant-pagination-item-active) {
  background: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-pagination-item-active a) {
  color: #fff;
}

:deep(.ant-select-selector) {
  border-radius: 4px;
}

/* 按钮样式优化 */
:deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.ant-btn-primary) {
  background: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 输入框样式优化 */
:deep(.ant-input) {
  border-radius: 6px;
}

/* 标签样式 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* 下拉菜单样式 */
:deep(.ant-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0;
  border: none;
  background: transparent;
}

:deep(.ant-dropdown-menu-item) {
  padding: 0;
  margin: 0;
  border: none;
}

/* 代码编辑器弹窗样式优化 */
:deep(.ant-modal-body) {
  padding: 12px;
}

/* 代码编辑器专用弹窗样式 */
:deep(.code-editor-modal .ant-modal) {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  border: none;
}

:deep(.code-editor-modal .ant-modal-content) {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  border: none;
}

:deep(.code-editor-modal .ant-modal-header) {
  background: #3c3c3c;
  border-bottom: 1px solid #4a4a4a;
  border-radius: 8px 8px 0 0;
  padding: 12px 16px;
  border-left: none;
  border-right: none;
  border-top: none;
}

:deep(.code-editor-modal .ant-modal-title) {
  color: #e8e8e8;
  font-size: 14px;
  font-weight: 500;
}

:deep(.code-editor-modal .ant-modal-close) {
  color: #b0b0b0;
}

:deep(.code-editor-modal .ant-modal-close:hover) {
  color: #e8e8e8;
  background: #4a4a4a;
}

:deep(.code-editor-modal .ant-modal-body) {
  padding: 0;
  background: #2d2d30;
  height: 75vh;
  overflow: hidden;
  border: none;
}

:deep(.ant-dropdown-menu-item:hover) {
  background-color: transparent;
}

/* 确保科技感菜单不受Ant Design样式影响 */
:deep(.ant-dropdown) {
  z-index: 9999;
}

:deep(.ant-dropdown .tech-disk-menu) {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  min-width: 400px;
  backdrop-filter: blur(20px);
}

/* 可拖拽代码编辑器样式 */
.draggable-code-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  pointer-events: none; /* 允许点击穿透到背景 */
}

.draggable-code-editor-window {
  background: #1e1e1e;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  border: 1px solid #3c3c3c;
  /* 硬件加速优化 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.draggable-code-editor-window.maximized {
  border-radius: 0;
  box-shadow: none;
}

.draggable-code-editor-window.minimized {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.draggable-code-editor-window.dragging {
  user-select: none;
  cursor: move;
  will-change: transform;
  transform: translateZ(0); /* 启用硬件加速 */
  transition: none !important; /* 拖拽时禁用所有过渡动画 */
  pointer-events: none; /* 拖拽时禁用子元素的指针事件 */
}

.draggable-code-editor-window.dragging * {
  pointer-events: none; /* 确保拖拽时子元素不干扰 */
}

.draggable-code-editor-window.dragging .code-editor-title-bar {
  pointer-events: auto; /* 标题栏保持可交互 */
}

.draggable-code-editor-window.resizing {
  user-select: none;
  will-change: transform;
  transform: translateZ(0); /* 启用硬件加速 */
}

/* 调整大小手柄样式 */
.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  pointer-events: auto;
  z-index: 10;
}

/* 边缘手柄 */
.resize-handle-right {
  top: 8px;
  right: -4px;
  bottom: 8px;
  width: 8px;
  cursor: ew-resize;
}

.resize-handle-left {
  top: 8px;
  left: -4px;
  bottom: 8px;
  width: 8px;
  cursor: ew-resize;
}

.resize-handle-bottom {
  left: 8px;
  right: 8px;
  bottom: -4px;
  height: 8px;
  cursor: ns-resize;
}

.resize-handle-top {
  left: 8px;
  right: 8px;
  top: -4px;
  height: 8px;
  cursor: ns-resize;
}

/* 角落手柄 */
.resize-handle-bottom-right {
  bottom: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  cursor: nw-resize;
}

.resize-handle-bottom-left {
  bottom: -4px;
  left: -4px;
  width: 12px;
  height: 12px;
  cursor: ne-resize;
}

.resize-handle-top-right {
  top: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  cursor: ne-resize;
}

.resize-handle-top-left {
  top: -4px;
  left: -4px;
  width: 12px;
  height: 12px;
  cursor: nw-resize;
}

/* 调整大小时的视觉反馈 */
.resize-handle:hover {
  background-color: rgba(24, 144, 255, 0.2);
}

.draggable-code-editor-window.resizing .resize-handle {
  background-color: rgba(24, 144, 255, 0.3);
}

/* 代码编辑器标题栏 */
.code-editor-title-bar {
  background: #2d2d30;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  border-bottom: 1px solid #3c3c3c;
  cursor: move;
  user-select: none;
}

.title-bar-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.window-icon {
  color: #cccccc;
  display: flex;
  align-items: center;
}

.editor-title {
  color: #cccccc;
  font-size: 13px;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.title-bar-right {
  display: flex;
  align-items: center;
  gap: 1px;
}

.title-bar-btn {
  width: 46px;
  height: 35px;
  border: none;
  background: transparent;
  color: #cccccc;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.1s ease;
}

.title-bar-btn:hover {
  background: #404040;
}

.close-btn:hover {
  background: #e81123;
  color: white;
}

/* 代码编辑器内容区域 */
.code-editor-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #1e1e1e;
}

/* 调整大小手柄 */
.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  pointer-events: auto;
}

.resize-handle-n {
  top: 0;
  left: 8px;
  right: 8px;
  height: 4px;
  cursor: n-resize;
}

.resize-handle-s {
  bottom: 0;
  left: 8px;
  right: 8px;
  height: 4px;
  cursor: s-resize;
}

.resize-handle-e {
  top: 8px;
  right: 0;
  bottom: 8px;
  width: 4px;
  cursor: e-resize;
}

.resize-handle-w {
  top: 8px;
  left: 0;
  bottom: 8px;
  width: 4px;
  cursor: w-resize;
}

.resize-handle-ne {
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  cursor: ne-resize;
}

.resize-handle-nw {
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  cursor: nw-resize;
}

.resize-handle-se {
  bottom: 0;
  right: 0;
  width: 8px;
  height: 8px;
  cursor: se-resize;
}

.resize-handle-sw {
  bottom: 0;
  left: 0;
  width: 8px;
  height: 8px;
  cursor: sw-resize;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .draggable-code-editor-window {
    width: 95vw !important;
    height: 90vh !important;
    top: 5vh !important;
    left: 2.5vw !important;
  }

  .editor-title {
    font-size: 12px;
  }

  .title-bar-btn {
    width: 40px;
    height: 32px;
  }

  .code-editor-title-bar {
    height: 32px;
  }
}

</style>