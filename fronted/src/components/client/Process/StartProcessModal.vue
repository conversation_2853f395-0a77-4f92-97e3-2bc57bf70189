<template>
  <a-modal
    :open="visible"
    title="启动新进程"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirm-loading="loading"
    width="600px"
    class="start-process-modal"
  >
    <template #title>
      <div class="modal-title">
        <PlayCircleOutlined class="title-icon" />
        <span>启动新进程</span>
      </div>
    </template>

    <div class="modal-content">
      <a-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        layout="vertical"
        class="process-form"
      >
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="程序路径" name="path" required>
              <a-input
                v-model:value="formData.path"
                placeholder="请输入可执行文件的完整路径"
                size="large"
                class="path-input"
              >
                <template #prefix>
                  <FileOutlined class="input-icon" />
                </template>
                <template #suffix>
                  <a-button 
                    type="text" 
                    size="small" 
                    @click="showPathHelper"
                    class="helper-btn"
                  >
                    <QuestionCircleOutlined />
                  </a-button>
                </template>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="命令行参数" name="args">
              <a-input
                v-model:value="formData.args"
                placeholder="请输入命令行参数（可选）"
                size="large"
                class="args-input"
              >
                <template #prefix>
                  <CodeOutlined class="input-icon" />
                </template>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="工作目录" name="workDir">
              <a-input
                v-model:value="formData.workDir"
                placeholder="请输入工作目录（可选，默认为程序所在目录）"
                size="large"
                class="workdir-input"
              >
                <template #prefix>
                  <FolderOutlined class="input-icon" />
                </template>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item name="runAsAdmin">
              <a-checkbox v-model:checked="formData.runAsAdmin" class="admin-checkbox">
                <div class="checkbox-content">
                  <SafetyCertificateOutlined class="checkbox-icon" />
                  <span class="checkbox-text">以管理员权限运行</span>
                  <a-tooltip title="某些程序可能需要管理员权限才能正常运行">
                    <InfoCircleOutlined class="info-icon" />
                  </a-tooltip>
                </div>
              </a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16" v-if="isWindows">
          <a-col :span="24">
            <a-form-item name="hideWindow">
              <a-checkbox v-model:checked="formData.hideWindow" class="admin-checkbox">
                <div class="checkbox-content">
                  <EyeInvisibleOutlined class="checkbox-icon" />
                  <span class="checkbox-text">隐藏窗口</span>
                  <a-tooltip title="启动进程时不显示窗口界面（仅Windows系统有效）">
                    <InfoCircleOutlined class="info-icon" />
                  </a-tooltip>
                </div>
              </a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 常用程序快捷选择 -->
      <div class="quick-select-section">
        <div class="section-title">
          <AppstoreOutlined class="section-icon" />
          <span>常用程序</span>
        </div>
        <div class="quick-programs">
          <a-button
            v-for="program in commonPrograms"
            :key="program.name"
            @click="selectProgram(program)"
            class="program-btn"
            size="small"
          >
            <component :is="program.icon" class="program-icon" />
            {{ program.name }}
          </a-button>
        </div>
      </div>
    </div>

    <!-- 路径帮助弹窗 -->
    <a-modal
      :open="pathHelperVisible"
      title="路径输入帮助"
      @ok="pathHelperVisible = false"
      @cancel="pathHelperVisible = false"
      :footer="null"
      width="500px"
    >
      <div class="path-helper">
        <h4>Windows 系统示例：</h4>
        <ul>
          <li><code>C:\\Windows\\System32\\notepad.exe</code> - 记事本</li>
          <li><code>C:\\Windows\\System32\\calc.exe</code> - 计算器</li>
          <li><code>C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe</code> - Chrome浏览器</li>
        </ul>
        
        <h4>Linux/Unix 系统示例：</h4>
        <ul>
          <li><code>/bin/bash</code> - Bash Shell</li>
          <li><code>/usr/bin/firefox</code> - Firefox浏览器</li>
          <li><code>/usr/bin/gedit</code> - 文本编辑器</li>
        </ul>
        
        <a-alert
          message="提示"
          description="请确保输入的路径是可执行文件的完整绝对路径，并且该文件在目标系统上存在。"
          type="info"
          show-icon
        />
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlayCircleOutlined,
  FileOutlined,
  CodeOutlined,
  FolderOutlined,
  SafetyCertificateOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined,
  AppstoreOutlined,
  WindowsOutlined,
  AppleOutlined,
  ChromeOutlined,
  FireOutlined,
  CalculatorOutlined,
  EditOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  clientInfo: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'submit']);

const loading = ref(false);
const formRef = ref();
const pathHelperVisible = ref(false);

// 表单数据
const formData = reactive({
  path: '',
  args: '',
  workDir: '',
  runAsAdmin: false,
  hideWindow: false
});

// 表单验证规则
const rules = {
  path: [
    { required: true, message: '请输入程序路径', trigger: 'blur' },
    { min: 1, message: '程序路径不能为空', trigger: 'blur' }
  ]
};

// 判断是否为Windows系统
const isWindows = computed(() => {
  return props.clientInfo?.os?.toLowerCase().includes('windows');
});

// 常用程序列表
const commonPrograms = computed(() => {
  const isWindowsOS = isWindows.value;
  
  if (isWindowsOS) {
    return [
      { name: '记事本', path: 'C:\\Windows\\System32\\notepad.exe', icon: EditOutlined },
      { name: '计算器', path: 'C:\\Windows\\System32\\calc.exe', icon: CalculatorOutlined },
      { name: 'CMD', path: 'C:\\Windows\\System32\\cmd.exe', icon: WindowsOutlined },
      { name: 'PowerShell', path: 'C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe', icon: WindowsOutlined }
    ];
  } else {
    return [
      { name: 'Bash', path: '/bin/bash', icon: FireOutlined },
      { name: 'Vi/Vim', path: '/usr/bin/vim', icon: EditOutlined },
      { name: 'Firefox', path: '/usr/bin/firefox', icon: FireOutlined },
      { name: 'Terminal', path: '/usr/bin/gnome-terminal', icon: WindowsOutlined }
    ];
  }
});

// 选择常用程序
const selectProgram = (program) => {
  formData.path = program.path;
};

// 显示路径帮助
const showPathHelper = () => {
  pathHelperVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;
    
    emit('submit', {
      command: formData.path,
      args: formData.args,
      workDir: formData.workDir,
      runAsAdmin: formData.runAsAdmin,
      hideWindow: formData.hideWindow
    });
    
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit('update:visible', false);
  // 重置表单
  Object.assign(formData, {
    path: '',
    args: '',
    workDir: '',
    runAsAdmin: false,
    hideWindow: false
  });
  formRef.value?.resetFields();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    path: '',
    args: '',
    workDir: '',
    runAsAdmin: false,
    hideWindow: false
  });
  formRef.value?.resetFields();
};

// 暴露方法给父组件
defineExpose({
  resetForm
});
</script>

<style scoped>
.start-process-modal :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.title-icon {
  color: #1890ff;
  font-size: 18px;
}

.modal-content {
  padding: 8px 0;
}

.process-form {
  margin-bottom: 24px;
}

.path-input,
.args-input,
.workdir-input {
  border-radius: 8px;
}

.input-icon {
  color: #bfbfbf;
}

.helper-btn {
  color: #1890ff;
  padding: 0;
}

.admin-checkbox {
  margin: 0;
}

.checkbox-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-icon {
  color: #fa8c16;
  font-size: 16px;
}

.checkbox-text {
  font-weight: 500;
}

.info-icon {
  color: #bfbfbf;
  font-size: 14px;
  cursor: help;
}

.quick-select-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #262626;
}

.section-icon {
  color: #722ed1;
  font-size: 16px;
}

.quick-programs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.program-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  background: #fafafa;
  transition: all 0.3s ease;
}

.program-btn:hover {
  border-color: #1890ff;
  background: #e6f7ff;
  color: #1890ff;
}

.program-icon {
  font-size: 14px;
}

.path-helper h4 {
  color: #262626;
  margin: 16px 0 8px 0;
}

.path-helper ul {
  margin: 0 0 16px 0;
  padding-left: 20px;
}

.path-helper li {
  margin-bottom: 4px;
}

.path-helper code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}
</style>