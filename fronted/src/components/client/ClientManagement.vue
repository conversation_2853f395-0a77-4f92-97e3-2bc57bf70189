<template>
  <div class="client-management">
    <div class="management-header">
      <div class="header-left">
        <h3>客户端管理 - ID: {{ clientId }}</h3>
        <div class="client-status-info" v-if="clientInfo">
          <a-tag :color="getStatusColor(clientInfo.status)" class="status-tag">
            <template #icon>
              <component :is="getStatusIcon(clientInfo.status)" />
            </template>
            {{ getStatusText(clientInfo.status) }}
          </a-tag>
          <span class="client-system">
            {{ clientInfo.os }} {{ clientInfo.arch }}
          </span>
        </div>
      </div>

      <div class="header-quotes">
        <TechQuotes />
      </div>
    </div>
    
    <a-tabs v-model:activeKey="activeTab" type="card">
      <a-tab-pane key="details" tab="会话详情">
        <client-details
          :client-id="clientId"
          :client-info="clientInfo"
          :active="activeTab === 'details'"
          @refresh="getClientInfo"
        />
      </a-tab-pane>

      <a-tab-pane key="terminal" tab="终端">
        <terminal-manager :client-id="clientId" />
      </a-tab-pane>
      
      <a-tab-pane key="file" tab="文件管理">
        <file-manager :client-id="clientId" />
      </a-tab-pane>
      
      <a-tab-pane key="process" tab="进程管理">
        <process-manager :client-id="clientId" />
      </a-tab-pane>
      
      <a-tab-pane key="screenshot" tab="屏幕截图">
        <screenshot-manager :client-id="clientId" :client-info="clientInfo" />
      </a-tab-pane>
      
      <a-tab-pane key="network" tab="网络监控">
        <network-monitor :client-id="clientId" />
      </a-tab-pane>
      
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import { clientApi } from '@/api'
import TechQuotes from '@/components/common/TechQuotes.vue'
import ClientDetails from './ClientDetails.vue'
import TerminalManager from './TerminalManager.vue'
import FileManager from './FileManager.vue'
import ProcessManager from './ProcessManager.vue'
import ScreenshotManager from './ScreenshotManager.vue'
import NetworkMonitor from './NetworkMonitor.vue'

const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  },
  client: {
    type: Object,
    default: () => ({})
  }
})

const activeTab = ref('details')
const loading = ref(false)
const clientInfo = ref(null)

// 获取客户端信息
const getClientInfo = async () => {
  if (!props.clientId) return
  
  try {
    loading.value = true
    const response = await clientApi.getClient(props.clientId)
    
    if (response.code === 200) {
      clientInfo.value = response.data
    } else {
      console.error('ClientManagement - 获取客户端信息失败:', response.msg)
    }
  } catch (error) {
    console.error('ClientManagement - 获取客户端信息异常:', error)
  } finally {
    loading.value = false
  }
}

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 1: return 'green'
    case 0: return 'red'
    default: return 'orange'
  }
}

// 获取状态图标
const getStatusIcon = (status) => {
  switch (status) {
    case 1: return CheckCircleOutlined
    case 0: return CloseCircleOutlined
    default: return ExclamationCircleOutlined
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 1: return '在线'
    case 0: return '离线'
    default: return '未知'
  }
}

// 保留刷新功能（如果需要的话）
const refreshData = () => {
  getClientInfo()
}

// 监听clientId变化
watch(() => props.clientId, (newClientId) => {
  if (newClientId) {
    getClientInfo()
  }
}, { immediate: true })

onMounted(() => {
  // 如果props.client存在，直接使用
  if (props.client && Object.keys(props.client).length > 0) {
    clientInfo.value = props.client
  } else {
    getClientInfo()
  }
})
</script>

<style scoped>
.client-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-left h3 {
  margin: 0;
  color: #1890ff;
}

.client-status-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.client-system {
  color: #666;
  font-size: 12px;
  background: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
}

.header-quotes {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 400px;
  max-width: 400px; /* 限制最大宽度 */
}

.header-quotes .tech-quotes {
  width: 100%;
  text-align: left;
}

</style>