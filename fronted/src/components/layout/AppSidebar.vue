<template>
  <a-layout-sider
    :collapsed="collapsed"
    :trigger="null"
    collapsible
    class="sidebar"
    @update:collapsed="updateCollapsed"
  >
    <div class="logo">
      <img src="@/assets/logo.svg" alt="Logo" />
      <h1 v-if="!collapsed">管理系统</h1>
    </div>
    <a-menu
      :selectedKeys="props.selectedKeys"
      theme="dark"
      mode="inline"
      @select="handleMenuSelect"
    >
      <a-menu-item key="1">
        <template #icon>
          <DashboardOutlined />
        </template>
        <span>仪表盘</span>
      </a-menu-item>
      <a-menu-item key="2">
        <template #icon>
          <ApiOutlined />
        </template>
        <span>监听器管理</span>
      </a-menu-item>
      <a-menu-item key="3">
        <template #icon>
          <ClientIcon />
        </template>
        <span>客户端管理</span>
      </a-menu-item>
      <a-sub-menu key="proxy" title="代理管理">
        <template #icon>
          <NodeIndexOutlined />
        </template>
        <a-menu-item key="proxy-index">
          <template #icon>
            <ApiOutlined />
          </template>
          <span>代理实例</span>
        </a-menu-item>
        <a-menu-item key="proxy-chain">
          <template #icon>
            <LinkOutlined />
          </template>
          <span>代理链</span>
        </a-menu-item>
        <a-menu-item key="proxy-monitor">
          <template #icon>
            <MonitorOutlined />
          </template>
          <span>监控统计</span>
        </a-menu-item>
      </a-sub-menu>
      <a-menu-item key="4">
        <template #icon>
          <DownloadOutlined />
        </template>
        <span>下载/上传管理</span>
      </a-menu-item>
      <a-menu-item key="5">
        <template #icon>
          <PictureOutlined />
        </template>
        <span>截图库</span>
      </a-menu-item>
      <a-menu-item key="6" v-if="userInfo.isAdmin">
        <template #icon>
          <UserOutlined />
        </template>
        <span>用户管理</span>
      </a-menu-item>
      <a-menu-item key="7">
        <template #icon>
          <FileTextOutlined />
        </template>
        <span>日志监控</span>
      </a-menu-item>
      <a-menu-item key="8">
        <template #icon>
          <BarChartOutlined />
        </template>
        <span>性能监控</span>
      </a-menu-item>
      <a-menu-item key="9">
        <template #icon>
          <SettingOutlined />
        </template>
        <span>系统设置</span>
      </a-menu-item>
    </a-menu>
  </a-layout-sider>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { DashboardOutlined, SettingOutlined, ApiOutlined, DownloadOutlined, PictureOutlined, UserOutlined, NodeIndexOutlined, LinkOutlined, MonitorOutlined, FileTextOutlined, BarChartOutlined } from '@ant-design/icons-vue';
import ClientIcon from '@/components/icons/ClientIcon.vue';
import * as authApi from '@/api/auth';

// 接收父组件传递的属性
const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  selectedKeys: {
    type: Array,
    default: () => ['1']
  }
});

// 定义事件
const emit = defineEmits(['update:collapsed', 'menu-select']);

// 获取路由实例
const router = useRouter();

// 用户信息
const userInfo = ref({
  isAdmin: false
});

// 处理菜单选择
const handleMenuSelect = ({ key }) => {
  // 发送菜单选择事件给父组件
  emit('menu-select', [key]);
  
  // 根据选择的菜单项导航到相应的路由
  switch(key) {
    case '1':
      router.push('/dashboard');
      break;
    case '2':
      router.push('/listener/index');
      break;
    case '3':
      router.push('/client/index');
      break;
    case 'proxy-index':
      router.push('/proxy/index');
      break;
    case 'proxy-chain':
      router.push('/proxy/chain');
      break;
    case 'proxy-monitor':
      router.push('/proxy/monitor');
      break;
    case '4':
      router.push('/download/index');
      break;
    case '5':
      router.push('/screenshot/index');
      break;
    case '6':
      router.push('/user-manage/index');
      break;
    case '7':
      router.push('/log/index');
      break;
    case '8':
      router.push('/performance/index');
      break;
    case '9':
      router.push('/settings/index');
      break;
  }
};

// 处理折叠状态更新
const updateCollapsed = (value) => {
  emit('update:collapsed', value);
};

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const response = await authApi.getUserInfo();
    if (response.code === 200) {
      userInfo.value = response.data;
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

// 组件挂载时获取用户信息
onMounted(() => {
  fetchUserInfo();
});
</script>

<style scoped lang="scss">
.logo {
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  overflow: hidden;
  padding: 0 16px;
  
  img {
    height: 32px;
    margin-right: 8px;
  }
  
  h1 {
    color: white;
    margin: 0;
    font-size: 18px;
    white-space: nowrap;
  }
}
</style>