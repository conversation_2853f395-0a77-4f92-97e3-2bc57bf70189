<template>
  <div class="workspace-manager">
    <!-- 标签页导航 -->
    <div class="workspace-tabs">
      <div 
        v-for="tab in tabs" 
        :key="tab.id"
        :class="['workspace-tab', { active: tab.id === activeTabId }]"
        @click="switchTab(tab.id)"
      >
        <span class="tab-title">{{ tab.title }}</span>
        <a-button 
          v-if="tab.closable" 
          type="text" 
          size="small" 
          class="tab-close"
          @click.stop="closeTab(tab.id)"
        >
          <template #icon>
            <CloseOutlined />
          </template>
        </a-button>
      </div>
    </div>
    
    <!-- 标签页内容 -->
    <div class="workspace-content">
      <div 
        v-for="tab in tabs" 
        :key="tab.id"
        v-show="tab.id === activeTabId"
        class="tab-content"
      >
        <component 
          :is="tab.component" 
          v-bind="tab.props"
          :active="tab.id === activeTabId"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import { useRoute, useRouter } from 'vue-router';

// 导入页面组件
import DashboardContent from '@/components/dashboard/DashboardContent.vue';
import ListenerContent from '@/components/listener/ListenerContent.vue';
import ClientContent from '@/components/client/ClientContent.vue';
import DownloadContent from '@/components/download/DownloadContent.vue';
import SettingsContent from '@/components/settings/SettingsContent.vue';
import ClientManagement from '@/components/client/ClientManagement.vue';
import ScreenshotLibrary from '@/views/screenshot/index.vue';
import UserManageContent from '@/views/userManage/index.vue';
import LogContent from '@/components/log/LogContent.vue';
import PerformanceContent from '@/components/performance/PerformanceContent.vue';
// 代理管理相关组件
import ProxyContent from '@/views/proxy/index.vue';
import ProxyChainContent from '@/views/proxy/chain.vue';
import ProxyMonitorContent from '@/views/proxy/monitor.vue';

const route = useRoute();
const router = useRouter();

// 标签页数据
const tabs = ref([
  {
    id: 'dashboard',
    title: '仪表盘',
    component: DashboardContent,
    props: {},
    closable: false, // 仪表盘不可关闭
    route: '/dashboard'
  }
]);

const activeTabId = ref('dashboard');

// 定义事件
const emit = defineEmits(['menu-select']);

// 页面组件映射
const pageComponents = {
  dashboard: DashboardContent,
  listener: ListenerContent,
  client: ClientContent,
  download: DownloadContent,
  screenshot: ScreenshotLibrary,
  'user-manage': UserManageContent,
  'log-monitor': LogContent,
  performance: PerformanceContent,
  settings: SettingsContent,
  'client-management': ClientManagement,
  // 代理管理组件
  proxy: ProxyContent,
  'proxy-chain': ProxyChainContent,
  'proxy-monitor': ProxyMonitorContent
};

// 路由到标签页的映射
const routeToTabMap = {
  '/dashboard': 'dashboard',
  '/listener/index': 'listener',
  '/client/index': 'client',
  '/download/index': 'download',
  '/screenshot/index': 'screenshot',
  '/user-manage/index': 'user-manage',
  '/log/index': 'log-monitor',
  '/performance/index': 'performance',
  '/settings/index': 'settings',
  // 代理管理路由映射
  '/proxy/index': 'proxy',
  '/proxy': 'proxy',
  '/proxy/chain': 'proxy-chain',
  '/proxy/monitor': 'proxy-monitor'
};

// 添加标签页
const addTab = (tabConfig) => {
  const existingTab = tabs.value.find(tab => tab.id === tabConfig.id);
  if (existingTab) {
    // 如果标签页已存在，切换到该标签页
    activeTabId.value = tabConfig.id;
    return;
  }
  
  tabs.value.push({
    id: tabConfig.id,
    title: tabConfig.title,
    component: pageComponents[tabConfig.component] || tabConfig.component,
    props: tabConfig.props || {},
    closable: tabConfig.closable !== false,
    route: tabConfig.route
  });
  
  activeTabId.value = tabConfig.id;
};

// 关闭标签页
const closeTab = (tabId) => {
  const tabIndex = tabs.value.findIndex(tab => tab.id === tabId);
  if (tabIndex === -1) return;
  
  const tab = tabs.value[tabIndex];
  if (!tab.closable) return;
  
  tabs.value.splice(tabIndex, 1);
  
  // 如果关闭的是当前活动标签页，切换到其他标签页
  if (activeTabId.value === tabId) {
    if (tabs.value.length > 0) {
      // 优先切换到前一个标签页，如果没有则切换到后一个
      const newActiveIndex = Math.max(0, tabIndex - 1);
      activeTabId.value = tabs.value[newActiveIndex].id;
      
      // 更新路由
      const newActiveTab = tabs.value[newActiveIndex];
      if (newActiveTab.route) {
        router.push(newActiveTab.route);
      }
    }
  }
};

// 切换标签页
const switchTab = (tabId) => {
  activeTabId.value = tabId;
  const tab = tabs.value.find(t => t.id === tabId);
  if (tab && tab.route) {
    router.push(tab.route);
  }
};

// 打开客户端管理页面
const openClientManagement = (clientId) => {
  const tabId = `client-management-${clientId}`;
  addTab({
    id: tabId,
    title: `客户端管理 ID:${clientId}`,
    component: 'client-management',
    props: { clientId },
    closable: true
  });
};

// 监听路由变化，自动添加对应的标签页
watch(() => route.path, (newPath) => {
  const tabId = routeToTabMap[newPath];
  if (tabId) {
    const existingTab = tabs.value.find(tab => tab.id === tabId);
    if (!existingTab) {
      // 根据路由添加对应的标签页
      const tabConfigs = {
        dashboard: { id: 'dashboard', title: '仪表盘', component: 'dashboard', closable: false, route: '/dashboard' },
        listener: { id: 'listener', title: '监听器管理', component: 'listener', closable: true, route: '/listener/index' },
        client: { id: 'client', title: '客户端管理', component: 'client', closable: true, route: '/client/index' },
        download: { id: 'download', title: '下载/上传管理', component: 'download', closable: true, route: '/download/index' },
        screenshot: { id: 'screenshot', title: '截图库', component: 'screenshot', closable: true, route: '/screenshot/index' },
        'user-manage': { id: 'user-manage', title: '用户管理', component: 'user-manage', closable: true, route: '/user-manage/index' },
        'log-monitor': { id: 'log-monitor', title: '日志监控', component: 'log-monitor', closable: true, route: '/log/index' },
        performance: { id: 'performance', title: '性能监控', component: 'performance', closable: true, route: '/performance/index' },
        settings: { id: 'settings', title: '系统设置', component: 'settings', closable: true, route: '/settings/index' },
        // 代理管理标签页配置
        proxy: { id: 'proxy', title: '代理管理', component: 'proxy', closable: true, route: '/proxy/index' },
        'proxy-chain': { id: 'proxy-chain', title: '代理链管理', component: 'proxy-chain', closable: true, route: '/proxy/chain' },
        'proxy-monitor': { id: 'proxy-monitor', title: '代理监控', component: 'proxy-monitor', closable: true, route: '/proxy/monitor' }
      };
      
      if (tabConfigs[tabId]) {
        addTab(tabConfigs[tabId]);
      }
    } else {
      activeTabId.value = tabId;
    }
    
    // 发送菜单选择事件
    const menuKeyMap = {
      dashboard: '1',
      listener: '2',
      client: '3',
      download: '4',
      screenshot: '5',
      'user-manage': '6',
      'log-monitor': '7',
      performance: '8',
      settings: '9',
      // 代理管理菜单键映射
      proxy: 'proxy-index',
      'proxy-chain': 'proxy-chain',
      'proxy-monitor': 'proxy-monitor'
    };
    if (menuKeyMap[tabId]) {
      emit('menu-select', [menuKeyMap[tabId]]);
    }
  }
}, { immediate: true });

// 处理路由变化的方法
const handleRouteChange = (newPath) => {
  // 这个方法主要是为了兼容父组件的调用，实际逻辑已经在watch中处理
  console.log('Route changed to:', newPath);
};

// 监听全局事件，处理打开工作区标签页
const handleOpenWorkspaceTab = (event) => {
  const { type, id, title, clientId, client } = event.detail;
  
  if (type === 'client-management') {
    addTab({
      id: id,
      title: title,
      component: 'client-management',
      props: { clientId, client },
      closable: true
    });
  }
};

// 组件挂载时添加事件监听器
onMounted(() => {
  window.addEventListener('openWorkspaceTab', handleOpenWorkspaceTab);
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('openWorkspaceTab', handleOpenWorkspaceTab);
});

// 暴露方法给父组件
defineExpose({
  addTab,
  closeTab,
  switchTab,
  openClientManagement,
  handleRouteChange
});
</script>

<style scoped>
.workspace-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.workspace-tabs {
  display: flex;
  background: #f5f5f5;
  border-bottom: 1px solid #d9d9d9;
  padding: 0;
  margin: 0;
  overflow-x: auto;
  flex-shrink: 0;
}

.workspace-tab {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #fafafa;
  border-right: 1px solid #d9d9d9;
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
  min-width: 120px;
  transition: all 0.2s;
}

.workspace-tab:hover {
  background: #e6f7ff;
}

.workspace-tab.active {
  background: #fff;
  border-bottom: 2px solid #1890ff;
  color: #1890ff;
}

.tab-title {
  flex: 1;
  margin-right: 8px;
}

.tab-close {
  opacity: 0;
  transition: opacity 0.2s;
}

.workspace-tab:hover .tab-close {
  opacity: 1;
}

.workspace-content {
  flex: 1;
  overflow: hidden;
}

.tab-content {
  height: 100%;
  overflow: auto;
}
</style>