<template>
  <div class="dashboard-container">
    <a-layout style="min-height: 100vh">
      <!-- 侧边栏 -->
      <AppSidebar 
        v-model:collapsed="collapsed" 
        v-model:selectedKeys="selectedKeys"
        @menu-select="handleMenuSelect"
      />

      <!-- 内容区 -->
      <a-layout>
        <!-- 头部 -->
        <AppHeader 
          v-model:collapsed="collapsed" 
          :selectedKeys="selectedKeys"
          :userInfo="userInfo"
          @update:userInfo="handleUserInfoUpdate"
        />

        <!-- 内容 -->
        <a-layout-content class="content">
          <slot></slot>
        </a-layout-content>

        <!-- 页脚 -->
        <AppFooter />
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { useRoute } from 'vue-router';
import AppSidebar from './AppSidebar.vue';
import AppHeader from './AppHeader.vue';
import AppFooter from './AppFooter.vue';

// 状态变量
const collapsed = ref(false);
const selectedKeys = ref(['1']);
const userInfo = reactive(JSON.parse(localStorage.getItem('userInfo') || '{}'));
const route = useRoute();

// 定义事件
const emit = defineEmits(['menu-select']);

// 处理菜单选择
const handleMenuSelect = (keys) => {
  selectedKeys.value = keys;
  emit('menu-select', keys);
};

// 处理用户信息更新
const handleUserInfoUpdate = (updatedUserInfo) => {
  // 更新本地状态
  Object.assign(userInfo, updatedUserInfo);

  // 更新本地存储
  localStorage.setItem('userInfo', JSON.stringify(userInfo));
};

// 根据当前路由更新选中的菜单项
const updateSelectedKeys = () => {
  const path = route.path;
  if (path === '/dashboard') {
    selectedKeys.value = ['1'];
  } else if (path.startsWith('/listener')) {
    selectedKeys.value = ['2'];
  } else if (path.startsWith('/client')) {
    selectedKeys.value = ['3'];
  } else if (path === '/proxy/index') {
    selectedKeys.value = ['proxy-index'];
  } else if (path === '/proxy/chain') {
    selectedKeys.value = ['proxy-chain'];
  } else if (path === '/proxy/monitor') {
    selectedKeys.value = ['proxy-monitor'];
  } else if (path.startsWith('/download')) {
    selectedKeys.value = ['4'];
  } else if (path.startsWith('/screenshot')) {
    selectedKeys.value = ['5'];
  } else if (path.startsWith('/user-manage')) {
    selectedKeys.value = ['6'];
  } else if (path.startsWith('/log')) {
    selectedKeys.value = ['7'];
  } else if (path.startsWith('/performance')) {
    selectedKeys.value = ['8'];
  } else if (path.startsWith('/settings')) {
    selectedKeys.value = ['9'];
  }
};

// 监听路由变化
watch(() => route.path, () => {
  updateSelectedKeys();
}, { immediate: true });
</script>

<style scoped lang="scss">
.dashboard-container {
  height: 100%;
}

.content {
  margin: 16px;
  overflow: auto;
}
</style>