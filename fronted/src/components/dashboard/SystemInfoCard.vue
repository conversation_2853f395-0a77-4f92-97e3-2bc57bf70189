<template>
  <a-card class="dashboard-card system-info-card" :loading="loading">
    <template #title>
      <div class="card-title">
        <DesktopOutlined />
        <span>系统信息</span>
      </div>
    </template>
    <div class="card-content">
      <!-- 系统 Logo 和主要信息 -->
      <div class="system-info-header">
        <div class="os-logo">
          <WindowsOutlined v-if="systemInfo?.os?.goos === 'windows'" />
          <AppleOutlined v-else-if="systemInfo?.os?.goos === 'darwin'" />
          <GlobalOutlined v-else-if="systemInfo?.os?.goos === 'linux'" />
          <DesktopOutlined v-else />
        </div>
        <div class="os-info">
          <div class="os-name">{{ getOSDisplayName() }}</div>
          <div class="os-version">{{ systemInfo?.os?.kernelVersion || '未知版本' }}</div>
          <div class="hostname">{{ systemInfo?.os?.hostname || '未知主机' }}</div>
        </div>
      </div>
      
      <!-- 详细系统信息 -->
      <div class="system-info-grid">
        <div class="system-info-item">
          <div class="info-label">CPU核心数</div>
          <div class="info-value">{{ systemInfo?.cpu?.cores || 0 }}</div>
          <ThunderboltOutlined class="info-icon cpu" />
        </div>
        <div class="system-info-item">
          <div class="info-label">系统架构</div>
          <div class="info-value">{{ systemInfo?.os?.arch || '未知' }}</div>
          <CodeOutlined class="info-icon arch" />
        </div>
        <div class="system-info-item">
          <div class="info-label">运行时间</div>
          <div class="info-value">{{ formatUptime(systemInfo?.os?.uptime) }}</div>
          <FieldTimeOutlined class="info-icon uptime" />
        </div>
        <div class="system-info-item">
          <div class="info-label">Go版本</div>
          <div class="info-value">{{ systemInfo?.os?.goVersion || '未知' }}</div>
          <CodeSandboxOutlined class="info-icon go" />
        </div>
        <div class="system-info-item">
          <div class="info-label">Goroutine数</div>
          <div class="info-value">{{ systemInfo?.os?.numGoroutine || 0 }}</div>
          <NodeIndexOutlined class="info-icon goroutine" />
        </div>
        <div class="system-info-item">
          <div class="info-label">编译器</div>
          <div class="info-value">{{ systemInfo?.os?.compiler || '未知' }}</div>
          <ToolOutlined class="info-icon compiler" />
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { 
  DesktopOutlined,
  WindowsOutlined,
  AppleOutlined,
  GlobalOutlined,
  ThunderboltOutlined,
  CodeOutlined,
  FieldTimeOutlined,
  CodeSandboxOutlined,
  NodeIndexOutlined,
  ToolOutlined
} from '@ant-design/icons-vue';

// 接收父组件传递的属性
const props = defineProps({
  systemInfo: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 获取操作系统显示名称
const getOSDisplayName = () => {
  const osType = props.systemInfo?.os?.goos;
  
  if (!osType) return '未知系统';
  
  switch (osType.toLowerCase()) {
    case 'windows':
      return 'Windows';
    case 'darwin':
      return 'macOS';
    case 'linux':
      return 'Linux';
    default:
      return osType;
  }
};

// 格式化系统运行时间
const formatUptime = (seconds) => {
  if (!seconds && seconds !== 0) return '未知';
  
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  let result = '';
  if (days > 0) result += `${days}天 `;
  if (hours > 0 || days > 0) result += `${hours}小时 `;
  result += `${minutes}分钟`;
  
  return result;
};
</script>

<style scoped lang="scss">
.dashboard-card {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  .card-title {
    display: flex;
    align-items: center;
    
    .anticon {
      font-size: 18px;
      transition: transform 0.3s ease;
    }
    
    span {
      margin-left: 8px;
      font-weight: 500;
    }
  }
  
  &:hover .card-title .anticon {
    transform: scale(1.2);
  }
  
  .card-content {
    display: flex;
    flex-direction: column;
    padding: 16px 0;
    position: relative;
    overflow: hidden;
  }
}

.system-info-card {
  margin-bottom: 16px;
}

.system-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  
  .os-logo {
    font-size: 48px;
    margin-right: 16px;
    color: #1890ff;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.2));
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(24, 144, 255, 0.2);
    }
    
    .anticon {
      font-size: 36px;
      transition: transform 0.3s ease;
    }
    
    &:hover .anticon {
      transform: scale(1.1);
    }
  }
  
  .os-info {
    flex: 1;
    
    .os-name {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 4px;
      color: #000000d9;
      display: flex;
      align-items: center;
      
      &::after {
        content: '';
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #52c41a;
        margin-left: 8px;
        box-shadow: 0 0 6px rgba(82, 196, 26, 0.6);
      }
    }
    
    .os-version {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 4px;
    }
    
    .hostname {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      display: flex;
      align-items: center;
      
      &::before {
        content: '🖥️';
        margin-right: 4px;
        font-size: 14px;
      }
    }
  }
}

.system-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  
  .system-info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 8px;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.02);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    &:hover {
      background-color: rgba(24, 144, 255, 0.05);
      transform: translateY(-2px);
    }
    
    .info-label {
      font-size: 10px;
      color: rgba(0, 0, 0, 0.45);
      margin-bottom: 8px;
      position: relative;
      z-index: 1;
    }
    
    .info-value {
      font-size: 9px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      position: relative;
      z-index: 1;
    }
    
    .info-icon {
      position: absolute;
      right: 8px;
      bottom: 8px;
      font-size: 24px;
      opacity: 0.15;
      color: #1890ff;
      transition: all 0.3s ease;
      
      &.cpu {
        color: #f5222d;
      }
      
      &.arch {
        color: #722ed1;
      }
      
      &.uptime {
        color: #13c2c2;
      }
      
      &.go {
        color: #1890ff;
      }
      
      &.goroutine {
        color: #fa8c16;
      }
      
      &.compiler {
        color: #52c41a;
      }
    }
    
    &:hover .info-icon {
      opacity: 0.3;
      transform: scale(1.2);
    }
  }
}
</style>