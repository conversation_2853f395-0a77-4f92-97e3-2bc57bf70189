<template>
  <div class="download-button-group">
    <!-- 单个文件下载按钮 -->
    <a-button
      v-if="!multiple"
      type="primary"
      size="small"
      :loading="isDownloading"
      :disabled="disabled"
      @click="handleSingleDownload"
    >
      <template #icon>
        <DownloadOutlined />
      </template>
      {{ buttonText }}
    </a-button>
    
    <!-- 多文件下载按钮组 -->
    <div v-else class="multiple-download">
      <a-button
        type="primary"
        size="small"
        :loading="isDownloading"
        :disabled="disabled || selectedFiles.length === 0"
        @click="handleMultipleDownload"
      >
        <template #icon>
          <DownloadOutlined />
        </template>
        下载选中 ({{ selectedFiles.length }})
      </a-button>
      
      <a-dropdown 
        v-if="selectedFiles.length > 0"
        @click="handleDropdownCommand"
        trigger="['click']"
      >
        <a-button size="small">
          更多选项
          <template #icon>
            <DownOutlined />
          </template>
        </a-button>
        <template #overlay>
          <a-menu @click="handleDropdownCommand">
            <a-menu-item key="download-all">下载全部</a-menu-item>
            <a-menu-item key="check-all" :disabled="isDownloading">
              检查文件状态
            </a-menu-item>
            <a-menu-item key="get-info" :disabled="isDownloading">
              获取文件信息
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import { downloadServerFile, downloadMultipleFiles, getFileInfo, checkFileExists } from '@/api/download'
import { DownloadOutlined, DownOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'

export default {
  name: 'DownloadButton',
  components: {
    DownloadOutlined,
    DownOutlined
  },
  props: {
    // 单文件模式的文件路径
    filePath: {
      type: String,
      default: ''
    },
    // 单文件模式的文件名
    fileName: {
      type: String,
      default: ''
    },
    // 多文件模式
    multiple: {
      type: Boolean,
      default: false
    },
    // 多文件模式下选中的文件列表
    selectedFiles: {
      type: Array,
      default: () => []
    },
    // 按钮文本（单文件模式）
    buttonText: {
      type: String,
      default: '下载'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 下载前是否检查文件
    checkBeforeDownload: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isDownloading: false,
      downloadIds: []
    }
  },
  methods: {
    /**
     * 处理单文件下载
     */
    async handleSingleDownload() {
      if (!this.filePath) {
        message.error('文件路径不能为空')
        return
      }

      this.isDownloading = true
      
      try {
        // 检查文件是否存在（如果启用）
        if (this.checkBeforeDownload) {
          const exists = await checkFileExists(this.filePath)
          if (!exists) {
            message.error('文件不存在或无法访问')
            return
          }
        }

        // 开始下载
        const downloadId = downloadServerFile(
          this.filePath,
          this.fileName,
          {
            onProgress: this.handleProgress,
            onComplete: this.handleComplete,
            onError: this.handleError
          }
        )
        
        this.downloadIds.push(downloadId)
        
        this.$emit('download-started', {
          downloadId,
          filePath: this.filePath,
          fileName: this.fileName
        })
        
        message.success('开始下载文件')
        
      } catch (error) {
        const errorMsg = error.response?.data?.error || error.response?.data?.message || error.message;
        message.error(`下载失败: ${errorMsg}`)
        this.handleError(errorMsg)
      } finally {
        this.isDownloading = false
      }
    },
    
    /**
     * 处理多文件下载
     */
    async handleMultipleDownload() {
      if (this.selectedFiles.length === 0) {
        message.warning('请先选择要下载的文件')
        return
      }

      this.isDownloading = true
      
      try {
        // 检查文件是否存在（如果启用）
        if (this.checkBeforeDownload) {
          const checkResults = await Promise.all(
            this.selectedFiles.map(file => checkFileExists(file.filePath))
          )
          
          const invalidFiles = this.selectedFiles.filter((file, index) => !checkResults[index])
          if (invalidFiles.length > 0) {
            message.warning(`以下文件不存在或无法访问: ${invalidFiles.map(f => f.fileName || f.filePath).join(', ')}`)
            return
          }
        }

        // 开始批量下载
        const downloadIds = downloadMultipleFiles(
          this.selectedFiles,
          {
            onProgress: this.handleMultipleProgress,
            onComplete: this.handleMultipleComplete,
            onError: this.handleMultipleError
          }
        )
        
        this.downloadIds.push(...downloadIds)
        
        this.$emit('download-started', {
          downloadIds,
          files: this.selectedFiles
        })
        
        message.success(`开始下载 ${this.selectedFiles.length} 个文件`)
        
      } catch (error) {
        const errorMsg = error.response?.data?.error || error.response?.data?.message || error.message;
        message.error(`批量下载失败: ${errorMsg}`)
        this.handleError(errorMsg)
      } finally {
        this.isDownloading = false
      }
    },
    
    /**
     * 处理下拉菜单命令
     */
    async handleDropdownCommand({ key }) {
      switch (key) {
        case 'download-all':
          await this.handleMultipleDownload()
          break
        case 'check-all':
          await this.checkAllFiles()
          break
        case 'get-info':
          await this.getFilesInfo()
          break
      }
    },
    
    /**
     * 检查所有选中文件的状态
     */
    async checkAllFiles() {
      this.isDownloading = true
      
      try {
        const results = await Promise.all(
          this.selectedFiles.map(async file => {
            const exists = await checkFileExists(file.filePath)
            return {
              filePath: file.filePath,
              fileName: file.fileName || file.filePath.split('/').pop(),
              exists
            }
          })
        )
        
        const existingFiles = results.filter(r => r.exists)
        const missingFiles = results.filter(r => !r.exists)
        
        let msg = `检查完成: ${existingFiles.length} 个文件可下载`
        if (missingFiles.length > 0) {
          msg += `, ${missingFiles.length} 个文件不存在`
        }
        
        message.success(msg)
        
        this.$emit('files-checked', { existingFiles, missingFiles })
        
      } catch (error) {
        message.error(`检查文件状态失败: ${error.response?.data?.error || error.response?.data?.message || error.message}`)
      } finally {
        this.isDownloading = false
      }
    },
    
    /**
     * 获取所有选中文件的详细信息
     */
    async getFilesInfo() {
      this.isDownloading = true
      
      try {
        const results = await Promise.all(
          this.selectedFiles.map(async file => {
            const info = await getFileInfo(file.filePath)
            return {
              filePath: file.filePath,
              fileName: file.fileName || file.filePath.split('/').pop(),
              ...info
            }
          })
        )
        
        this.$emit('files-info', results)
        
        // 显示总大小
        const totalSize = results
          .filter(r => r.exists)
          .reduce((sum, r) => sum + (r.size || 0), 0)
        
        const formatSize = (bytes) => {
          if (bytes === 0) return '0 B'
          const k = 1024
          const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
          const i = Math.floor(Math.log(bytes) / Math.log(k))
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        }
        
        message.success(`文件信息获取完成，总大小: ${formatSize(totalSize)}`)
        
      } catch (error) {
        message.error(`获取文件信息失败: ${error.response?.data?.error || error.response?.data?.message || error.message}`)
      } finally {
        this.isDownloading = false
      }
    },
    
    /**
     * 处理下载进度
     */
    handleProgress(progress) {
      this.$emit('download-progress', progress)
    },
    
    /**
     * 处理多文件下载进度
     */
    handleMultipleProgress(filePath, progress) {
      this.$emit('download-progress', { filePath, ...progress })
    },
    
    /**
     * 处理下载完成
     */
    handleComplete() {
      this.$emit('download-complete')
    },
    
    /**
     * 处理多文件下载完成
     */
    handleMultipleComplete(filePath) {
      this.$emit('download-complete', { filePath })
    },
    
    /**
     * 处理下载错误
     */
    handleError(error) {
      this.$emit('download-error', error)
    },
    
    /**
     * 处理多文件下载错误
     */
    handleMultipleError(filePath, error) {
      this.$emit('download-error', { filePath, error })
    }
  }
}
</script>

<style scoped>
.download-button-group {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.multiple-download {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>