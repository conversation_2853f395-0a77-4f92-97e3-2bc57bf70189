<template>
  <div class="download-progress">
    <!-- 下载任务列表 -->
    <div v-if="downloads.length > 0" class="download-list">
      <div class="download-header">
        <h3>下载管理器</h3>
        <div class="download-actions">
          <a-button 
            size="small" 
            @click="clearCompleted"
            :disabled="!hasCompleted"
          >
            清除已完成
          </a-button>
          <a-button 
            size="small" 
            @click="toggleMinimize"
          >
            <template #icon>
              <UpOutlined v-if="isMinimized" />
              <DownOutlined v-else />
            </template>
            {{ isMinimized ? '展开' : '收起' }}
          </a-button>
        </div>
      </div>
      
      <div v-show="!isMinimized" class="download-items">
        <div 
          v-for="download in downloads" 
          :key="download.id" 
          class="download-item"
          :class="`status-${download.status}`"
        >
          <!-- 文件信息 -->
          <div class="file-info">
            <div class="file-name" :title="download.fileName">
              <FileOutlined />
              {{ download.fileName }}
            </div>
            <div class="file-size">
              {{ formatFileSize(download.downloadedBytes) }} / 
              {{ formatFileSize(download.totalBytes) }}
            </div>
          </div>
          
          <!-- 进度条 -->
          <div class="progress-section">
            <a-progress 
              :percent="download.progress" 
              :status="getProgressStatus(download.status)"
              :show-info="false"
              :stroke-width="6"
            />
            <div class="progress-text">
              <span class="percentage">{{ Math.round(download.progress) }}%</span>
              <span v-if="download.status === 'downloading' && download.speed" class="speed">
                {{ formatSpeed(download.speed) }}
              </span>
            </div>
          </div>
          
          <!-- 状态和操作 -->
          <div class="download-controls">
            <div class="status-text">
              {{ getStatusText(download.status) }}
            </div>
            <div class="control-buttons">
              <!-- 暂停/恢复按钮 -->
              <a-button 
                v-if="download.status === 'downloading'"
                size="small"
                danger
                @click="pauseDownload(download.id)"
                shape="circle"
                title="暂停下载"
              >
                <template #icon>
                  <PauseOutlined />
                </template>
              </a-button>
              
              <a-button 
                v-if="download.status === 'paused'"
                size="small"
                type="primary"
                @click="resumeDownload(download.id)"
                shape="circle"
                title="恢复下载"
              >
                <template #icon>
                  <PlayCircleOutlined />
                </template>
              </a-button>
              
              <!-- 取消按钮 -->
              <a-button 
                v-if="['downloading', 'paused', 'pending'].includes(download.status)"
                size="small"
                danger
                @click="cancelDownload(download.id)"
                shape="circle"
                title="取消下载"
              >
                <template #icon>
                  <CloseOutlined />
                </template>
              </a-button>
              
              <!-- 重试按钮 -->
              <a-button 
                v-if="download.status === 'error'"
                size="small"
                type="primary"
                @click="retryDownload(download.id)"
                shape="circle"
                title="重试下载"
              >
                <template #icon>
                  <ReloadOutlined />
                </template>
              </a-button>
              
              <!-- 移除按钮 -->
              <a-button 
                v-if="['completed', 'error', 'cancelled'].includes(download.status)"
                size="small"
                @click="removeDownload(download.id)"
                shape="circle"
                title="移除任务"
              >
                <template #icon>
                  <DeleteOutlined />
                </template>
              </a-button>
            </div>
          </div>
          
          <!-- 错误信息 -->
          <div v-if="download.status === 'error' && download.errorMessage" class="error-message">
            <ExclamationCircleOutlined />
            {{ download.errorMessage }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 无下载任务时的提示 -->
    <div v-else class="no-downloads">
      <DownloadOutlined />
      <p>暂无下载任务</p>
    </div>
  </div>
</template>

<script>
import { downloadManager } from '@/utils/downloadManager'
import { Modal } from 'ant-design-vue'
import { 
  UpOutlined, 
  DownOutlined, 
  FileOutlined, 
  PauseOutlined, 
  PlayCircleOutlined, 
  CloseOutlined, 
  ReloadOutlined, 
  DeleteOutlined,
  ExclamationCircleOutlined,
  DownloadOutlined 
} from '@ant-design/icons-vue'

export default {
  name: 'DownloadProgress',
  components: {
    UpOutlined,
    DownOutlined,
    FileOutlined,
    PauseOutlined,
    PlayCircleOutlined,
    CloseOutlined,
    ReloadOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    DownloadOutlined
  },
  data() {
    return {
      downloads: [],
      isMinimized: false,
      updateTimer: null
    }
  },
  computed: {
    hasCompleted() {
      return this.downloads.some(d => d.status === 'completed')
    }
  },
  mounted() {
    this.startUpdateTimer()
  },
  beforeDestroy() {
    this.stopUpdateTimer()
  },
  methods: {
    /**
     * 开始更新定时器
     */
    startUpdateTimer() {
      this.updateDownloads()
      this.updateTimer = setInterval(() => {
        this.updateDownloads()
      }, 1000)
    },
    
    /**
     * 停止更新定时器
     */
    stopUpdateTimer() {
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    },
    
    /**
     * 更新下载列表
     */
    updateDownloads() {
      this.downloads = downloadManager.getAllDownloads()
    },
    
    /**
     * 暂停下载
     */
    pauseDownload(downloadId) {
      downloadManager.pauseDownload(downloadId)
      this.updateDownloads()
    },
    
    /**
     * 恢复下载
     */
    resumeDownload(downloadId) {
      downloadManager.resumeDownload(downloadId)
      this.updateDownloads()
    },
    
    /**
     * 取消下载
     */
    cancelDownload(downloadId) {
      Modal.confirm({
        title: '确认取消',
        content: '确定要取消这个下载任务吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          downloadManager.cancelDownload(downloadId)
          this.updateDownloads()
        }
      })
    },
    
    /**
     * 重试下载
     */
    retryDownload(downloadId) {
      const download = this.downloads.find(d => d.id === downloadId)
      if (download) {
        // 重新开始下载
        this.$emit('retry-download', {
          filePath: download.filePath,
          fileName: download.fileName
        })
        this.removeDownload(downloadId)
      }
    },
    
    /**
     * 移除下载任务
     */
    removeDownload(downloadId) {
      downloadManager.cancelDownload(downloadId)
      this.updateDownloads()
    },
    
    /**
     * 清除已完成的下载
     */
    clearCompleted() {
      const completedDownloads = this.downloads.filter(d => d.status === 'completed')
      completedDownloads.forEach(download => {
        downloadManager.cancelDownload(download.id)
      })
      this.updateDownloads()
    },
    
    /**
     * 切换最小化状态
     */
    toggleMinimize() {
      this.isMinimized = !this.isMinimized
    },
    
    /**
     * 获取进度条状态
     */
    getProgressStatus(status) {
      switch (status) {
        case 'completed':
          return 'success'
        case 'error':
        case 'cancelled':
          return 'exception'
        case 'downloading':
          return 'active'
        case 'paused':
          return 'normal'
        default:
          return 'normal'
      }
    },
    
    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        pending: '等待中',
        downloading: '下载中',
        paused: '已暂停',
        completed: '已完成',
        error: '下载失败',
        cancelled: '已取消'
      }
      return statusMap[status] || '未知状态'
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
      return downloadManager.constructor.formatFileSize(bytes)
    },
    
    /**
     * 格式化下载速度
     */
    formatSpeed(bytesPerSecond) {
      return downloadManager.constructor.formatSpeed(bytesPerSecond)
    }
  }
}
</script>

<style scoped>
.download-progress {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 400px;
  max-height: 500px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.download-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.download-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.download-actions {
  display: flex;
  gap: 8px;
}

.download-items {
  max-height: 400px;
  overflow-y: auto;
}

.download-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.download-item:hover {
  background-color: #f9f9f9;
}

.download-item:last-child {
  border-bottom: none;
}

.file-info {
  margin-bottom: 8px;
}

.file-name {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.progress-section {
  margin-bottom: 8px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
}

.percentage {
  color: #303133;
  font-weight: 500;
}

.speed {
  color: #67c23a;
}

.download-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-text {
  font-size: 12px;
  color: #909399;
}

.control-buttons {
  display: flex;
  gap: 4px;
}

.error-message {
  margin-top: 8px;
  padding: 8px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  font-size: 12px;
  color: #f56c6c;
  display: flex;
  align-items: center;
  gap: 6px;
}

.no-downloads {
  padding: 40px 20px;
  text-align: center;
  color: #909399;
}

.no-downloads i {
  font-size: 48px;
  margin-bottom: 12px;
  display: block;
}

.no-downloads p {
  margin: 0;
  font-size: 14px;
}

/* 状态样式 */
.status-downloading {
  border-left: 3px solid #409eff;
}

.status-completed {
  border-left: 3px solid #67c23a;
}

.status-error {
  border-left: 3px solid #f56c6c;
}

.status-paused {
  border-left: 3px solid #e6a23c;
}

.status-pending {
  border-left: 3px solid #909399;
}
</style>