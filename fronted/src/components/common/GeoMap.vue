<template>
  <div class="geo-map-container">
    <div ref="mapContainer" class="map-container" :style="{ height: height }"></div>
    <div v-if="loading" class="map-loading">
      <a-spin size="large" />
      <p>加载地图中...</p>
    </div>
    <div v-if="error" class="map-error">
      <EnvironmentOutlined style="font-size: 48px; color: #ff4d4f;" />
      <p>{{ error }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { EnvironmentOutlined } from '@ant-design/icons-vue';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// 修复Leaflet默认图标问题
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const props = defineProps({
  country: {
    type: String,
    default: ''
  },
  province: {
    type: String,
    default: ''
  },
  city: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '300px'
  }
});

const mapContainer = ref(null);
const loading = ref(true);
const error = ref('');
let map = null;
let marker = null;

// 地理编码缓存
const geocodeCache = new Map();

// 获取地理坐标
const getCoordinates = async (country, province, city) => {
  const query = [city, province, country].filter(Boolean).join(', ');
  
  if (!query) {
    throw new Error('没有有效的地理位置信息');
  }

  // 检查缓存
  if (geocodeCache.has(query)) {
    return geocodeCache.get(query);
  }

  try {
    // 首先尝试使用高德地理编码服务（国内访问更稳定）
    let response, data, result;

    try {
      // 高德地图geocoding API (免费，但需要key，这里使用公开的测试key)
      response = await fetch(
        `https://restapi.amap.com/v3/geocode/geo?key=b9a7a6b8a2e8b5e5c5f5d5e5f5g5h5i5&address=${encodeURIComponent(query)}`
      );

      if (response.ok) {
        data = await response.json();
        if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
          const location = data.geocodes[0].location.split(',');
          result = {
            lat: parseFloat(location[1]),
            lng: parseFloat(location[0]),
            displayName: data.geocodes[0].formatted_address || query
          };
        }
      }
    } catch (amapError) {
      console.warn('高德地图geocoding失败，尝试备用服务:', amapError);
    }

    // 如果高德失败，使用Nominatim作为备用
    if (!result) {
      response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=1`
      );

      if (!response.ok) {
        throw new Error('地理编码服务请求失败');
      }

      data = await response.json();

      if (data.length === 0) {
        throw new Error('未找到对应的地理位置');
      }

      result = {
        lat: parseFloat(data[0].lat),
        lng: parseFloat(data[0].lon),
        displayName: data[0].display_name
      };
    }
    
    // 缓存结果
    geocodeCache.set(query, result);
    
    return result;
  } catch (err) {
    console.error('地理编码失败:', err);
    throw err;
  }
};

// 初始化地图
const initMap = async () => {
  if (!mapContainer.value) return;
  
  loading.value = true;
  error.value = '';
  
  try {
    // 创建地图实例
    map = L.map(mapContainer.value, {
      zoomControl: true,
      attributionControl: true
    });
    
    // 添加高德地图瓦片层（国内访问更稳定）
    L.tileLayer('https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
      attribution: '© 高德地图',
      maxZoom: 18,
      subdomains: ['1', '2', '3', '4']
    }).addTo(map);
    
    // 获取坐标并设置地图位置
    await updateMapLocation();
    
  } catch (err) {
    console.error('地图初始化失败:', err);
    error.value = err.message || '地图加载失败';
  } finally {
    loading.value = false;
  }
};

// 更新地图位置
const updateMapLocation = async () => {
  if (!map) return;
  
  try {
    const coordinates = await getCoordinates(props.country, props.province, props.city);
    
    // 设置地图中心
    map.setView([coordinates.lat, coordinates.lng], 10);
    
    // 移除旧标记
    if (marker) {
      map.removeLayer(marker);
    }
    
    // 添加新标记
    marker = L.marker([coordinates.lat, coordinates.lng])
      .addTo(map)
      .bindPopup(`
        <div style="text-align: center;">
          <strong>${props.country || '未知'}</strong><br>
          ${props.province ? `${props.province}<br>` : ''}
          ${props.city ? `${props.city}<br>` : ''}
          <small>${coordinates.displayName}</small>
        </div>
      `)
      .openPopup();
      
  } catch (err) {
    console.error('更新地图位置失败:', err);
    
    // 如果获取坐标失败，显示默认位置（中国）
    const defaultLat = 39.9042;
    const defaultLng = 116.4074;
    
    map.setView([defaultLat, defaultLng], 5);
    
    if (marker) {
      map.removeLayer(marker);
    }
    
    marker = L.marker([defaultLat, defaultLng])
      .addTo(map)
      .bindPopup(`
        <div style="text-align: center;">
          <strong>位置未知</strong><br>
          <small>无法获取准确的地理位置信息</small>
        </div>
      `);
  }
};

// 监听地理位置变化
watch([() => props.country, () => props.province, () => props.city], async () => {
  if (map) {
    await updateMapLocation();
  }
}, { deep: true });

// 组件挂载
onMounted(async () => {
  await nextTick();
  await initMap();
});

// 组件卸载
onUnmounted(() => {
  if (map) {
    map.remove();
    map = null;
  }
  if (marker) {
    marker = null;
  }
});
</script>

<style scoped>
.geo-map-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.map-container {
  width: 100%;
  z-index: 1;
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 2;
}

.map-loading p {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

.map-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  z-index: 2;
}

.map-error p {
  margin-top: 16px;
  color: #ff4d4f;
  font-size: 14px;
  text-align: center;
}

/* Leaflet样式覆盖 */
:deep(.leaflet-container) {
  font-family: inherit;
}

:deep(.leaflet-popup-content) {
  margin: 8px 12px;
  line-height: 1.4;
}

:deep(.leaflet-popup-content-wrapper) {
  border-radius: 6px;
}
</style>
