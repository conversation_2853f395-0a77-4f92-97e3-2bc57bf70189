<template>
  <div ref="editorContainer" class="monaco-editor-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import * as monaco from 'monaco-editor';
import loader from '@monaco-editor/loader';

// 配置Monaco Editor的CDN
loader.config({
  paths: {
    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs'
  }
});

interface Props {
  modelValue: string;
  language?: string;
  theme?: string;
  options?: any;
  height?: string | number;
  width?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  language: 'plaintext',
  theme: 'vs',
  options: () => ({}),
  height: '400px',
  width: '100%'
});

interface Emits {
  'update:modelValue': [value: string];
  'change': [value: string];
}

const emit = defineEmits<Emits>();

const editorContainer = ref<HTMLElement>();
let editor: monaco.editor.IStandaloneCodeEditor | null = null;
let isUpdatingFromProp = false;

// 默认编辑器选项
const defaultOptions = {
  automaticLayout: true,
  fontSize: 14,
  fontFamily: 'JetBrains Mono, Monaco, Menlo, Ubuntu Mono, monospace',
  lineHeight: 1.6,
  minimap: { enabled: false },
  scrollBeyondLastLine: false,
  wordWrap: 'on',
  lineNumbers: 'on',
  glyphMargin: false,
  folding: true,
  lineDecorationsWidth: 10,
  lineNumbersMinChars: 3,
  renderLineHighlight: 'line',
  selectOnLineNumbers: true,
  roundedSelection: false,
  readOnly: false,
  cursorStyle: 'line',
  tabSize: 2,
  insertSpaces: true,
  detectIndentation: true,
  trimAutoWhitespace: true,
  acceptSuggestionOnEnter: 'on',
  acceptSuggestionOnCommitCharacter: true,
  quickSuggestions: true,
  suggestOnTriggerCharacters: true,
  wordBasedSuggestions: true,
  parameterHints: { enabled: true },
  autoClosingBrackets: 'always',
  autoClosingQuotes: 'always',
  autoSurround: 'languageDefined',
  colorDecorators: true,
  contextmenu: true,
  copyWithSyntaxHighlighting: true,
  dragAndDrop: true,
  find: {
    seedSearchStringFromSelection: 'always',
    autoFindInSelection: 'never'
  },
  formatOnPaste: true,
  formatOnType: true,
  hover: { enabled: true },
  links: true,
  mouseWheelZoom: true,
  multiCursorModifier: 'alt',
  occurrencesHighlight: true,
  renderControlCharacters: false,
  renderIndentGuides: true,
  renderValidationDecorations: 'editable',
  renderWhitespace: 'selection',
  rulers: [],
  scrollbar: {
    vertical: 'auto',
    horizontal: 'auto',
    arrowSize: 11,
    useShadows: true,
    verticalHasArrows: false,
    horizontalHasArrows: false,
    verticalScrollbarSize: 14,
    horizontalScrollbarSize: 10,
    verticalSliderSize: 14,
    horizontalSliderSize: 10
  },
  selectionHighlight: true,
  showFoldingControls: 'mouseover',
  smoothScrolling: true,
  snippetSuggestions: 'top',
  useTabStops: true,
  wordSeparators: '`~!@#$%^&*()-=+[{]}\\|;:\",.<>/?'
};

// 语言映射
const getLanguageFromExtension = (filename: string): string => {
  const ext = filename.split('.').pop()?.toLowerCase();
  const languageMap: Record<string, string> = {
    'js': 'javascript',
    'jsx': 'javascript',
    'ts': 'typescript',
    'tsx': 'typescript',
    'vue': 'html',
    'html': 'html',
    'htm': 'html',
    'css': 'css',
    'scss': 'scss',
    'sass': 'sass',
    'less': 'less',
    'json': 'json',
    'xml': 'xml',
    'yaml': 'yaml',
    'yml': 'yaml',
    'md': 'markdown',
    'markdown': 'markdown',
    'py': 'python',
    'php': 'php',
    'java': 'java',
    'c': 'c',
    'cpp': 'cpp',
    'cxx': 'cpp',
    'cc': 'cpp',
    'h': 'c',
    'hpp': 'cpp',
    'cs': 'csharp',
    'go': 'go',
    'rs': 'rust',
    'rb': 'ruby',
    'sh': 'shell',
    'bash': 'shell',
    'zsh': 'shell',
    'fish': 'shell',
    'ps1': 'powershell',
    'sql': 'sql',
    'r': 'r',
    'swift': 'swift',
    'kt': 'kotlin',
    'scala': 'scala',
    'clj': 'clojure',
    'hs': 'haskell',
    'ml': 'fsharp',
    'fs': 'fsharp',
    'lua': 'lua',
    'pl': 'perl',
    'dockerfile': 'dockerfile',
    'ini': 'ini',
    'cfg': 'ini',
    'conf': 'ini',
    'toml': 'ini',
    'bat': 'bat',
    'cmd': 'bat'
  };
  return languageMap[ext || ''] || 'plaintext';
};

// 初始化编辑器
const initEditor = async () => {
  if (!editorContainer.value) return;

  try {
    const monacoInstance = await loader.init();

    // 定义自定义深色主题
    monacoInstance.editor.defineTheme('custom-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: '', foreground: 'E8E8E8', background: '2D2D30' },
        { token: 'comment', foreground: '6A9955' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'regexp', foreground: 'D16969' },
        { token: 'operator', foreground: 'E8E8E8' },
        { token: 'namespace', foreground: '4EC9B0' },
        { token: 'type', foreground: '4EC9B0' },
        { token: 'struct', foreground: '4EC9B0' },
        { token: 'class', foreground: '4EC9B0' },
        { token: 'interface', foreground: '4EC9B0' },
        { token: 'parameter', foreground: '9CDCFE' },
        { token: 'variable', foreground: '9CDCFE' },
        { token: 'function', foreground: 'DCDCAA' },
        { token: 'member', foreground: 'DCDCAA' }
      ],
      colors: {
        'editor.background': '#2D2D30',
        'editor.foreground': '#E8E8E8',
        'editorLineNumber.foreground': '#A0A0A0',
        'editorLineNumber.activeForeground': '#E8E8E8',
        'editor.selectionBackground': '#264F78',
        'editor.selectionHighlightBackground': '#ADD6FF26',
        'editor.lineHighlightBackground': '#383838',
        'editorCursor.foreground': '#FFFFFF',
        'editorWhitespace.foreground': '#505050',
        'editorIndentGuide.background': '#505050',
        'editorIndentGuide.activeBackground': '#808080'
      }
    });

    // 合并选项
    const editorOptions = {
      ...defaultOptions,
      ...props.options,
      value: props.modelValue,
      language: props.language,
      theme: props.theme
    };

    editor = monacoInstance.editor.create(editorContainer.value, editorOptions);

    // 监听内容变化
    editor.onDidChangeModelContent(() => {
      if (!isUpdatingFromProp && editor) {
        const value = editor.getValue();
        emit('update:modelValue', value);
        emit('change', value);
      }
    });

    // 设置编辑器尺寸
    updateEditorSize();

  } catch (error) {
    console.error('Monaco Editor 初始化失败:', error);
  }
};

// 更新编辑器尺寸
const updateEditorSize = () => {
  if (!editor || !editorContainer.value) return;
  
  const container = editorContainer.value;
  const height = typeof props.height === 'number' ? `${props.height}px` : props.height;
  const width = typeof props.width === 'number' ? `${props.width}px` : props.width;
  
  container.style.height = height;
  container.style.width = width;
  
  editor.layout();
};

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (editor && editor.getValue() !== newValue) {
    isUpdatingFromProp = true;
    editor.setValue(newValue || '');
    nextTick(() => {
      isUpdatingFromProp = false;
    });
  }
});

watch(() => props.language, (newLanguage) => {
  if (editor) {
    const model = editor.getModel();
    if (model) {
      monaco.editor.setModelLanguage(model, newLanguage);
    }
  }
});

watch(() => props.theme, (newTheme) => {
  if (editor) {
    monaco.editor.setTheme(newTheme);
  }
});

watch([() => props.height, () => props.width], () => {
  nextTick(() => {
    updateEditorSize();
  });
});

// 搜索功能
const findText = (searchText: string, options: any = {}) => {
  if (!editor) return;

  const findOptions = {
    searchString: searchText,
    isRegex: options.isRegex || false,
    matchCase: options.matchCase || false,
    wholeWord: options.wholeWord || false,
    ...options
  };

  editor.getAction('actions.find')?.run();
  // 或者使用更直接的方式
  editor.trigger('keyboard', 'actions.find', findOptions);
};

// 替换功能
const replaceText = (searchText: string, replaceText: string, options: any = {}) => {
  if (!editor) return;

  const model = editor.getModel();
  if (!model) return;

  const findOptions = {
    searchString: searchText,
    replaceString: replaceText,
    isRegex: options.isRegex || false,
    matchCase: options.matchCase || false,
    wholeWord: options.wholeWord || false
  };

  if (options.replaceAll) {
    // 全部替换
    const matches = model.findMatches(
      searchText,
      true, // searchOnlyEditableRange
      findOptions.isRegex,
      findOptions.matchCase,
      findOptions.wholeWord ? searchText : null,
      true // captureMatches
    );

    const edits = matches.map(match => ({
      range: match.range,
      text: replaceText
    }));

    editor.executeEdits('replace-all', edits);
  } else {
    // 单个替换
    editor.getAction('editor.action.startFindReplaceAction')?.run();
   
    
  }
};

// 跳转到行
const goToLine = (lineNumber: number) => {
  if (!editor) return;

  editor.setPosition({ lineNumber, column: 1 });
  editor.revealLineInCenter(lineNumber);
  editor.focus();
};

// 暴露方法
const focus = () => {
  if (editor) {
    editor.focus();
  }
};

const getEditor = () => editor;

const setValue = (value: string) => {
  if (editor) {
    isUpdatingFromProp = true;
    editor.setValue(value);
    nextTick(() => {
      isUpdatingFromProp = false;
    });
  }
};

const getValue = () => {
  return editor ? editor.getValue() : '';
};

const setLanguage = (language: string) => {
  if (editor) {
    const model = editor.getModel();
    if (model) {
      monaco.editor.setModelLanguage(model, language);
    }
  }
};

defineExpose({
  focus,
  getEditor,
  setValue,
  getValue,
  setLanguage,
  getLanguageFromExtension,
  findText,
  replaceText,
  goToLine
});

onMounted(() => {
  nextTick(() => {
    initEditor();
  });
});

onBeforeUnmount(() => {
  if (editor) {
    editor.dispose();
    editor = null;
  }
});
</script>

<style scoped>
.monaco-editor-container {
  width: 100%;
  height: 400px;
  border: none;
  border-radius: 0;
  overflow: hidden;
  background: #2d2d30;
  box-shadow: none;
}

.monaco-editor-container :deep(.monaco-editor) {
  border-radius: 0;
  background: #2d2d30 !important;
}

.monaco-editor-container :deep(.monaco-editor .margin) {
  background-color: #2d2d30 !important;
}

.monaco-editor-container :deep(.monaco-editor .monaco-editor-background) {
  background-color: #2d2d30 !important;
}

.monaco-editor-container :deep(.monaco-editor .current-line) {
  background-color: #383838 !important;
}

.monaco-editor-container :deep(.monaco-editor .line-numbers) {
  color: #a0a0a0 !important;
}

.monaco-editor-container :deep(.monaco-editor .current-line-number) {
  color: #e8e8e8 !important;
}

.monaco-editor-container :deep(.monaco-scrollable-element .scrollbar) {
  background: #2d2d30 !important;
}

.monaco-editor-container :deep(.monaco-scrollable-element .slider) {
  background: #5a5a5a !important;
}

.monaco-editor-container :deep(.monaco-scrollable-element .slider:hover) {
  background: #6a6a6a !important;
}

/* 确保文本颜色正确 */
.monaco-editor-container :deep(.monaco-editor .view-lines) {
  color: #e8e8e8 !important;
}

.monaco-editor-container :deep(.monaco-editor .mtk1) {
  color: #e8e8e8 !important;
}

/* 光标颜色 */
.monaco-editor-container :deep(.monaco-editor .cursor) {
  background-color: #ffffff !important;
}

/* 选择区域颜色 */
.monaco-editor-container :deep(.monaco-editor .selected-text) {
  background-color: #264f78 !important;
}
</style>