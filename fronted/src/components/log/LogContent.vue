<template>
  <div class="log-monitor-container">
    <!-- 顶部统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :span="6" v-for="(stat, level) in logStats?.levels || {}" :key="level">
          <a-card class="stat-card" :class="`stat-${level}`">
            <div class="stat-content">
              <div class="stat-icon">
                <component :is="getLevelIcon(level)" />
              </div>
              <div class="stat-info">
                <div class="stat-title">{{ level.toUpperCase() }}</div>
                <div class="stat-value">{{ stat.count }} 个文件</div>
                <div class="stat-size">{{ formatFileSize(stat.size) }}</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card stat-total">
            <div class="stat-content">
              <div class="stat-icon">
                <FileTextOutlined />
              </div>
              <div class="stat-info">
                <div class="stat-title">总计</div>
                <div class="stat-value">{{ totalFiles }} 个文件</div>
                <div class="stat-size">{{ formatFileSize(totalSize) }}</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="16">
        <!-- 左侧文件列表 -->
        <a-col :span="6">
          <a-card title="日志文件" class="file-list-card">
            <template #extra>
              <a-button
                type="text"
                size="small"
                :icon="h(ReloadOutlined)"
                @click="loadLogFiles"
                :loading="loadingFiles"
              />
            </template>

            <div class="file-filters">
              <a-select
                v-model:value="selectedLevel"
                placeholder="筛选级别"
                style="width: 100%; margin-bottom: 12px;"
                @change="handleLevelFilter"
                allowClear
              >
                <a-select-option value="info">INFO</a-select-option>
                <a-select-option value="warn">WARN</a-select-option>
                <a-select-option value="error">ERROR</a-select-option>
                <a-select-option value="debug">DEBUG</a-select-option>
                <a-select-option value="fatal">FATAL</a-select-option>
              </a-select>
            </div>

            <div class="file-list">
              <div
                v-for="file in filteredLogFiles"
                :key="file.path"
                class="file-item"
                :class="{ active: selectedFile === file.path }"
                @click="selectFile(file)"
              >
                <div class="file-header">
                  <a-tag :color="getLevelColor(file.level)" size="small">
                    {{ file.level.toUpperCase() }}
                  </a-tag>
                  <span class="file-date">{{ formatDate(file.date) }}</span>
                </div>
                <div class="file-info">
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  <span class="file-time">{{ formatTime(file.modTime) }}</span>
                </div>
              </div>

              <a-empty v-if="filteredLogFiles.length === 0"
                description="暂无日志文件"
                :image="h('div')"
                size="small"
              />
            </div>
          </a-card>
        </a-col>

        <!-- 右侧日志内容 -->
        <a-col :span="18">
          <a-card class="log-content-card">
            <template #title>
              <div class="content-title">
                <span>日志内容</span>
                <a-tag v-if="currentFileName" color="blue">{{ currentFileName }}</a-tag>
              </div>
            </template>

            <template #extra>
              <a-space>
                <a-input-search
                  v-model:value="searchKeyword"
                  placeholder="搜索日志内容"
                  style="width: 200px;"
                  @search="handleSearch"
                  @pressEnter="handleSearch"
                  size="small"
                />
                <a-button
                  :type="autoRefresh ? 'primary' : 'default'"
                  :icon="h(autoRefresh ? PauseCircleOutlined : PlayCircleOutlined)"
                  @click="toggleAutoRefresh"
                  size="small"
                  :disabled="!selectedFile"
                >
                  {{ autoRefresh ? '停止' : '实时' }}
                </a-button>
                <a-button
                  :icon="h(ClearOutlined)"
                  @click="clearLogs"
                  size="small"
                >
                  清空
                </a-button>
                <a-button
                  :icon="h(DownloadOutlined)"
                  @click="downloadLogs"
                  size="small"
                  :disabled="!selectedFile"
                >
                  下载
                </a-button>
              </a-space>
            </template>

            <!-- 日志内容显示 -->
            <div class="log-viewer">
              <div v-if="!selectedFile" class="no-file-selected">
                <a-empty description="请选择左侧的日志文件查看内容">
                  <template #image>
                    <FileTextOutlined style="font-size: 48px; color: #d9d9d9;" />
                  </template>
                </a-empty>
              </div>

              <div v-else class="log-display" ref="logDisplayRef">
                <div class="log-header">
                  <a-space>
                    <a-tag :color="autoRefresh ? 'green' : 'blue'">
                      {{ autoRefresh ? '实时监控中' : '静态查看' }}
                    </a-tag>
                    <span class="log-count">共 {{ totalLines }} 行</span>
                    <span v-if="filteredLines > 0" class="filtered-count">
                      筛选后 {{ filteredLines }} 行
                    </span>
                  </a-space>
                </div>

                <div class="log-lines" :class="{ loading: loading }">
                  <div
                    v-for="(line, index) in displayLogs"
                    :key="index"
                    class="log-line"
                    :class="getLogLineClass(line)"
                  >
                    <span class="line-number">{{ index + 1 }}</span>
                    <span class="log-timestamp">{{ extractTimestamp(line) }}</span>
                    <a-tag
                      :color="getLevelColor(extractLevel(line).toLowerCase())"
                      size="small"
                      class="log-level-tag"
                    >
                      {{ extractLevel(line) }}
                    </a-tag>
                    <span class="log-message">{{ extractMessage(line) }}</span>
                  </div>

                  <!-- 加载更多 -->
                  <div v-if="hasMore" class="load-more">
                    <a-button
                      type="dashed"
                      @click="loadMore"
                      :loading="loadingMore"
                      block
                    >
                      加载更多日志
                    </a-button>
                  </div>

                  <!-- 加载状态 -->
                  <div v-if="loading" class="loading-indicator">
                    <a-spin size="large">
                      <template #indicator>
                        <LoadingOutlined style="font-size: 24px;" />
                      </template>
                    </a-spin>
                    <div>正在加载日志...</div>
                  </div>

                  <!-- 空状态 -->
                  <div v-if="displayLogs.length === 0 && !loading && selectedFile" class="empty-logs">
                    <a-empty description="该日志文件暂无内容" />
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, h, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ClearOutlined,
  FileTextOutlined,
  DownloadOutlined,
  LoadingOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  BugOutlined
} from '@ant-design/icons-vue'
import * as logApi from '@/api/log'

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const loadingFiles = ref(false)
const autoRefresh = ref(false)
const selectedLevel = ref('')
const selectedFile = ref('')
const searchKeyword = ref('')
const logFiles = ref([])
const logStats = ref(null)
const displayLogs = ref([])
const totalLines = ref(0)
const filteredLines = ref(0)
const currentPage = ref(1)
const pageSize = ref(100)
const hasMore = ref(false)

// DOM引用
const logDisplayRef = ref(null)

// 计算属性
const filteredLogFiles = computed(() => {
  if (!selectedLevel.value) {
    return logFiles.value
  }
  return logFiles.value.filter(file => file.level === selectedLevel.value)
})

const currentFileName = computed(() => {
  if (!selectedFile.value) return ''
  const file = logFiles.value.find(f => f.path === selectedFile.value)
  return file ? `${file.date}-${file.level}.log` : ''
})

const totalFiles = computed(() => {
  return Object.values(logStats.value?.levels || {}).reduce((sum, stat) => sum + stat.count, 0)
})

const totalSize = computed(() => {
  return Object.values(logStats.value?.levels || {}).reduce((sum, stat) => sum + stat.size, 0)
})

// 自动刷新定时器
let refreshTimer = null
let eventSource = null

// 组件挂载
onMounted(() => {
  initLogMonitor()
})

// 组件卸载
onUnmounted(() => {
  cleanup()
})

// 初始化日志监控
const initLogMonitor = async () => {
  await Promise.all([
    loadLogFiles(),
    loadLogStats()
  ])

  // 自动选择最新的日志文件
  if (logFiles.value.length > 0) {
    const latestFile = logFiles.value[0] // 文件已按修改时间倒序排列
    selectFile(latestFile)
  }
}

// 加载日志文件列表
const loadLogFiles = async () => {
  loadingFiles.value = true
  try {
    const response = await logApi.getLogFileList()
    if (response.code === 200) {
      logFiles.value = response.data.files || []
    }
  } catch (error) {
    console.error('加载日志文件列表失败:', error)
    message.error('加载日志文件列表失败')
  } finally {
    loadingFiles.value = false
  }
}

// 选择文件
const selectFile = (file) => {
  selectedFile.value = file.path
  loadLogContent()
}

// 处理级别筛选
const handleLevelFilter = () => {
  // 级别筛选后，如果当前选中的文件不在筛选结果中，则选择第一个
  if (selectedLevel.value && filteredLogFiles.value.length > 0) {
    const currentFileInFilter = filteredLogFiles.value.find(f => f.path === selectedFile.value)
    if (!currentFileInFilter) {
      selectFile(filteredLogFiles.value[0])
    }
  }
}

// 加载日志统计信息
const loadLogStats = async () => {
  try {
    const response = await logApi.getLogStats()
    if (response.code === 200) {
      logStats.value = response.data
    }
  } catch (error) {
    console.error('加载日志统计失败:', error)
  }
}

// 加载日志内容
const loadLogContent = async (append = false) => {
  if (!selectedFile.value) {
    displayLogs.value = []
    totalLines.value = 0
    return
  }

  if (!append) {
    loading.value = true
    currentPage.value = 1
  } else {
    loadingMore.value = true
  }

  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      path: selectedFile.value
    }

    const response = await logApi.getLogContent(params)
    if (response.code === 200) {
      const { content, totalPages } = response.data

      if (append) {
        displayLogs.value.push(...content.lines)
      } else {
        displayLogs.value = content.lines
      }

      totalLines.value = content.totalLines
      hasMore.value = currentPage.value < totalPages

      // 应用搜索筛选
      applySearch()

      // 滚动到底部（新日志）
      if (!append) {
        await nextTick()
        scrollToBottom()
      }
    }
  } catch (error) {
    console.error('加载日志内容失败:', error)
    message.error('加载日志内容失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 应用搜索筛选
const applySearch = () => {
  if (!searchKeyword.value.trim()) {
    filteredLines.value = displayLogs.value.length
    return
  }

  const keyword = searchKeyword.value.toLowerCase()
  const filtered = displayLogs.value.filter(line =>
    line.toLowerCase().includes(keyword)
  )
  filteredLines.value = filtered.length
}

// 加载更多日志
const loadMore = () => {
  currentPage.value++
  loadLogContent(true)
}



// 处理搜索
const handleSearch = () => {
  applySearch()
  if (searchKeyword.value.trim()) {
    // 重新加载内容以应用搜索
    loadLogContent()
  }
}

// 下载日志
const downloadLogs = () => {
  if (!selectedFile.value) {
    message.warning('请先选择日志文件')
    return
  }

  const file = logFiles.value.find(f => f.path === selectedFile.value)
  if (file) {
    const link = document.createElement('a')
    const token = localStorage.getItem('token') || sessionStorage.getItem('token')
    link.href = `/api/log/download?path=${encodeURIComponent(selectedFile.value)}&token=${token}`
    link.download = `${file.date}-${file.level}.log`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    message.success('开始下载日志文件')
  }
}

// 刷新日志
const refreshLogs = () => {
  initLogMonitor()
}

// 切换自动刷新
const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    startRealTimeMonitoring()
  } else {
    stopRealTimeMonitoring()
  }
}

// 开始实时监控
const startRealTimeMonitoring = () => {
  if (!selectedFile.value) {
    message.warning('请先选择要监控的日志文件')
    autoRefresh.value = false
    return
  }

  // const url = `/api/log/stream?path=${encodeURIComponent(selectedFile.value)}`
  // eventSource = new EventSource(url)
  eventSource = logApi.getLogStream(selectedFile.value)
  eventSource.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      if (data.line) {
        displayLogs.value.push(data.line)
        totalLines.value++
        
        // 保持最大行数限制
        if (displayLogs.value.length > 1000) {
          displayLogs.value = displayLogs.value.slice(-1000)
        }
        
        nextTick(() => {
          scrollToBottom()
        })
      }
    } catch (error) {
      console.error('解析实时日志数据失败:', error)
    }
  }
  
  eventSource.onerror = (error) => {
    console.error('实时日志连接错误:', error)
    message.error('实时日志连接断开')
    autoRefresh.value = false
  }
}

// 停止实时监控
const stopRealTimeMonitoring = () => {
  if (eventSource) {
    eventSource.close()
    eventSource = null
  }
}

// 清空日志显示
const clearLogs = () => {
  displayLogs.value = []
  totalLines.value = 0
}

// 滚动到底部
const scrollToBottom = () => {
  if (logDisplayRef.value) {
    logDisplayRef.value.scrollTop = logDisplayRef.value.scrollHeight
  }
}

// 获取级别颜色
const getLevelColor = (level) => {
  const colors = {
    info: '#1890ff',
    warn: '#faad14',
    error: '#ff4d4f',
    debug: '#52c41a'
  }
  return colors[level] || '#666'
}

// 获取级别图标
const getLevelIcon = (level) => {
  const icons = {
    info: InfoCircleOutlined,
    warn: WarningOutlined,
    error: CloseCircleOutlined,
    debug: BugOutlined
  }
  return icons[level] || InfoCircleOutlined
}

// 格式化日期
const formatDate = (dateStr) => {
  if (dateStr === 'unknown') return '未知日期'
  return dateStr
}

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 提取时间戳
const extractTimestamp = (line) => {
  const match = line.match(/\[EzC2\](\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})/)
  return match ? match[1] : ''
}

// 提取日志级别
const extractLevel = (line) => {
  const match = line.match(/\[(info|warn|error|debug)\]/)
  return match ? match[1].toUpperCase() : ''
}

// 提取消息内容
const extractMessage = (line) => {
  const parts = line.split('\t')
  return parts.length > 3 ? parts.slice(3).join('\t') : line
}

// 获取日志行样式类
const getLogLineClass = (line) => {
  const level = extractLevel(line).toLowerCase()
  return `log-line-${level}`
}

// 清理资源
const cleanup = () => {
  stopRealTimeMonitoring()
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
}
</script>

<style scoped>
.log-monitor-container {
  padding: 16px;
  height: 100vh;
  background: #f5f7fa;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 16px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  font-size: 24px;
  margin-right: 12px;
  width: 40px;
  text-align: center;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-size {
  font-size: 11px;
  color: #999;
}

.stat-info .stat-icon {
  color: #1890ff;
}

.stat-warn .stat-icon {
  color: #faad14;
}

.stat-error .stat-icon {
  color: #ff4d4f;
}

.stat-debug .stat-icon {
  color: #52c41a;
}

.stat-total .stat-icon {
  color: #722ed1;
}

/* 主要内容区域 */
.main-content {
  height: calc(100vh - 200px);
}

/* 文件列表样式 */
.file-list-card {
  height: 100%;
}

.file-filters {
  margin-bottom: 12px;
}

.file-list {
  height: calc(100% - 80px);
  overflow-y: auto;
}

.file-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.file-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.file-item.active {
  border-color: #1890ff;
  background: #e6f7ff;
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.file-date {
  font-size: 12px;
  color: #666;
}

.file-info {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #999;
}

/* 日志内容样式 */
.log-content-card {
  height: 100%;
}

.content-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-viewer {
  height: calc(100% - 60px);
}

.no-file-selected {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.log-display {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.log-header {
  padding: 8px 12px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  border-radius: 4px 4px 0 0;
}

.log-lines {
  flex: 1;
  overflow-y: auto;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 0 0 4px 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
}

.log-lines.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.log-line {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.log-line:hover {
  background: #f5f5f5;
}

.line-number {
  width: 50px;
  color: #999;
  font-size: 11px;
  text-align: right;
  margin-right: 12px;
  user-select: none;
}

.log-timestamp {
  color: #666;
  margin-right: 8px;
  font-size: 11px;
  min-width: 140px;
}

.log-level-tag {
  margin-right: 8px;
  min-width: 50px;
  text-align: center;
}

.log-message {
  flex: 1;
  word-break: break-all;
  color: #333;
}

.log-line-info {
  border-left: 3px solid #1890ff;
}

.log-line-warn {
  border-left: 3px solid #faad14;
}

.log-line-error {
  border-left: 3px solid #ff4d4f;
  background: rgba(255, 77, 79, 0.05);
}

.log-line-debug {
  border-left: 3px solid #52c41a;
}

.load-more {
  padding: 16px;
  text-align: center;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
}

.loading-indicator > div {
  margin-top: 16px;
}

.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.log-count, .filtered-count {
  font-size: 12px;
  color: #666;
}

.filtered-count {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-section .ant-col {
    margin-bottom: 8px;
  }

  .main-content .ant-col:first-child {
    margin-bottom: 16px;
  }
}

/* 滚动条样式 */
.file-list::-webkit-scrollbar,
.log-lines::-webkit-scrollbar {
  width: 6px;
}

.file-list::-webkit-scrollbar-track,
.log-lines::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.file-list::-webkit-scrollbar-thumb,
.log-lines::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.file-list::-webkit-scrollbar-thumb:hover,
.log-lines::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
