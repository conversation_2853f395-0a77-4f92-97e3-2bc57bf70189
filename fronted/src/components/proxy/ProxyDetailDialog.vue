<template>
  <el-dialog
    v-model="dialogVisible"
    title="代理实例详情"
    width="900px"
    :before-close="handleClose"
  >
    <div v-if="proxyData" class="proxy-detail">
      <!-- 基本信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button type="primary" size="small" @click="editMode = !editMode">
              {{ editMode ? '取消编辑' : '编辑' }}
            </el-button>
          </div>
        </template>

        <el-form
          v-if="editMode"
          ref="formRef"
          :model="editForm"
          :rules="rules"
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="代理名称" prop="name">
                <el-input v-model="editForm.name" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="代理类型">
                <el-tag :type="getProxyTypeColor(proxyData.type)">
                  {{ getProxyTypeText(proxyData.type) }}
                </el-tag>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="描述">
            <el-input
              v-model="editForm.description"
              type="textarea"
              :rows="2"
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="最大连接数">
                <el-input-number
                  v-model="editForm.max_connections"
                  :min="1"
                  :max="10000"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="超时时间(秒)">
                <el-input-number
                  v-model="editForm.timeout"
                  :min="1"
                  :max="3600"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="日志级别">
                <el-select v-model="editForm.log_level" style="width: 100%">
                  <el-option label="Debug" value="debug" />
                  <el-option label="Info" value="info" />
                  <el-option label="Warn" value="warn" />
                  <el-option label="Error" value="error" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item>
                <el-checkbox v-model="editForm.enable_metrics">启用指标收集</el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="form-actions">
            <el-button @click="editMode = false">取消</el-button>
            <el-button type="primary" @click="handleUpdate" :loading="updating">
              保存
            </el-button>
          </div>
        </el-form>

        <div v-else class="info-display">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>代理ID：</label>
                <span>{{ proxyData.proxy_id }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>代理名称：</label>
                <span>{{ proxyData.name }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>代理类型：</label>
                <el-tag :type="getProxyTypeColor(proxyData.type)">
                  {{ getProxyTypeText(proxyData.type) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>监听端口：</label>
                <span>{{ proxyData.port }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>运行状态：</label>
                <el-tag :type="getStatusColor(proxyData.status)">
                  {{ getStatusText(proxyData.status) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatTime(proxyData.created_at) }}</span>
              </div>
            </el-col>
          </el-row>

          <div class="info-item">
            <label>描述：</label>
            <span>{{ proxyData.description || '无' }}</span>
          </div>
        </div>
      </el-card>

      <!-- 客户端信息 -->
      <el-card class="detail-card" shadow="never" v-if="proxyData.client_info">
        <template #header>
          <span>关联客户端</span>
        </template>

        <div class="client-detail">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>主机名：</label>
                <span>{{ proxyData.client_info.hostname }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>操作系统：</label>
                <span>{{ proxyData.client_info.os }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>内网IP：</label>
                <span>{{ proxyData.client_info.local_ip }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>公网IP：</label>
                <span>{{ proxyData.client_info.public_ip }}</span>
              </div>
            </el-col>
          </el-row>

          <div class="info-item">
            <label>客户端状态：</label>
            <el-tag :type="proxyData.client_info.status === 1 ? 'success' : 'danger'">
              {{ proxyData.client_info.status === 1 ? '在线' : '离线' }}
            </el-tag>
          </div>
        </div>
      </el-card>

      <!-- 统计信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <span>统计信息</span>
        </template>

        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ proxyData.total_connections || 0 }}</div>
            <div class="stat-label">总连接数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ proxyData.active_connections || 0 }}</div>
            <div class="stat-label">活跃连接</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatBytes(proxyData.bytes_transferred || 0) }}</div>
            <div class="stat-label">上传流量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatBytes(proxyData.bytes_received || 0) }}</div>
            <div class="stat-label">下载流量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ proxyData.request_count || 0 }}</div>
            <div class="stat-label">请求总数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatTime(proxyData.last_activity) || '无' }}</div>
            <div class="stat-label">最后活动</div>
          </div>
        </div>
      </el-card>

      <!-- 配置信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <span>配置信息</span>
        </template>

        <div class="config-info">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>最大连接数：</label>
                <span>{{ proxyData.max_connections || 100 }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>超时时间：</label>
                <span>{{ proxyData.timeout || 30 }}秒</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>缓冲区大小：</label>
                <span>{{ proxyData.buffer_size || 4096 }}字节</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>日志级别：</label>
                <span>{{ proxyData.log_level || 'info' }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>需要认证：</label>
                <el-tag :type="proxyData.auth_required ? 'success' : 'info'">
                  {{ proxyData.auth_required ? '是' : '否' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>指标收集：</label>
                <el-tag :type="proxyData.enable_metrics ? 'success' : 'info'">
                  {{ proxyData.enable_metrics ? '启用' : '禁用' }}
                </el-tag>
              </div>
            </el-col>
          </el-row>

          <div class="info-item" v-if="proxyData.allowed_ips">
            <label>允许的IP：</label>
            <span>{{ proxyData.allowed_ips }}</span>
          </div>

          <div class="info-item" v-if="proxyData.blocked_ips">
            <label>阻止的IP：</label>
            <span>{{ proxyData.blocked_ips }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { updateProxy } from '@/api/proxy'
import { formatBytes, formatTime } from '@/utils/format'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  proxyData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const dialogVisible = ref(false)
const editMode = ref(false)
const updating = ref(false)

// 编辑表单
const editForm = reactive({
  id: null,
  name: '',
  description: '',
  max_connections: 100,
  timeout: 30,
  log_level: 'info',
  enable_metrics: true
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入代理名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val && props.proxyData) {
    // 初始化编辑表单
    Object.assign(editForm, {
      id: props.proxyData.id,
      name: props.proxyData.name,
      description: props.proxyData.description || '',
      max_connections: props.proxyData.max_connections || 100,
      timeout: props.proxyData.timeout || 30,
      log_level: props.proxyData.log_level || 'info',
      enable_metrics: props.proxyData.enable_metrics !== false
    })
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    editMode.value = false
  }
})

// 方法
const getProxyTypeColor = (type) => {
  const colors = {
    forward: 'success',
    reverse: 'warning',
    chain: 'info'
  }
  return colors[type] || 'info'
}

const getProxyTypeText = (type) => {
  const texts = {
    forward: '正向代理',
    reverse: '反向代理',
    chain: '代理链'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    0: 'info',      // 停止
    1: 'success',   // 运行中
    2: 'danger',    // 错误
    3: 'warning'    // 启动中
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    0: '停止',
    1: '运行中',
    2: '错误',
    3: '启动中'
  }
  return texts[status] || '未知'
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleUpdate = async () => {
  try {
    await formRef.value.validate()
    
    updating.value = true
    
    const response = await updateProxy(editForm)
    
    if (response.code === 200) {
      ElMessage.success('代理更新成功')
      editMode.value = false
      emit('success')
    } else {
      ElMessage.error(response.msg || '代理更新失败')
    }
  } catch (error) {
    if (error !== false) {
      console.error('更新代理失败:', error)
      ElMessage.error('更新代理失败')
    }
  } finally {
    updating.value = false
  }
}
</script>

<style scoped>
.proxy-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-display .info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.info-display .info-item:last-child {
  margin-bottom: 0;
}

.info-display .info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.config-info .info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.config-info .info-item:last-child {
  margin-bottom: 0;
}

.config-info .info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 10px;
}

.client-detail .info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.client-detail .info-item:last-child {
  margin-bottom: 0;
}

.client-detail .info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.form-actions {
  text-align: right;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  text-align: right;
}
</style>
