<template>
  <a-modal
    v-model:visible="visible"
    title="iox代理系统配置工具书"
    width="900px"
    :footer="null"
    class="proxy-config-guide"
  >
    <div class="guide-content">
      <!-- 导航标签 -->
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 概述标签 -->
        <a-tab-pane key="overview" tab="📖 系统概述">
          <div class="overview-section">
            <a-alert
              message="iox代理系统特点"
              description="基于Client-Server架构的高性能代理系统，支持正向代理、反向代理和代理链三种模式"
              type="info"
              show-icon
              style="margin-bottom: 16px;"
            />
            
            <a-row :gutter="16">
              <a-col :span="8">
                <a-card title="🔄 正向代理" size="small" hoverable>
                  <p><strong>原理</strong>：Client启动SOCKS5服务器</p>
                  <p><strong>连接</strong>：用户 → Server → Client → 内网资源</p>
                  <p><strong>场景</strong>：通过Client访问其内网资源，横向移动</p>
                  <a-tag color="green">内网访问</a-tag>
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card title="🔄 反向代理" size="small" hoverable>
                  <p><strong>原理</strong>：Client主动连接Server</p>
                  <p><strong>连接</strong>：用户 → Server ← Client内网服务</p>
                  <p><strong>场景</strong>：暴露Client内网服务供外部访问</p>
                  <a-tag color="blue">服务暴露</a-tag>
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card title="🔗 代理链" size="small" hoverable>
                  <p><strong>原理</strong>：多Client协同搭建链路</p>
                  <p><strong>连接</strong>：用户 → Client1 → Client2 → 深层内网</p>
                  <p><strong>场景</strong>：多层内网环境下的深度访问</p>
                  <a-tag color="purple">深度渗透</a-tag>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 正向代理标签 -->
        <a-tab-pane key="forward" tab="🔄 正向代理">
          <div class="forward-section">
            <a-typography-title :level="4">正向代理 (ForwardProxyIOX)</a-typography-title>
            
            <a-alert
              message="核心原理"
              description="Client启动SOCKS5服务器，Server连接Client，用户通过Server端口访问Client所在内网的资源，实现横向移动"
              type="success"
              show-icon
              style="margin-bottom: 16px;"
            />

            <a-descriptions title="配置说明" :column="1" bordered>
              <a-descriptions-item label="端口配置">
                <a-tag color="orange">Server端开启</a-tag> 用户连接端口 (28000-48000)
              </a-descriptions-item>
              <a-descriptions-item label="协议类型">
                <a-tag color="blue">SOCKS5</a-tag> 动态代理协议
              </a-descriptions-item>
              <a-descriptions-item label="连接流程">
                1. Client启动SOCKS5服务器<br/>
                2. Server连接Client的SOCKS5服务器<br/>
                3. 用户连接Server的SOCKS5端口<br/>
                4. 流量通过Client转发到其内网中的目标资源
              </a-descriptions-item>
            </a-descriptions>

            <a-divider />

            <div class="architecture-diagram">
              <a-typography-title :level="5">架构图</a-typography-title>
              <div class="diagram-box">
                <pre class="ascii-diagram">
用户设备                Server                    Client              Client内网资源
   │                     │                        │                      │
   │  ① 连接SOCKS5端口    │                        │                      │
   ├─────────────────────►│                        │                      │
   │                     │  ② 连接Client SOCKS5   │                      │
   │                     ├────────────────────────►│                      │
   │                     │                        │  ③ 访问内网资源       │
   │                     │                        ├─────────────────────►│
   │  ④ 数据流转发        │  ⑤ 数据流转发           │  ⑥ 数据流转发         │
   ◄─────────────────────┼────────────────────────┼─────────────────────◄│
                </pre>
              </div>
            </div>

            <a-divider />

            <a-typography-title :level="5">使用场景</a-typography-title>
            <a-list size="small">
              <a-list-item>🎯 <strong>横向移动</strong>：通过Client访问其内网中的其他主机</a-list-item>
              <a-list-item>🔍 <strong>内网探测</strong>：扫描和发现Client内网中的服务</a-list-item>
              <a-list-item>📊 <strong>资源访问</strong>：访问内网Web服务、数据库、文件共享</a-list-item>
              <a-list-item>⚡ <strong>权限提升</strong>：利用Client权限访问受限资源</a-list-item>
            </a-list>
          </div>
        </a-tab-pane>

        <!-- 反向代理标签 -->
        <a-tab-pane key="reverse" tab="🔄 反向代理">
          <div class="reverse-section">
            <a-typography-title :level="4">反向代理 (ReverseProxyIOX)</a-typography-title>
            
            <a-alert
              message="核心原理"
              description="Server开启双端口服务，Client主动连接，外部用户可以通过Server访问Client内网的特定服务"
              type="info"
              show-icon
              style="margin-bottom: 16px;"
            />

            <a-descriptions title="配置说明" :column="1" bordered>
              <a-descriptions-item label="端口配置">
                <a-tag color="orange">Server端开启</a-tag> 
                Client连接端口 + 用户SOCKS5端口 (双端口架构)
              </a-descriptions-item>
              <a-descriptions-item label="连接方向">
                <a-tag color="red">Client主动连接</a-tag> Client → Server
              </a-descriptions-item>
              <a-descriptions-item label="连接流程">
                1. Server启动双端口服务<br/>
                2. Client主动连接Server的Client端口<br/>
                3. 用户连接Server的用户SOCKS5端口<br/>
                4. 流量通过Client转发到其内网的特定服务
              </a-descriptions-item>
            </a-descriptions>

            <a-divider />

            <div class="architecture-diagram">
              <a-typography-title :level="5">架构图</a-typography-title>
              <div class="diagram-box">
                <pre class="ascii-diagram">
用户设备                Server                    Client              Client内网服务
   │                     │                        │                      │
   │                     │ Client端口 (自动分配)   │                      │
   │                     ◄────────────────────────┤ ① Client主动连接     │
   │                     │                        │                      │
   │  ② 连接SOCKS5端口    │                        │                      │
   ├─────────────────────►│ 用户SOCKS5端口         │                      │
   │                     │                        │  ③ 访问内网服务       │
   │                     │                        ├─────────────────────►│
   │  ④ 数据流转发        │  ⑤ 数据流转发           │  ⑥ 数据流转发         │
   ◄─────────────────────┼────────────────────────┼─────────────────────◄│
                </pre>
              </div>
            </div>

            <a-divider />

            <a-typography-title :level="5">使用场景</a-typography-title>
            <a-list size="small">
              <a-list-item>🌐 <strong>服务暴露</strong>：将Client内网Web服务暴露给外部访问</a-list-item>
              <a-list-item>💾 <strong>数据提取</strong>：访问Client内网数据库、文件服务器</a-list-item>
              <a-list-item>🖥️ <strong>远程管理</strong>：远程访问Client内网的管理界面</a-list-item>
              <a-list-item>🔧 <strong>持久化访问</strong>：建立到Client内网服务的稳定通道</a-list-item>
            </a-list>
          </div>
        </a-tab-pane>

        <!-- 代理链标签 -->
        <a-tab-pane key="chain" tab="🔗 代理链">
          <div class="chain-section">
            <a-typography-title :level="4">代理链 (ProxyChainIOX)</a-typography-title>
            
            <a-alert
              message="核心原理"
              description="多个Client协同工作自动搭建代理链，穿越多层内网环境，用户连接最终SOCKS5端口即可访问深层内网资源"
              type="warning"
              show-icon
              style="margin-bottom: 16px;"
            />

            <a-descriptions title="配置说明" :column="1" bordered>
              <a-descriptions-item label="端口配置">
                <a-tag color="orange">Server端开启</a-tag> 用户SOCKS5端口 (28000-48000)
              </a-descriptions-item>
              <a-descriptions-item label="节点类型">
                <a-tag color="green">正向代理</a-tag> + <a-tag color="blue">反向代理</a-tag> 混合模式
              </a-descriptions-item>
              <a-descriptions-item label="连接流程">
                1. Server启动SOCKS5服务<br/>
                2. 用户连接Server的SOCKS5端口<br/>
                3. 流量按照代理链顺序经过多个Client<br/>
                4. 最终到达深层内网中的目标资源
              </a-descriptions-item>
            </a-descriptions>

            <a-divider />

            <div class="architecture-diagram">
              <a-typography-title :level="5">架构图</a-typography-title>
              <div class="diagram-box">
                <pre class="ascii-diagram">
用户设备        Server       Client1(内网1)    Client2(内网2)    深层内网资源
   │             │               │               │               │
   │ ① 连接SOCKS5 │               │               │               │
   ├─────────────►│               │               │               │
   │             │ ② 连接Client1  │               │               │
   │             ├───────────────►│               │               │
   │             │               │ ③ 连接Client2  │               │
   │             │               ├───────────────►│               │
   │             │               │               │ ④ 访问深层资源 │
   │             │               │               ├───────────────►│
   │ ⑤ 数据流转发  │ ⑥ 数据流转发   │ ⑦ 数据流转发   │ ⑧ 数据流转发   │
   ◄─────────────┼───────────────┼───────────────┼───────────────◄│
                </pre>
              </div>
            </div>

            <a-divider />

            <a-typography-title :level="5">使用场景</a-typography-title>
            <a-list size="small">
              <a-list-item>🎯 <strong>深层渗透</strong>：穿越多层内网环境访问深层资源</a-list-item>
              <a-list-item>🔗 <strong>链路协同</strong>：多个Client自动协同搭建访问链路</a-list-item>
              <a-list-item>🌐 <strong>复杂拓扑</strong>：应对复杂的网络拓扑结构</a-list-item>
              <a-list-item>🚀 <strong>APT模拟</strong>：模拟高级持续性威胁的网络行为</a-list-item>
            </a-list>
          </div>
        </a-tab-pane>

        <!-- 常见问题标签 -->
        <a-tab-pane key="faq" tab="❓ 常见问题">
          <div class="faq-section">
            <a-collapse>
              <a-collapse-panel key="1" header="🤔 如何选择代理类型？">
                <p><strong>正向代理</strong>：当你需要通过Client访问其所在内网的资源，进行横向移动时使用</p>
                <p><strong>反向代理</strong>：当你需要将Client内网的服务暴露给外部访问时使用</p>
                <p><strong>代理链</strong>：当你需要穿越多层内网环境，访问深层内网资源时使用</p>
              </a-collapse-panel>
              
              <a-collapse-panel key="2" header="🔧 端口配置有什么要求？">
                <p><strong>推荐端口范围</strong>：28000-48000 (iox系统标准)</p>
                <p><strong>自动分配</strong>：系统自动选择可用端口，推荐使用</p>
                <p><strong>手动设置</strong>：可以指定特定端口，需要确保端口可用</p>
              </a-collapse-panel>
              
              <a-collapse-panel key="3" header="🌐 Client和Server的区别？">
                <p><strong>Server</strong>：运行在有公网IP的服务器上，提供代理服务</p>
                <p><strong>Client</strong>：运行在需要代理的设备上，连接到Server</p>
                <p><strong>关系</strong>：一个Server可以管理多个Client</p>
              </a-collapse-panel>
              
              <a-collapse-panel key="4" header="⚡ 如何提高代理性能？">
                <p><strong>优先级设置</strong>：数值越小优先级越高，合理设置可提高性能</p>
                <p><strong>连接数限制</strong>：根据服务器性能调整最大连接数</p>
                <p><strong>缓冲区大小</strong>：适当增大缓冲区可提高传输效率</p>
              </a-collapse-panel>
              
              <a-collapse-panel key="5" header="🔒 如何确保代理安全？">
                <p><strong>启用认证</strong>：设置用户名密码防止未授权访问</p>
                <p><strong>IP访问控制</strong>：限制允许连接的IP地址范围</p>
                <p><strong>速率限制</strong>：防止滥用，保护服务器资源</p>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  defaultTab: {
    type: String,
    default: 'overview'
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(props.modelValue)
const activeTab = ref(props.defaultTab)

// 监听器
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
})

watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})
</script>

<style scoped>
.proxy-config-guide {
  .guide-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .overview-section {
    .ant-card {
      height: 140px;
      
      .ant-card-body {
        padding: 12px;
      }
      
      p {
        margin-bottom: 4px;
        font-size: 12px;
        line-height: 1.4;
      }
    }
  }

  .architecture-diagram {
    .diagram-box {
      background-color: #f5f5f5;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 16px;
      margin: 12px 0;
      
      .ascii-diagram {
        font-family: 'Courier New', monospace;
        font-size: 11px;
        line-height: 1.3;
        color: #333;
        margin: 0;
        white-space: pre;
        overflow-x: auto;
      }
    }
  }

  .ant-descriptions {
    margin-bottom: 16px;
  }

  .ant-list-item {
    padding: 4px 0;
    font-size: 13px;
  }

  .faq-section {
    .ant-collapse-panel {
      .ant-collapse-content-box {
        p {
          margin-bottom: 8px;
          line-height: 1.5;
        }
      }
    }
  }
}
</style>
