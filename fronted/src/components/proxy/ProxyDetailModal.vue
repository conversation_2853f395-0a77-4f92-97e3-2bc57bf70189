<template>
  <a-modal
    v-model:visible="modalVisible"
    title="代理实例详情"
    width="900px"
    :footer="null"
    @cancel="handleClose"
  >
    <div v-if="proxyData" class="proxy-detail">
      <!-- 基本信息 -->
      <a-card title="基本信息" size="small" class="detail-card">
        <template #extra>
          <a-button type="primary" size="small" @click="editMode = !editMode">
            {{ editMode ? '取消编辑' : '编辑' }}
          </a-button>
        </template>

        <a-form
          v-if="editMode"
          ref="formRef"
          :model="editForm"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="代理名称" name="name">
                <a-input v-model:value="editForm.name" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="代理类型">
                <a-tag :color="getProxyTypeColor(proxyData.type)">
                  {{ getProxyTypeText(proxyData.type) }}
                </a-tag>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="描述">
            <a-textarea
              v-model:value="editForm.description"
              :rows="2"
            />
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="最大连接数">
                <a-input-number
                  v-model:value="editForm.max_connections"
                  :min="1"
                  :max="10000"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="超时时间(秒)">
                <a-input-number
                  v-model:value="editForm.timeout"
                  :min="1"
                  :max="3600"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 认证配置 -->
          <a-divider orientation="left">认证配置</a-divider>
          <a-form-item>
            <a-checkbox v-model:checked="editForm.auth_required">启用认证</a-checkbox>
          </a-form-item>

          <div v-if="editForm.auth_required">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户名">
                  <a-input v-model:value="editForm.username" placeholder="请输入用户名" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="密码">
                  <a-input-password v-model:value="editForm.password" placeholder="请输入密码" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <div class="form-actions">
            <a-space>
              <a-button @click="editMode = false">取消</a-button>
              <a-button type="primary" @click="handleUpdate" :loading="updating">
                保存
              </a-button>
            </a-space>
          </div>
        </a-form>

        <a-descriptions v-else :column="2" bordered size="small">
          <a-descriptions-item label="代理ID">{{ proxyData.id }}</a-descriptions-item>
          <a-descriptions-item label="代理名称">{{ proxyData.name }}</a-descriptions-item>
          <a-descriptions-item label="代理类型">
            <a-tag :color="getProxyTypeColor(proxyData.type)">
              {{ getProxyTypeText(proxyData.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="监听端口">
            <template v-if="proxyData.user_port && proxyData.user_port > 0">
              <a-tag color="blue">{{ proxyData.user_port }}</a-tag>
            </template>
            <template v-else>
              <a-tag color="orange">未分配</a-tag>
            </template>
          </a-descriptions-item>
          <a-descriptions-item label="运行状态">
            <a-tag :color="getStatusColor(proxyData.status)">
              {{ getStatusText(proxyData.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="认证状态">
            <a-tag :color="proxyData.auth_required ? 'green' : 'default'">
              {{ proxyData.auth_required ? '启用' : '禁用' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="最大连接数">{{ proxyData.max_connections }}</a-descriptions-item>
          <a-descriptions-item label="超时时间">{{ proxyData.timeout }}秒</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ formatTime(proxyData.created_at) }}</a-descriptions-item>
          <a-descriptions-item label="描述" :span="2">{{ proxyData.description || '无' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 客户端信息 -->
      <a-card v-if="proxyData.client_info" title="关联客户端" size="small" class="detail-card">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="主机名">{{ proxyData.client_info.hostname }}</a-descriptions-item>
          <a-descriptions-item label="操作系统">{{ proxyData.client_info.os }}</a-descriptions-item>
          <a-descriptions-item label="内网IP">{{ proxyData.client_info.local_ip }}</a-descriptions-item>
          <a-descriptions-item label="公网IP">{{ proxyData.client_info.public_ip }}</a-descriptions-item>
          <a-descriptions-item label="客户端状态" :span="2">
            <a-tag :color="proxyData.client_info.status === 1 ? 'green' : 'red'">
              {{ proxyData.client_info.status === 1 ? '在线' : '离线' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 统计信息 -->
      <a-card title="统计信息" size="small" class="detail-card">
        <a-row :gutter="16" class="stats-row">
          <a-col :span="4">
            <a-statistic title="总连接数" :value="proxyData.total_connections || 0" />
          </a-col>
          <a-col :span="4">
            <a-statistic title="活跃连接" :value="proxyData.active_connections || 0" />
          </a-col>
          <a-col :span="4">
            <a-statistic title="上传流量" :value="formatBytes(proxyData.bytes_transferred || 0)" />
          </a-col>
          <a-col :span="4">
            <a-statistic title="下载流量" :value="formatBytes(proxyData.bytes_received || 0)" />
          </a-col>
          <a-col :span="4">
            <a-statistic title="请求总数" :value="proxyData.request_count || 0" />
          </a-col>
          <a-col :span="4">
            <a-statistic title="最后活动" :value="formatTime(proxyData.last_activity) || '无'" />
          </a-col>
        </a-row>
      </a-card>

      <!-- 配置信息 -->
      <a-card title="配置信息" size="small" class="detail-card">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="最大连接数">{{ proxyData.max_connections || 100 }}</a-descriptions-item>
          <a-descriptions-item label="超时时间">{{ proxyData.timeout || 30 }}秒</a-descriptions-item>
          <a-descriptions-item label="缓冲区大小">{{ proxyData.buffer_size || 4096 }}字节</a-descriptions-item>
          <a-descriptions-item label="日志级别">{{ proxyData.log_level || 'info' }}</a-descriptions-item>
          <a-descriptions-item label="需要认证">
            <a-tag :color="proxyData.auth_required ? 'green' : 'default'">
              {{ proxyData.auth_required ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="指标收集">
            <a-tag :color="proxyData.enable_metrics ? 'green' : 'default'">
              {{ proxyData.enable_metrics ? '启用' : '禁用' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item v-if="proxyData.allowed_ips" label="允许的IP" :span="2">
            {{ proxyData.allowed_ips }}
          </a-descriptions-item>
          <a-descriptions-item v-if="proxyData.blocked_ips" label="阻止的IP" :span="2">
            {{ proxyData.blocked_ips }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import { updateProxy } from '@/api/proxy'
import { formatBytes, formatTime } from '@/utils/format'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  proxyData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const modalVisible = ref(false)
const editMode = ref(false)
const updating = ref(false)

// 编辑表单
const editForm = reactive({
  id: null,
  name: '',
  description: '',
  max_connections: 100,
  timeout: 30,
  auth_required: false,
  username: '',
  password: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入代理名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (val) => {
  modalVisible.value = val
  if (val && props.proxyData) {
    // 初始化编辑表单
    Object.assign(editForm, {
      id: props.proxyData.id,
      name: props.proxyData.name,
      description: props.proxyData.description || '',
      max_connections: props.proxyData.max_connections || 100,
      timeout: props.proxyData.timeout || 30,
      auth_required: props.proxyData.auth_required || false,
      username: props.proxyData.username || '',
      password: '' // 密码不回显
    })
  }
})

watch(modalVisible, (val) => {
  emit('update:visible', val)
  if (!val) {
    editMode.value = false
  }
})

// 方法
const getProxyTypeColor = (type) => {
  const colors = {
    forward: 'green',
    reverse: 'orange',
    chain: 'blue'
  }
  return colors[type] || 'default'
}

const getProxyTypeText = (type) => {
  const texts = {
    forward: '正向代理',
    reverse: '反向代理',
    chain: '代理链'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    0: 'default',   // 停止
    1: 'green',     // 运行中
    2: 'red',       // 错误
    3: 'orange'     // 启动中
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    0: '停止',
    1: '运行中',
    2: '错误',
    3: '启动中'
  }
  return texts[status] || '未知'
}

const handleClose = () => {
  modalVisible.value = false
}

const handleUpdate = async () => {
  try {
    await formRef.value.validate()
    
    updating.value = true
    
    const response = await updateProxy(editForm)
    
    if (response.code === 200) {
      message.success('代理更新成功')
      editMode.value = false
      emit('success')
    } else {
      message.error(response.msg || '代理更新失败')
    }
  } catch (error) {
    if (error.errorFields) {
      return
    }
    console.error('更新代理失败:', error)
    message.error('更新代理失败')
  } finally {
    updating.value = false
  }
}
</script>

<style scoped>
.proxy-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.stats-row {
  text-align: center;
}

.form-actions {
  text-align: right;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 600;
  background: #fafafa;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
  color: #8c8c8c;
}

:deep(.ant-statistic-content) {
  font-size: 16px;
  font-weight: 600;
}
</style>
