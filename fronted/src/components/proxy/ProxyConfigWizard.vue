<template>
  <a-modal
    v-model:visible="modalVisible"
    title="代理配置向导"
    width="900px"
    :footer="null"
    @cancel="handleClose"
  >
    <a-steps :current="currentStep" class="wizard-steps">
      <a-step title="选择客户端" description="选择要配置代理的客户端" />
      <a-step title="代理类型" description="选择代理类型和基本配置" />
      <a-step title="高级配置" description="配置高级参数和安全选项" />
      <a-step title="确认配置" description="确认配置信息并创建代理" />
    </a-steps>

    <div class="wizard-content">
      <!-- 步骤1：选择客户端 -->
      <div v-if="currentStep === 0" class="step-content">
        <h3>选择客户端</h3>
        <p class="step-desc">请选择要配置代理的在线客户端</p>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="在线客户端" size="small">
              <a-list
                :data-source="onlineClients"
                :loading="loadingClients"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #avatar>
                        <a-avatar :style="{ backgroundColor: getClientStatusColor(item.status) }">
                          <template #icon><DesktopOutlined /></template>
                        </a-avatar>
                      </template>
                      <template #title>
                        <a-radio
                          :value="item.id"
                          v-model:checked="selectedClientId"
                          @change="handleClientSelect(item)"
                        >
                          {{ item.hostname }}
                        </a-radio>
                      </template>
                      <template #description>
                        <div class="client-info">
                          <a-tag size="small" :color="getOSColor(item.os)">{{ item.os }}</a-tag>
                          <span class="client-ip">{{ item.local_ip }}</span>
                          <a-tag size="small" color="green">在线</a-tag>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-col>
          
          <a-col :span="12">
            <a-card title="客户端详情" size="small">
              <div v-if="selectedClient" class="client-detail">
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="主机名">{{ selectedClient.hostname }}</a-descriptions-item>
                  <a-descriptions-item label="操作系统">{{ selectedClient.os }}</a-descriptions-item>
                  <a-descriptions-item label="内网IP">{{ selectedClient.local_ip }}</a-descriptions-item>
                  <a-descriptions-item label="公网IP">{{ selectedClient.public_ip }}</a-descriptions-item>
                  <a-descriptions-item label="CPU架构">{{ selectedClient.arch }}</a-descriptions-item>
                  <a-descriptions-item label="连接时间">{{ formatTime(selectedClient.connected_at) }}</a-descriptions-item>
                </a-descriptions>
                
                <a-divider />
                
                <h4>系统信息</h4>
                <a-row :gutter="8">
                  <a-col :span="8">
                    <a-statistic title="CPU使用率" :value="selectedClient.cpu_usage || 0" suffix="%" />
                  </a-col>
                  <a-col :span="8">
                    <a-statistic title="内存使用率" :value="selectedClient.memory_usage || 0" suffix="%" />
                  </a-col>
                  <a-col :span="8">
                    <a-statistic title="网络延迟" :value="selectedClient.ping || 0" suffix="ms" />
                  </a-col>
                </a-row>
              </div>
              <a-empty v-else description="请选择一个客户端查看详情" />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 步骤2：代理类型配置 -->
      <div v-if="currentStep === 1" class="step-content">
        <h3>代理类型配置</h3>
        <p class="step-desc">选择代理类型并配置基本参数</p>
        
        <a-form
          :model="proxyConfig"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="代理类型">
                <a-radio-group v-model:value="proxyConfig.type">
                  <a-radio-button value="forward">
                    <ArrowRightOutlined />
                    正向代理
                  </a-radio-button>
                  <a-radio-button value="reverse">
                    <ArrowLeftOutlined />
                    反向代理
                  </a-radio-button>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="代理名称">
                <a-input v-model:value="proxyConfig.name" placeholder="请输入代理名称" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="描述">
            <a-textarea v-model:value="proxyConfig.description" :rows="2" placeholder="请输入代理描述（可选）" />
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="端口分配">
                <a-radio-group v-model:value="proxyConfig.alloc_mode">
                  <a-radio value="auto">自动分配</a-radio>
                  <a-radio value="manual">手动指定</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="proxyConfig.alloc_mode === 'manual'">
              <a-form-item label="监听端口">
                <a-input-number
                  v-model:value="proxyConfig.user_port"
                  :min="1"
                  :max="65535"
                  placeholder="请输入端口号"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 基本配置说明 -->
          <a-alert
            v-if="proxyConfig.type === 'forward'"
            message="正向代理说明"
            description="客户端将启动SOCKS5服务，用户连接到服务器端口，流量通过客户端转发到目标服务器。"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />
          <a-alert
            v-if="proxyConfig.type === 'reverse'"
            message="反向代理说明"
            description="服务器将启动SOCKS5服务，客户端连接到服务器，用户可以通过服务器端口访问客户端网络。"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />


        </a-form>
      </div>

      <!-- 步骤3：高级配置 -->
      <div v-if="currentStep === 2" class="step-content">
        <h3>高级配置</h3>
        <p class="step-desc">配置安全选项、性能参数和访问控制</p>
        
        <a-form
          :model="proxyConfig"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <!-- 认证配置 -->
          <a-divider orientation="left">认证配置</a-divider>
          <a-form-item>
            <a-checkbox v-model:checked="proxyConfig.auth_required">启用认证</a-checkbox>
          </a-form-item>
          
          <div v-if="proxyConfig.auth_required">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户名">
                  <a-input v-model:value="proxyConfig.username" placeholder="请输入用户名" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="密码">
                  <a-input-password v-model:value="proxyConfig.password" placeholder="请输入密码" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 性能配置 -->
          <a-divider orientation="left">性能配置</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="最大连接数">
                <a-input-number
                  v-model:value="proxyConfig.max_connections"
                  :min="1"
                  :max="10000"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="超时时间(秒)">
                <a-input-number
                  v-model:value="proxyConfig.timeout"
                  :min="1"
                  :max="3600"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 访问控制 -->
          <a-divider orientation="left">访问控制</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="允许的IP">
                <a-textarea
                  v-model:value="proxyConfig.allowed_ips"
                  :rows="3"
                  placeholder="每行一个IP地址或CIDR，留空表示允许所有"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="阻止的IP">
                <a-textarea
                  v-model:value="proxyConfig.blocked_ips"
                  :rows="3"
                  placeholder="每行一个IP地址或CIDR"
                />
              </a-form-item>
            </a-col>
          </a-row>


        </a-form>
      </div>

      <!-- 步骤4：确认配置 -->
      <div v-if="currentStep === 3" class="step-content">
        <h3>确认配置</h3>
        <p class="step-desc">请确认以下配置信息，确认无误后点击创建代理</p>
        
        <a-card title="配置摘要" size="small">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="客户端">{{ selectedClient?.hostname }}</a-descriptions-item>
            <a-descriptions-item label="代理名称">{{ proxyConfig.name }}</a-descriptions-item>
            <a-descriptions-item label="代理类型">
              <a-tag :color="getProxyTypeColor(proxyConfig.type)">
                {{ getProxyTypeText(proxyConfig.type) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="端口分配">
              {{ proxyConfig.alloc_mode === 'auto' ? '自动分配' : `手动指定: ${proxyConfig.user_port}` }}
            </a-descriptions-item>
            <a-descriptions-item label="认证">
              <a-tag :color="proxyConfig.auth_required ? 'green' : 'default'">
                {{ proxyConfig.auth_required ? '启用' : '禁用' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="最大连接数">{{ proxyConfig.max_connections }}</a-descriptions-item>
            <a-descriptions-item label="超时时间">{{ proxyConfig.timeout }}秒</a-descriptions-item>
            <a-descriptions-item label="描述" :span="2">{{ proxyConfig.description || '无' }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <a-card title="访问控制" size="small" style="margin-top: 16px" v-if="proxyConfig.allowed_ips || proxyConfig.blocked_ips">
          <a-space wrap>
            <a-tag v-if="proxyConfig.allowed_ips" color="cyan">IP白名单</a-tag>
            <a-tag v-if="proxyConfig.blocked_ips" color="red">IP黑名单</a-tag>
          </a-space>
        </a-card>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="wizard-footer">
      <a-space>
        <a-button v-if="currentStep > 0" @click="prevStep">上一步</a-button>
        <a-button v-if="currentStep < 3" type="primary" @click="nextStep" :disabled="!canProceed">下一步</a-button>
        <a-button v-if="currentStep === 3" type="primary" @click="handleSubmit" :loading="submitting">创建代理</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  DesktopOutlined,
  ArrowRightOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons-vue'
import { getClientList } from '@/api/client'
import { createProxy } from '@/api/proxy'
import { formatTime } from '@/utils/format'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const modalVisible = ref(false)
const currentStep = ref(0)
const loadingClients = ref(false)
const submitting = ref(false)
const onlineClients = ref([])
const selectedClientId = ref(null)
const selectedClient = ref(null)

// 代理配置 - 重构为匹配后端API
const proxyConfig = reactive({
  name: '',
  description: '',
  type: 'forward',
  user_port: 0, // 0表示自动分配
  auth_required: false,
  alloc_mode: 'auto',
  username: '',
  password: '',
  max_connections: 100,
  timeout: 30,
  client_id: null,
  listener_id: null,
  allowed_ips: '',
  blocked_ips: ''
})

// 计算属性
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return selectedClientId.value !== null
    case 1:
      return proxyConfig.name && proxyConfig.type
    case 2:
      if (proxyConfig.auth_required) {
        return proxyConfig.username && proxyConfig.password
      }
      return true
    case 3:
      return true
    default:
      return false
  }
})

// 监听对话框显示状态
watch(() => props.visible, (val) => {
  modalVisible.value = val
  if (val) {
    loadOnlineClients()
  }
})

watch(modalVisible, (val) => {
  emit('update:visible', val)
  if (!val) {
    resetWizard()
  }
})

// 方法
const loadOnlineClients = async () => {
  loadingClients.value = true
  try {
    const response = await getClientList({ 
      page: 1, 
      page_size: 100,
      status: 1 // 只获取在线客户端
    })
    if (response.code === 200) {
      onlineClients.value = response.data.list || []
    } else {
      message.error(response.msg || '获取客户端列表失败')
    }
  } catch (error) {
    console.error('获取客户端列表失败:', error)
    message.error('获取客户端列表失败')
  } finally {
    loadingClients.value = false
  }
}

const handleClientSelect = (client) => {
  selectedClient.value = client
  selectedClientId.value = client.id
  proxyConfig.client_id = client.id
}



const getClientStatusColor = (status) => {
  return status === 1 ? '#52c41a' : '#d9d9d9'
}

const getOSColor = (os) => {
  const colors = {
    'Windows': 'blue',
    'Linux': 'green',
    'macOS': 'purple',
    'Darwin': 'purple'
  }
  return colors[os] || 'default'
}

const getProxyTypeColor = (type) => {
  const colors = {
    forward: 'green',
    reverse: 'orange',
    chain: 'blue'
  }
  return colors[type] || 'default'
}

const getProxyTypeText = (type) => {
  const texts = {
    forward: '正向代理',
    reverse: '反向代理',
    chain: '代理链'
  }
  return texts[type] || type
}

const nextStep = () => {
  if (canProceed.value && currentStep.value < 3) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleSubmit = async () => {
  try {
    submitting.value = true

    // 准备提交数据，匹配后端ProxyCreateRequest结构
    const submitData = {
      name: proxyConfig.name,
      description: proxyConfig.description,
      type: proxyConfig.type,
      user_port: proxyConfig.alloc_mode === 'manual' ? proxyConfig.user_port : 0,
      auth_required: proxyConfig.auth_required,
      alloc_mode: proxyConfig.alloc_mode,
      username: proxyConfig.auth_required ? proxyConfig.username : '',
      password: proxyConfig.auth_required ? proxyConfig.password : '',
      max_connections: proxyConfig.max_connections,
      timeout: proxyConfig.timeout,
      client_id: proxyConfig.client_id,
      listener_id: proxyConfig.listener_id,
      allowed_ips: proxyConfig.allowed_ips ? proxyConfig.allowed_ips.split('\n').filter(ip => ip.trim()).join(',') : '',
      blocked_ips: proxyConfig.blocked_ips ? proxyConfig.blocked_ips.split('\n').filter(ip => ip.trim()).join(',') : ''
    }

    const response = await createProxy(submitData)
    
    if (response.code === 200) {
      message.success('代理创建成功')
      emit('success')
    } else {
      message.error(response.msg || '代理创建失败')
    }
  } catch (error) {
    console.error('创建代理失败:', error)
    message.error('创建代理失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  modalVisible.value = false
}

const resetWizard = () => {
  currentStep.value = 0
  selectedClientId.value = null
  selectedClient.value = null

  // 重置配置 - 匹配后端API结构
  Object.assign(proxyConfig, {
    name: '',
    description: '',
    type: 'forward',
    user_port: 0,
    auth_required: false,
    alloc_mode: 'auto',
    username: '',
    password: '',
    max_connections: 100,
    timeout: 30,
    client_id: null,
    listener_id: null,
    allowed_ips: '',
    blocked_ips: ''
  })
}
</script>

<style scoped>
.wizard-steps {
  margin-bottom: 24px;
}

.wizard-content {
  min-height: 400px;
  padding: 24px 0;
}

.step-content h3 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.step-desc {
  margin: 0 0 24px 0;
  color: #8c8c8c;
  font-size: 14px;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.client-ip {
  color: #8c8c8c;
  font-size: 12px;
}

.client-detail {
  padding: 16px;
}

.type-specific-config {
  margin-top: 16px;
}

.wizard-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 24px;
}

:deep(.ant-steps-item-title) {
  font-size: 14px;
}

:deep(.ant-steps-item-description) {
  font-size: 12px;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0;
}

:deep(.ant-divider-inner-text) {
  font-weight: 600;
  color: #1890ff;
}

:deep(.ant-radio-button-wrapper) {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
