<template>
  <div class="proxy-chain-node-manager">
    <!-- 节点管理标题 -->
    <a-divider orientation="left">代理链节点管理</a-divider>
    
    <!-- 代理链说明 -->
    <a-alert
      message="代理链节点配置"
      description="选择多个代理实例组成代理链，支持正向代理和反向代理混合模式。节点顺序决定数据流转路径，可通过拖拽调整顺序。"
      type="info"
      show-icon
      style="margin-bottom: 16px;"
    />

    <a-row :gutter="16">
      <!-- 左侧：可用代理实例 -->
      <a-col :span="12">
        <a-card title="🔍 可用代理实例" size="small">
          <template #extra>
            <a-button type="link" size="small" @click="refreshProxyList">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
          </template>
          
          <!-- 搜索框 -->
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索代理实例..."
            style="margin-bottom: 12px;"
            @search="handleSearch"
          />
          
          <!-- 可用代理列表 -->
          <div class="available-proxies">
            <div
              v-for="proxy in filteredAvailableProxies"
              :key="proxy.id"
              class="proxy-item available"
              @click="addToChain(proxy)"
            >
              <div class="proxy-info">
                <div class="proxy-name">
                  <a-tag :color="getProxyTypeColor(proxy.type)">
                    {{ getProxyTypeLabel(proxy.type) }}
                  </a-tag>
                  {{ proxy.name }}
                  <a-tag v-if="proxy.isTestData" color="orange" size="small">
                    示例
                  </a-tag>
                </div>
                <div class="proxy-details">
                  <span class="client-info">{{ proxy.client_name }}</span>
                  <span class="status-info">
                    <a-badge 
                      :status="proxy.status === 'running' ? 'success' : 'default'" 
                      :text="proxy.status === 'running' ? '运行中' : '已停止'"
                    />
                  </span>
                </div>
              </div>
              <div class="proxy-actions">
                <a-button type="primary" size="small" ghost>
                  <template #icon>
                    <PlusOutlined />
                  </template>
                  添加
                </a-button>
              </div>
            </div>
            
            <!-- 空状态 -->
            <a-empty 
              v-if="filteredAvailableProxies.length === 0"
              description="暂无可用的代理实例"
              :image="false"
            />
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：代理链节点 -->
      <a-col :span="12">
        <a-card title="🔗 代理链节点" size="small">
          <template #extra>
            <span class="node-count">{{ selectedNodes.length }} 个节点</span>
          </template>
          
          <!-- 代理链节点列表 -->
          <div class="chain-nodes">
            <div class="draggable-list">
              <div
                v-for="(node, index) in selectedNodes"
                :key="node.id"
                class="chain-node-item"
                :class="{ 'first-node': index === 0, 'last-node': index === selectedNodes.length - 1 }"
              >
                <!-- 节点序号 -->
                <div class="node-index">
                  <a-badge :count="index + 1" :number-style="{ backgroundColor: '#1890ff' }" />
                </div>

                <!-- 节点信息 -->
                <div class="node-info">
                  <div class="node-header">
                    <a-tag :color="getProxyTypeColor(node.type)">
                      {{ getProxyTypeLabel(node.type) }}
                    </a-tag>
                    <span class="node-name">{{ node.name }}</span>
                    <a-tag v-if="node.isTestData" color="orange" size="small">
                      示例
                    </a-tag>
                  </div>
                  <div class="node-details">
                    <span class="client-info">{{ node.client_name }}</span>
                    <span class="route-info">
                      {{ getRouteDescription(node, index) }}
                    </span>
                  </div>
                </div>

                <!-- 节点操作 -->
                <div class="node-actions">
                  <a-button type="text" size="small" @click="moveNodeUp(index)" :disabled="index === 0">
                    <template #icon>
                      <UpOutlined />
                    </template>
                  </a-button>
                  <a-button type="text" size="small" @click="moveNodeDown(index)" :disabled="index === selectedNodes.length - 1">
                    <template #icon>
                      <DownOutlined />
                    </template>
                  </a-button>
                  <a-button type="text" size="small" danger @click="removeFromChain(index)">
                    <template #icon>
                      <DeleteOutlined />
                    </template>
                  </a-button>
                </div>

                <!-- 连接箭头 -->
                <div v-if="index < selectedNodes.length - 1" class="connection-arrow">
                  <DownOutlined />
                </div>
              </div>
            </div>
            
            <!-- 空状态 -->
            <a-empty 
              v-if="selectedNodes.length === 0"
              description="请从左侧添加代理节点"
              :image="false"
            >
              <template #description>
                <p>请从左侧添加代理节点</p>
                <p style="color: #999; font-size: 12px;">至少需要2个节点才能组成代理链</p>
              </template>
            </a-empty>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 代理链预览 -->
    <template v-if="selectedNodes.length > 0">
      <a-divider orientation="left">代理链结构预览</a-divider>
      <a-card size="small" class="chain-preview">
        <div class="chain-flow">
          <div class="flow-item start">
            <div class="flow-icon">👤</div>
            <div class="flow-label">用户</div>
          </div>
          
          <div class="flow-arrow">→</div>
          
          <div class="flow-item server">
            <div class="flow-icon">🖥️</div>
            <div class="flow-label">Server</div>
            <div class="flow-detail">SOCKS5端口</div>
          </div>
          
          <template v-for="(node, index) in selectedNodes" :key="node.id">
            <div class="flow-arrow">→</div>
            <div class="flow-item node" :class="node.type">
              <div class="flow-icon">
                {{ node.type === 'forward' ? '🔄' : '🔀' }}
              </div>
              <div class="flow-label">{{ node.name }}</div>
              <div class="flow-detail">{{ getProxyTypeLabel(node.type) }}</div>
            </div>
          </template>
          
          <div class="flow-arrow">→</div>
          
          <div class="flow-item target">
            <div class="flow-icon">🎯</div>
            <div class="flow-label">深层内网资源</div>
          </div>
        </div>
        
        <!-- 链路统计 -->
        <div class="chain-stats">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="节点数量" :value="selectedNodes.length" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="正向代理" :value="forwardNodeCount" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="反向代理" :value="reverseNodeCount" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="预估延迟" :value="estimatedLatency" suffix="ms" />
            </a-col>
          </a-row>
        </div>
      </a-card>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  PlusOutlined,
  UpOutlined,
  DownOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  availableProxies: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const searchKeyword = ref('')
const selectedNodes = ref([...props.modelValue])

// 计算属性
const filteredAvailableProxies = computed(() => {
  const selectedIds = selectedNodes.value.map(node => node.id)
  return props.availableProxies
    .filter(proxy => !selectedIds.includes(proxy.id))
    .filter(proxy => {
      if (!searchKeyword.value) return true
      return proxy.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
             proxy.client_name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    })
})

const forwardNodeCount = computed(() => {
  return selectedNodes.value.filter(node => node.type === 'forward').length
})

const reverseNodeCount = computed(() => {
  return selectedNodes.value.filter(node => node.type === 'reverse').length
})

const estimatedLatency = computed(() => {
  // 简单的延迟估算：每个节点增加50ms延迟
  return selectedNodes.value.length * 50
})

// 监听器
watch(selectedNodes, (newValue) => {
  emit('update:modelValue', [...newValue])
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(selectedNodes.value)) {
    selectedNodes.value = [...newValue]
  }
}, { deep: true })

// 方法
const getProxyTypeColor = (type) => {
  const colors = {
    forward: 'green',
    reverse: 'blue',
    chain: 'purple'
  }
  return colors[type] || 'default'
}

const getProxyTypeLabel = (type) => {
  const labels = {
    forward: '正向代理',
    reverse: '反向代理',
    chain: '代理链'
  }
  return labels[type] || type
}

const getRouteDescription = (node, index) => {
  if (index === 0) {
    return '入口节点 (最接近Server)'
  } else if (index === selectedNodes.value.length - 1) {
    return '出口节点 (最接近目标)'
  } else {
    return `中继节点 (第${index + 1}层)`
  }
}

const addToChain = (proxy) => {
  if (selectedNodes.value.find(node => node.id === proxy.id)) {
    message.warning('该代理实例已在代理链中')
    return
  }

  selectedNodes.value.push({ ...proxy })
  message.success(`已添加 ${proxy.name} 到代理链`)
}

const removeFromChain = (index) => {
  const node = selectedNodes.value[index]
  selectedNodes.value.splice(index, 1)
  message.success(`已从代理链中移除 ${node.name}`)
}

const moveNodeUp = (index) => {
  if (index > 0) {
    const temp = selectedNodes.value[index]
    selectedNodes.value[index] = selectedNodes.value[index - 1]
    selectedNodes.value[index - 1] = temp
  }
}

const moveNodeDown = (index) => {
  if (index < selectedNodes.value.length - 1) {
    const temp = selectedNodes.value[index]
    selectedNodes.value[index] = selectedNodes.value[index + 1]
    selectedNodes.value[index + 1] = temp
  }
}

const handleNodeOrderChange = () => {
  message.info('代理链节点顺序已更新')
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const refreshProxyList = () => {
  emit('refresh')
  message.success('代理列表已刷新')
}
</script>

<style scoped>
.proxy-chain-node-manager {
  .available-proxies {
    max-height: 400px;
    overflow-y: auto;
  }

  .proxy-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-bottom: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
    }

    &.available {
      background-color: #fafafa;
    }

    .proxy-info {
      flex: 1;
      
      .proxy-name {
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .proxy-details {
        font-size: 12px;
        color: #666;
        display: flex;
        gap: 12px;
      }
    }
  }

  .chain-nodes {
    max-height: 400px;
    overflow-y: auto;
  }

  .draggable-list {
    min-height: 50px;
  }

  .chain-node-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background-color: #fff;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.first-node {
      border-color: #52c41a;
      background-color: #f6ffed;
    }

    &.last-node {
      border-color: #1890ff;
      background-color: #f0f9ff;
    }

    .node-index {
      margin-right: 12px;
    }

    .node-info {
      flex: 1;
      
      .node-header {
        margin-bottom: 4px;
        
        .node-name {
          font-weight: 500;
          margin-left: 8px;
        }
      }
      
      .node-details {
        font-size: 12px;
        color: #666;
        display: flex;
        gap: 12px;
      }
    }

    .node-actions {
      display: flex;
      gap: 4px;
    }

    .connection-arrow {
      position: absolute;
      bottom: -18px;
      left: 50%;
      transform: translateX(-50%);
      color: #1890ff;
      font-size: 16px;
    }
  }

  .chain-preview {
    .chain-flow {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 16px;
      
      .flow-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        min-width: 80px;
        
        &.start {
          background-color: #f0f9ff;
          border: 1px solid #1890ff;
        }
        
        &.server {
          background-color: #fff7e6;
          border: 1px solid #fa8c16;
        }
        
        &.node {
          background-color: #f6ffed;
          border: 1px solid #52c41a;
          
          &.reverse {
            background-color: #f0f5ff;
            border: 1px solid #2f54eb;
          }
        }
        
        &.target {
          background-color: #fff1f0;
          border: 1px solid #ff4d4f;
        }
        
        .flow-icon {
          font-size: 20px;
          margin-bottom: 4px;
        }
        
        .flow-label {
          font-size: 12px;
          font-weight: 500;
        }
        
        .flow-detail {
          font-size: 10px;
          color: #666;
        }
      }
      
      .flow-arrow {
        font-size: 16px;
        color: #1890ff;
        font-weight: bold;
      }
    }
    
    .chain-stats {
      border-top: 1px solid #f0f0f0;
      padding-top: 16px;
    }
  }

  .node-count {
    color: #666;
    font-size: 12px;
  }
}
</style>
