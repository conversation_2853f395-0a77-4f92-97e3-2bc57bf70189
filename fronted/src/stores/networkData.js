import { reactive, ref } from 'vue'

// 网络数据共享状态
export const networkDataStore = reactive({
  // 网络统计数据
  stats: {
    uploadSpeed: 0,
    downloadSpeed: 0,
    activeConnections: 0,
    packetLoss: 0,
    totalBytesSent: 0,
    totalBytesRecv: 0,
    timestamp: 0
  },
  
  // 流量趋势数据
  traffic: {
    timestamps: [],
    downloadSpeeds: [],
    uploadSpeeds: [],
    maxDataPoints: 50
  },
  
  // 当前速度
  currentSpeeds: {
    download: 0,
    upload: 0
  },
  
  // 历史数据用于速度计算
  lastData: {
    bytesRecv: 0,
    bytesSent: 0,
    updateTime: 0
  }
})

// 更新网络统计数据
export function updateNetworkStats(stats) {
  console.log('Store收到网络统计数据:', stats)

  // 检查是否有流量数据
  const hasTotalBytes = stats.total_bytes_recv !== undefined && stats.total_bytes_sent !== undefined
  console.log('是否有流量数据:', hasTotalBytes, {
    total_bytes_recv: stats.total_bytes_recv,
    total_bytes_sent: stats.total_bytes_sent
  })

  // 计算网络速度（只有在有流量数据时才计算）
  const currentTime = Date.now()

  if (hasTotalBytes) {
    if (networkDataStore.lastData.updateTime > 0) {
      const timeDiff = (currentTime - networkDataStore.lastData.updateTime) / 1000 // 转换为秒

      if (timeDiff > 0) {
        // 计算速度 (字节/秒)
        const downloadSpeed = Math.max(0, (stats.total_bytes_recv - networkDataStore.lastData.bytesRecv) / timeDiff)
        const uploadSpeed = Math.max(0, (stats.total_bytes_sent - networkDataStore.lastData.bytesSent) / timeDiff)

        console.log('计算的速度:', { downloadSpeed, uploadSpeed, timeDiff })

        // 更新当前速度
        networkDataStore.currentSpeeds.download = downloadSpeed
        networkDataStore.currentSpeeds.upload = uploadSpeed

        // 添加到趋势数据
        addTrafficDataPoint(downloadSpeed, uploadSpeed)
      }
    } else {
      console.log('首次接收流量数据，设置基准值')
      // 首次接收数据，设置当前速度为0
      networkDataStore.currentSpeeds.download = 0
      networkDataStore.currentSpeeds.upload = 0
    }
  } else {
    console.log('数据中没有流量统计，跳过速度计算')
  }
  
  // 更新统计数据
  Object.assign(networkDataStore.stats, {
    uploadSpeed: stats.upload_speed || 0,
    downloadSpeed: stats.download_speed || 0,
    activeConnections: stats.active_connections || 0,
    packetLoss: stats.packet_loss || 0,
    totalBytesSent: stats.total_bytes_sent || 0,
    totalBytesRecv: stats.total_bytes_recv || 0,
    timestamp: stats.timestamp || Date.now()
  })

  console.log('更新后的store数据:', networkDataStore.stats)
  console.log('当前速度:', networkDataStore.currentSpeeds)

  // 更新历史数据（只有在有流量数据时才更新）
  if (hasTotalBytes) {
    networkDataStore.lastData.bytesRecv = stats.total_bytes_recv || 0
    networkDataStore.lastData.bytesSent = stats.total_bytes_sent || 0
    networkDataStore.lastData.updateTime = currentTime
    console.log('更新历史数据:', networkDataStore.lastData)
  }
}

// 添加流量趋势数据点
export function addTrafficDataPoint(downloadSpeed, uploadSpeed) {
  const now = new Date()
  const timeStr = now.toLocaleTimeString()
  
  networkDataStore.traffic.timestamps.push(timeStr)
  networkDataStore.traffic.downloadSpeeds.push(downloadSpeed)
  networkDataStore.traffic.uploadSpeeds.push(uploadSpeed)
  
  // 保持数据点数量在限制内
  if (networkDataStore.traffic.timestamps.length > networkDataStore.traffic.maxDataPoints) {
    networkDataStore.traffic.timestamps.shift()
    networkDataStore.traffic.downloadSpeeds.shift()
    networkDataStore.traffic.uploadSpeeds.shift()
  }
}

// 清空流量趋势数据
export function clearTrafficData() {
  networkDataStore.traffic.timestamps.length = 0
  networkDataStore.traffic.downloadSpeeds.length = 0
  networkDataStore.traffic.uploadSpeeds.length = 0
  networkDataStore.currentSpeeds.download = 0
  networkDataStore.currentSpeeds.upload = 0
  networkDataStore.lastData.bytesRecv = 0
  networkDataStore.lastData.bytesSent = 0
  networkDataStore.lastData.updateTime = 0
}

// 添加测试数据
export function addTestTrafficData() {
  // 添加一些测试数据来验证图表功能
  for (let i = 0; i < 10; i++) {
    const downloadSpeed = Math.random() * 1024 * 1024 // 0-1MB/s
    const uploadSpeed = Math.random() * 512 * 1024    // 0-512KB/s
    addTrafficDataPoint(downloadSpeed, uploadSpeed)
  }
  networkDataStore.currentSpeeds.download = networkDataStore.traffic.downloadSpeeds[networkDataStore.traffic.downloadSpeeds.length - 1] || 0
  networkDataStore.currentSpeeds.upload = networkDataStore.traffic.uploadSpeeds[networkDataStore.traffic.uploadSpeeds.length - 1] || 0
}

// 格式化字节数
export function formatBytes(bytes) {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化速度
export function formatSpeed(bytesPerSecond) {
  if (!bytesPerSecond) return '0 B/s'
  const k = 1024
  const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
