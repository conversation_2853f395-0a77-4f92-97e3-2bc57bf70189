import { reactive, ref } from 'vue'
import * as settingsApi from '@/api/settings'
import { message } from 'ant-design-vue'

// 创建全局设置状态管理
const createSettingsStore = () => {
    // 状态变量
    const loading = ref(false)
    const generalSaving = ref(false)
    const notificationSaving = ref(false)
    const resetting = ref(false)

    // 常规设置表单数据
    const generalForm = reactive({
        server: {
            port: 8888,
            version: '1.0.0',
            routerPrefix: '/api',
            limitCountIP: 15000,
            limitTimeIP: 3600,
            serverStartPort: 20000,
            serverEndPort: 65535,
            clientBinDir: './clientbin',
            uploadDir: './upload',
            downloadDir: './download'
        },
        jwt: {
            expiresTime: '7d',
            bufferTime: '1d',
            signingKey: ''
        },
        captcha: {
            keyLong: 6,
            imgWidth: 240,
            imgHeight: 80,
            openCaptcha: true,
            maxFailedAttempts: 5,
            openCaptchaTimeOut: 3600
        },
        sqlite: {
            dbName: 'ezdata',
            path: './',
            maxOpenConns: 100,
            maxIdleConns: 10
        },
        zap: {
            level: 'info',
            director: 'log',
            retentionDay: -1,
            logInConsole: true,
            showLine: true
        }
    })

    // 通知设置表单数据
    const notificationForm = reactive({
        clientStatus: false,
        displayDuration: 5, // 前端显示为秒，后端存储为毫秒
        soundEnabled: false,
        maxNotifications: 5
    })

    // 加载配置数据
    const loadSettings = async () => {
        if (loading.value) return // 防止重复加载

        try {
            loading.value = true

            // 加载常规配置
            const generalRes = await settingsApi.getGeneralConfig()
            if (generalRes.data) {
                // 服务器配置
                if (generalRes.data.server) {
                    Object.assign(generalForm.server, {
                        port: generalRes.data.server.port || 8888,
                        version: generalRes.data.server.version || '1.0.0',
                        routerPrefix: generalRes.data.server.routerPrefix || '/api',
                        limitCountIP: generalRes.data.server.limitCountIP || 15000,
                        limitTimeIP: generalRes.data.server.limitTimeIP || 3600,
                        serverStartPort: generalRes.data.server.serverStartPort || 20000,
                        serverEndPort: generalRes.data.server.serverEndPort || 65535,
                        clientBinDir: generalRes.data.server.clientBinDir || './clientbin',
                        uploadDir: generalRes.data.server.uploadDir || './upload',
                        downloadDir: generalRes.data.server.downloadDir || './download'
                    })
                }

                // JWT配置
                if (generalRes.data.jwt) {
                    Object.assign(generalForm.jwt, {
                        expiresTime: generalRes.data.jwt.expiresTime || '7d',
                        bufferTime: generalRes.data.jwt.bufferTime || '1d',
                        signingKey: generalRes.data.jwt.signingKey || ''
                    })
                }

                // 验证码配置
                if (generalRes.data.captcha) {
                    Object.assign(generalForm.captcha, {
                        keyLong: generalRes.data.captcha.keyLong || 6,
                        imgWidth: generalRes.data.captcha.imgWidth || 240,
                        imgHeight: generalRes.data.captcha.imgHeight || 80,
                        openCaptcha: generalRes.data.captcha.openCaptcha !== undefined ? generalRes.data.captcha.openCaptcha : true,
                        maxFailedAttempts: generalRes.data.captcha.maxFailedAttempts || 5,
                        openCaptchaTimeOut: generalRes.data.captcha.openCaptchaTimeOut || 3600
                    })
                }

                // SQLite配置
                if (generalRes.data.sqlite) {
                    Object.assign(generalForm.sqlite, {
                        dbName: generalRes.data.sqlite.dbName || 'ezdata',
                        path: generalRes.data.sqlite.path || './',
                        maxOpenConns: generalRes.data.sqlite.maxOpenConns || 100,
                        maxIdleConns: generalRes.data.sqlite.maxIdleConns || 10
                    })
                }

                // 日志配置
                if (generalRes.data.zap) {
                    Object.assign(generalForm.zap, {
                        level: generalRes.data.zap.level || 'info',
                        director: generalRes.data.zap.director || 'log',
                        retentionDay: generalRes.data.zap.retentionDay || -1,
                        logInConsole: generalRes.data.zap.logInConsole !== undefined ? generalRes.data.zap.logInConsole : true,
                        showLine: generalRes.data.zap.showLine !== undefined ? generalRes.data.zap.showLine : true
                    })
                }
            }

            // 加载通知配置
            const notificationRes = await settingsApi.getNotificationConfig()
            if (notificationRes.data) {
                // 将后端返回的毫秒转换为秒用于前端显示
                const notificationData = {
                    ...notificationRes.data,
                    displayDuration: Math.round(notificationRes.data.displayDuration / 1000) // 转换为秒
                }
                Object.assign(notificationForm, notificationData)
            }
        } catch (error) {
            console.error('加载配置失败:', error)
            message.error('加载配置失败，请刷新页面重试')
        } finally {
            loading.value = false
        }
    }

    // 保存常规设置
    const saveGeneralSettings = async () => {
        try {
            generalSaving.value = true

            // 验证端口范围
            if (generalForm.server.serverStartPort >= generalForm.server.serverEndPort) {
                message.error('代理端口起始范围必须小于结束范围')
                return false
            }

            // 使用驼峰命名格式发送数据，与后端模型匹配
            const configData = {
                server: {
                    port: generalForm.server.port,
                    version: generalForm.server.version,
                    routerPrefix: generalForm.server.routerPrefix,
                    limitCountIP: generalForm.server.limitCountIP,
                    limitTimeIP: generalForm.server.limitTimeIP,
                    serverStartPort: generalForm.server.serverStartPort,
                    serverEndPort: generalForm.server.serverEndPort,
                    clientBinDir: generalForm.server.clientBinDir,
                    uploadDir: generalForm.server.uploadDir,
                    downloadDir: generalForm.server.downloadDir
                },
                jwt: {
                    expiresTime: generalForm.jwt.expiresTime,
                    bufferTime: generalForm.jwt.bufferTime,
                    signingKey: generalForm.jwt.signingKey
                },
                captcha: {
                    keyLong: generalForm.captcha.keyLong,
                    imgWidth: generalForm.captcha.imgWidth,
                    imgHeight: generalForm.captcha.imgHeight,
                    openCaptcha: generalForm.captcha.openCaptcha,
                    maxFailedAttempts: generalForm.captcha.maxFailedAttempts,
                    openCaptchaTimeOut: generalForm.captcha.openCaptchaTimeOut
                },
                sqlite: {
                    dbName: generalForm.sqlite.dbName,
                    path: generalForm.sqlite.path,
                    maxOpenConns: generalForm.sqlite.maxOpenConns,
                    maxIdleConns: generalForm.sqlite.maxIdleConns
                },
                zap: {
                    level: generalForm.zap.level,
                    director: generalForm.zap.director,
                    retentionDay: generalForm.zap.retentionDay,
                    logInConsole: generalForm.zap.logInConsole,
                    showLine: generalForm.zap.showLine
                }
            }

            await settingsApi.updateGeneralConfig(configData)
            message.success('常规设置保存成功')
            return true
        } catch (error) {
            console.error('保存常规设置失败:', error)
            message.error('保存失败，请重试')
            return false
        } finally {
            generalSaving.value = false
        }
    }

    // 保存通知设置
    const saveNotificationSettings = async () => {
        try {
            notificationSaving.value = true

            // 准备发送给后端的数据，将秒转换为毫秒
            const notificationData = {
                clientStatus: notificationForm.clientStatus,
                displayDuration: notificationForm.displayDuration * 1000, // 转换为毫秒
                soundEnabled: notificationForm.soundEnabled,
                maxNotifications: notificationForm.maxNotifications
            }

            await settingsApi.updateNotificationConfig(notificationData)
            message.success('通知设置保存成功')
            return { success: true, data: notificationData }
        } catch (error) {
            console.error('保存通知设置失败:', error)
            message.error('保存失败，请重试')
            return { success: false }
        } finally {
            notificationSaving.value = false
        }
    }

    // 重置配置
    const resetSettings = async () => {
        try {
            resetting.value = true
            await settingsApi.resetConfig()
            message.success('配置重置成功')
            // 重新加载配置
            await loadSettings()
            return true
        } catch (error) {
            console.error('重置配置失败:', error)
            message.error('重置失败，请重试')
            return false
        } finally {
            resetting.value = false
        }
    }

    return {
        // 状态
        loading,
        generalSaving,
        notificationSaving,
        resetting,
        generalForm,
        notificationForm,

        // 方法
        loadSettings,
        saveGeneralSettings,
        saveNotificationSettings,
        resetSettings
    }
}

// 创建全局单例实例
export const settingsStore = createSettingsStore()

// 导出使用函数，保持与Pinia类似的API
export const useSettingsStore = () => settingsStore