import request from '@/utils/request'

// ===== 文件传输性能监控 =====

// 获取文件传输统计信息
export function getFileTransferStats() {
  return request({
    url: '/file-transfer-stats/stats',
    method: 'get'
  })
}

// 获取文件传输内存池状态
export function getFileTransferPoolStatus() {
  return request({
    url: '/file-transfer-stats/pool-status',
    method: 'get'
  })
}

// 重置文件传输统计信息
export function resetFileTransferStats() {
  return request({
    url: '/file-transfer-stats/reset',
    method: 'post'
  })
}

// ===== 监听器性能监控 =====

// 获取监听器性能统计
export function getListenerPerformanceStats() {
  return request({
    url: '/performance/listener-stats',
    method: 'get'
  })
}

// 获取监听器连接统计
export function getListenerConnectionStats() {
  return request({
    url: '/performance/listener-connections',
    method: 'get'
  })
}

// 获取监听器网络流量统计
export function getListenerTrafficStats() {
  return request({
    url: '/performance/listener-traffic',
    method: 'get'
  })
}

// ===== 客户端性能监控 =====

// 获取客户端连接性能统计
export function getClientConnectionStats() {
  return request({
    url: '/performance/client-connections',
    method: 'get'
  })
}

// 获取客户端任务执行统计
export function getClientTaskStats() {
  return request({
    url: '/performance/client-tasks',
    method: 'get'
  })
}

// ===== 任务性能监控 =====

// 获取任务执行统计
export function getTaskExecutionStats() {
  return request({
    url: '/performance/task-execution',
    method: 'get'
  })
}

// 获取任务队列状态
export function getTaskQueueStats() {
  return request({
    url: '/performance/task-queue',
    method: 'get'
  })
}

// ===== 内存池性能监控 =====

// 获取所有内存池统计（包括客户端内存池）
export function getAllMemoryPoolStats() {
  return request({
    url: '/performance/memory-pools',
    method: 'get'
  })
}

// ===== Goroutine和工作池性能监控 =====

// 获取Goroutine统计信息
export function getGoroutineStats() {
  return request({
    url: '/performance/goroutine-stats',
    method: 'get'
  })
}

// 获取工作池统计信息
export function getWorkerPoolStats() {
  return request({
    url: '/performance/worker-pool-stats',
    method: 'get'
  })
}

// 获取泄漏检测器统计信息
export function getLeakDetectorStats() {
  return request({
    url: '/performance/leak-detector-stats',
    method: 'get'
  })
}

// 获取系统统计信息
export function getSystemStats() {
  return request({
    url: '/performance/system-stats',
    method: 'get'
  })
}

// 获取性能概览
export function getPerformanceOverview() {
  return request({
    url: '/performance/performance-overview',
    method: 'get'
  })
}

// ===== 数据库连接池性能监控 =====

// 获取数据库统计信息
export function getDatabaseStats() {
  return request({
    url: '/performance/database-stats',
    method: 'get'
  })
}

// 获取数据库健康状态
export function getDatabaseHealth() {
  return request({
    url: '/performance/database-health',
    method: 'get'
  })
}

// 获取数据库连接统计
export function getDatabaseConnectionStats() {
  return request({
    url: '/performance/database-connections',
    method: 'get'
  })
}

// 获取数据库查询统计
export function getDatabaseQueryStats() {
  return request({
    url: '/performance/database-queries',
    method: 'get'
  })
}

// ===== 缓存性能监控 =====

// 获取缓存统计信息
export function getCacheStats() {
  return request({
    url: '/performance/cache-stats',
    method: 'get'
  })
}

// 获取缓存健康状态
export function getCacheHealth() {
  return request({
    url: '/performance/cache-health',
    method: 'get'
  })
}
