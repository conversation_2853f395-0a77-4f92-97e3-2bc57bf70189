import { get, post, put, del } from '@/utils/request'

/**
 * 获取进程列表
 * @param {Number} clientId 客户端ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getProcessList(clientId, params = {}) {
  return post(`/process/list/${clientId}`, params)
}

/**
 * 终止进程
 * @param {Number} clientId 客户端ID
 * @param {Object} data 进程数据 {pid, force}
 * @returns {Promise}
 */
export function killProcess(clientId, data) {
  return post(`/process/kill/${clientId}`, data)
}

/**
 * 启动进程
 * @param {Number} clientId 客户端ID
 * @param {Object} data 进程启动数据 {command, args, workDir, runAsAdmin}
 * @returns {Promise}
 */
export function startProcess(clientId, data) {
  return post(`/process/start/${clientId}`, data)
}

/**
 * 获取进程详情
 * @param {Number} clientId 客户端ID
 * @param {Object} data 进程数据 {pid}
 * @returns {Promise}
 */
export function getProcessDetails(clientId, data) {
  return post(`/process/details/${clientId}`, data)
}

/**
 * 挂起进程
 * @param {Number} clientId 客户端ID
 * @param {Object} data 进程数据 {pid}
 * @returns {Promise}
 */
export function suspendProcess(clientId, data) {
  return post(`/process/suspend/${clientId}`, data)
}

/**
 * 恢复进程
 * @param {Number} clientId 客户端ID
 * @param {Object} data 进程数据 {pid}
 * @returns {Promise}
 */
export function resumeProcess(clientId, data) {
  return post(`/process/resume/${clientId}`, data)
}

// 导出API对象
export const processApi = {
  getProcessList,
  killProcess,
  startProcess,
  getProcessDetails,
  suspendProcess,
  resumeProcess
}

export default processApi