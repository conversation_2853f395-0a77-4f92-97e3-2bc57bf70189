import { post, get, del } from '@/utils/request'
import { getApiBaseUrl } from '@/utils/serverConfig'
import service from '@/utils/request'

/**
 * 获取文件信息
 * @param {Number} clientId 客户端ID
 * @param {Object} params 查询参数 {path}
 * @returns {Promise}
 */
export function getFileInfo(clientId, params) {
  return post(`/file/${clientId}/info`,  params )
}

/**
 * 复制文件
 * @param {Number} clientId 客户端ID
 * @param {Object} data 复制数据 {sourcePath, targetPath}
 * @returns {Promise}
 */
export function copyFile(clientId, data) {
  return post(`/file/${clientId}/copy`, data)
}

/**
 * 删除文件
 * @param {Number} clientId 客户端ID
 * @param {Object} data 删除数据 {path}
 * @returns {Promise}
 */
export function deleteFile(clientId, data) {
  return post(`/file/${clientId}/delete`, data)
}

/**
 * 移动文件
 * @param {Number} clientId 客户端ID
 * @param {Object} data 移动数据 {sourcePath, targetPath}
 * @returns {Promise}
 */
export function moveFile(clientId, data) {
  return post(`/file/${clientId}/move`, data)
}

/**
 * 从客户端下载文件
 * @param {Number} clientId 客户端ID
 * @param {Object} data 下载数据 {remotePath, localPath}
 * @returns {Promise}
 */
export function downloadFileFromClient(clientId, data) {
  return post(`/file/${clientId}/download`, data)
}

/**
 * 上传文件到服务器
 * @param {Number} clientId 客户端ID
 * @param {FormData} formData 文件数据
 * @param {Object} config 配置参数
 * @returns {Promise}
 */
export function uploadFileToServerAndTransferToClient(clientId, formData, config = {}) {
  return post(`/file/${clientId}/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}

// 已删除getDownloadedFiles函数

/**
 * 获取文件传输任务列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getFileTransferTasks(params) {
  return get('/file/transfer-tasks', { params })
}

// 获取单个任务状态
export function getTaskStatus(taskId) {
  return get(`/file/transfer-tasks/${taskId}`)
}

/**
 * 取消文件传输任务
 * @param {Object} data 任务数据 {taskId}
 * @returns {Promise}
 */
export function cancelFileTransferTask(data) {
  return del(`/file/cancel-transfer-tasks/${data.taskId}`)
}

/**
 * 读取文件内容
 * @param {Number} clientId 客户端ID
 * @param {Object} data 读取数据 {path}
 * @returns {Promise}
 */
export function readFileContent(clientId, data) {
  return post(`/file/${clientId}/read`, data)
}

/**
 * 写入文件内容
 * @param {Number} clientId 客户端ID
 * @param {Object} data 写入数据 {path, content, createDir, backup}
 * @returns {Promise}
 */
export function writeFileContent(clientId, data) {
  return post(`/file/${clientId}/write`, data)
}

/**
 * 创建新文件
 * @param {Number} clientId 客户端ID
 * @param {Object} data 创建数据 {path, content, encoding, force_create, create_dirs, file_mode}
 * @returns {Promise}
 */
export function createFile(clientId, data) {
  return post(`/file/${clientId}/create`, data)
}

/**
 * 获取服务器上传目录文件列表
 * @param {String} path 子路径
 * @returns {Promise}
 */
export function getUploadedFiles(path = '') {
  const url = path ? `/file/uploaded-files?path=${encodeURIComponent(path)}` : '/file/uploaded-files'
  return get(url)
}

/**
 * 获取服务器下载目录文件列表
 * @param {String} path 子路径
 * @returns {Promise}
 */
export function getDownloadFiles(path = '') {
  const url = path ? `/file/download-files?path=${encodeURIComponent(path)}` : '/file/download-files'
  return get(url)
}

/**
 * 传输服务器文件到客户端
 * @param {Number} clientId 客户端ID
 * @param {Object} data 传输数据 {server_file_path, destination_path, force, chunk_size}
 * @returns {Promise}
 */
export function transferServerFileToClient(clientId, data) {
  return post(`/file/${clientId}/transfer-server-file`, data)
}