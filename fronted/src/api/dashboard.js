import { get } from '@/utils/request'
import sseManager from '@/utils/sse'
import { getApiBaseUrl } from '@/utils/serverConfig'
/**
 * 获取系统信息
 * @returns {Promise}
 */
export function getSystemInfo() {
  return get('/dashboard/info')
}

/**
 * 获取dashboard统计信息
 * @returns {Promise}
 */
export function getDashboardStats() {
  return get('/dashboard/stats')
}

/**
 * 🚀 新增：创建Dashboard SSE连接
 * @param {Object} callbacks 回调函数
 * @returns {EventSource} SSE连接实例
 */
export function createDashboardSSE(callbacks = {}) {
  const {
    onSystemInfo = null,
    onDashboardStats = null,
    onTopologyData = null,
    onOpen = null,
    onError = null,
    onClose = null
  } = callbacks

  const baseUrl = getApiBaseUrl()
  const token = localStorage.getItem('token');
  const jwt = encodeURIComponent(token || '')
  const sseUrl = `${baseUrl}/dashboard/sse?token=${jwt}`

  return sseManager.connect(sseUrl, {
    onOpen: (event) => {
      console.log('✅ Dashboard SSE连接已建立')
      if (onOpen) onOpen(event)
    },
    onError: (event) => {
      console.error('❌ Dashboard SSE连接错误:', event)
      if (onError) onError(event)
    },
    onClose: (event) => {
      console.log('🔌 Dashboard SSE连接已关闭')
      if (onClose) onClose(event)
    },
    events: {
      // 系统信息事件
      system_info: (data, event) => {
        if (onSystemInfo) onSystemInfo(data.data)
      },
      // 统计信息事件
      dashboard_stats: (data, event) => {
        if (onDashboardStats) onDashboardStats(data.data)
      },
      // 拓扑数据事件
      topology_data: (data, event) => {
        if (onTopologyData) onTopologyData(data.data)
      }
    },
    reconnect: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5
  })
}

/**
 * 🚀 新增：断开Dashboard SSE连接
 */
export function disconnectDashboardSSE() {
  const baseUrl = window.location.protocol + '//' + window.location.host
  const sseUrl = `${baseUrl}/api/dashboard/sse`
  sseManager.disconnect(sseUrl)
}

/**
 * 🌐 新增：获取网络拓扑图数据
 * @returns {Promise}
 */
export function getNetworkTopology() {
  return get('/dashboard/topology')
}