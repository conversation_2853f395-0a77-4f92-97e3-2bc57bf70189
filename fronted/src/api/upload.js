/**
 * 上传相关API
 * 提供文件上传的接口封装
 */

import { getApiBaseUrl } from '@/utils/serverConfig'

/**
 * 获取上传配置信息
 * @returns {Promise<Object>} 上传配置
 */
export async function getUploadConfig() {
  try {
    const baseUrl = getApiBaseUrl()
    const token = localStorage.getItem('token')
    
    const response = await fetch(`${baseUrl}/upload/config`, {
      method: 'GET',
      headers: {
        'X-Token': token,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      // 处理401未授权错误
      if (response.status === 401) {
        localStorage.removeItem('token')
        setTimeout(() => {
          location.reload()
        }, 1500)
        throw new Error('未授权，请重新登录')
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    return result.data || {}
  } catch (error) {
    console.error('获取上传配置失败:', error)
    throw error
  }
}

/**
 * 上传文件到上传目录
 * @param {File} file - 要上传的文件
 * @param {Object} options - 上传选项
 * @param {Function} options.onProgress - 进度回调函数
 * @param {Function} options.onSuccess - 成功回调函数
 * @param {Function} options.onError - 错误回调函数
 * @returns {Promise<Object>} 上传结果
 */
export async function uploadFileToUploadDir(file, options = {}) {
  return uploadFile(file, 'upload-dir', options)
}

/**
 * 上传文件到下载目录
 * @param {File} file - 要上传的文件
 * @param {Object} options - 上传选项
 * @param {Function} options.onProgress - 进度回调函数
 * @param {Function} options.onSuccess - 成功回调函数
 * @param {Function} options.onError - 错误回调函数
 * @returns {Promise<Object>} 上传结果
 */
export async function uploadFileToDownloadDir(file, options = {}) {
  return uploadFile(file, 'download-dir', options)
}

/**
 * 通用文件上传函数
 * @param {File} file - 要上传的文件
 * @param {string} endpoint - 上传端点 ('upload-dir' 或 'download-dir')
 * @param {Object} options - 上传选项
 * @returns {Promise<Object>} 上传结果
 */
async function uploadFile(file, endpoint, options = {}) {
  try {
    const baseUrl = getApiBaseUrl()
    const token = localStorage.getItem('token')
    
    // 创建FormData
    const formData = new FormData()
    formData.append('file', file)
    
    // 创建XMLHttpRequest以支持进度监听
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()
      
      // 监听上传进度
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100)
          options.onProgress?.({
            progress,
            loaded: event.loaded,
            total: event.total
          })
        }
      })
      
      // 监听请求完成
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const result = JSON.parse(xhr.responseText)
            if (result.code === 200) {
              options.onSuccess?.(result.data)
              resolve(result.data)
            } else {
              const error = new Error(result.msg || '上传失败')
              options.onError?.(error)
              reject(error)
            }
          } catch (parseError) {
            const error = new Error('解析响应失败')
            options.onError?.(error)
            reject(error)
          }
        } else {
          // 处理401未授权错误
          if (xhr.status === 401) {
            localStorage.removeItem('token')
            setTimeout(() => {
              location.reload()
            }, 1500)
            const error = new Error('未授权，请重新登录')
            options.onError?.(error)
            reject(error)
            return
          }
          
          try {
            const errorData = JSON.parse(xhr.responseText)
            const error = new Error(errorData.msg || errorData.message || `HTTP ${xhr.status}: ${xhr.statusText}`)
            options.onError?.(error)
            reject(error)
          } catch (parseError) {
            const error = new Error(`HTTP ${xhr.status}: ${xhr.statusText}`)
            options.onError?.(error)
            reject(error)
          }
        }
      })
      
      // 监听请求错误
      xhr.addEventListener('error', () => {
        const error = new Error('网络错误')
        options.onError?.(error)
        reject(error)
      })
      
      // 监听请求超时
      xhr.addEventListener('timeout', () => {
        const error = new Error('请求超时')
        options.onError?.(error)
        reject(error)
      })
      
      // 配置请求
      xhr.open('POST', `${baseUrl}/upload/${endpoint}`)
      xhr.setRequestHeader('X-Token', token)
      xhr.timeout = 300000 // 5分钟超时
      
      // 发送请求
      xhr.send(formData)
    })
  } catch (error) {
    console.error('上传文件失败:', error)
    options.onError?.(error)
    throw error
  }
}

/**
 * 批量上传文件
 * @param {FileList|Array<File>} files - 要上传的文件列表
 * @param {string} targetDir - 目标目录 ('upload' 或 'download')
 * @param {Object} options - 上传选项
 * @param {Function} options.onProgress - 进度回调函数 (fileIndex, progress)
 * @param {Function} options.onFileSuccess - 单个文件成功回调 (fileIndex, result)
 * @param {Function} options.onFileError - 单个文件错误回调 (fileIndex, error)
 * @param {Function} options.onComplete - 全部完成回调 (results)
 * @returns {Promise<Array>} 上传结果数组
 */
export async function uploadMultipleFiles(files, targetDir = 'upload', options = {}) {
  const fileArray = Array.from(files)
  const results = []
  const uploadFunction = targetDir === 'download' ? uploadFileToDownloadDir : uploadFileToUploadDir
  
  for (let i = 0; i < fileArray.length; i++) {
    const file = fileArray[i]
    
    try {
      const result = await uploadFunction(file, {
        onProgress: (progress) => {
          options.onProgress?.(i, progress)
        },
        onSuccess: (result) => {
          options.onFileSuccess?.(i, result)
        },
        onError: (error) => {
          options.onFileError?.(i, error)
        }
      })
      
      results.push({ success: true, result, file: file.name })
    } catch (error) {
      results.push({ success: false, error: error.message, file: file.name })
    }
  }
  
  options.onComplete?.(results)
  return results
}

/**
 * 验证文件是否符合上传要求
 * @param {File} file - 要验证的文件
 * @param {Object} config - 上传配置
 * @returns {Object} 验证结果 { valid: boolean, error?: string }
 */
export function validateFile(file, config = {}) {
  // 检查文件大小
  if (config.maxFileSize && file.size > config.maxFileSize) {
    const maxSizeMB = Math.round(config.maxFileSize / 1024 / 1024)
    return {
      valid: false,
      error: `文件大小超过限制，最大允许 ${maxSizeMB}MB`
    }
  }
  
  // 检查文件扩展名
  if (config.allowedExtensions && config.allowedExtensions.length > 0) {
    const fileExt = file.name.split('.').pop()?.toLowerCase()
    if (!fileExt || !config.allowedExtensions.includes(fileExt)) {
      return {
        valid: false,
        error: `不支持的文件类型，仅支持: ${config.allowedExtensions.join(', ')}`
      }
    }
  }
  
  return { valid: true }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (!bytes) return '0 B'
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export default {
  getUploadConfig,
  uploadFileToUploadDir,
  uploadFileToDownloadDir,
  uploadMultipleFiles,
  validateFile,
  formatFileSize
}