import { get, post, put, del } from '@/utils/request'

/**
 * 获取用户列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getUserList(params) {
  return post('/user-manage/list', params)
}

/**
 * 创建用户
 * @param {Object} data 用户数据
 * @returns {Promise}
 */
export function createUser(data) {
  return post('/user-manage/create', data)
}

/**
 * 更新用户
 * @param {Object} data 用户数据
 * @returns {Promise}
 */
export function updateUser(data) {
  return put('/user-manage/update', data)
}

/**
 * 删除用户
 * @param {Number} id 用户ID
 * @returns {Promise}
 */
export function deleteUser(id) {
  return del(`/user-manage/${id}`)
}

/**
 * 修改用户状态
 * @param {Object} data 状态数据
 * @returns {Promise}
 */
export function changeUserStatus(data) {
  return put('/user-manage/status', data)
}
