import { post, get } from '@/utils/request'

/**
 * 登录接口
 * @param {Object} data - 登录信息
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @param {string} data.captcha - 验证码
 * @param {string} data.captcha_id - 验证码ID
 * @returns {Promise}
 */
export function login(data) {
  return post('/auth/login', data)
}

/**
 * 获取验证码
 * @returns {Promise}
 */
export function getCaptcha() {
  return get('/auth/captcha', {})
    .then(response => {
      console.log('验证码API响应成功:', response);
      return response;
    })
    .catch(error => {
      console.error('验证码API响应失败:', error);
      throw error;
    });
}

/**
 * 获取当前用户信息
 * @returns {Promise}
 */
export function getUserInfo() {
  return get('/auth/user-info')
}