import request from '@/utils/request'

// 代理实例管理API - 重构版本，只保留与后端匹配的功能

/**
 * 测试Server端口可用性
 * @param {Object} data - 测试参数
 * @param {number} data.port - 要测试的端口
 * @param {string} data.type - 代理类型 (forward/reverse)
 * @returns {Promise}
 */
export function checkServerPortAvailability(data) {
  return request({
    url: '/proxy/check-server-port',
    method: 'post',
    data
  })
}

/**
 * 测试Client端口可用性
 * @param {Object} data - 测试参数
 * @param {number} data.port - 要测试的端口
 * @param {number} data.client_id - 客户端ID
 * @param {string} data.type - 代理类型 (forward/reverse)
 * @returns {Promise}
 */
export function checkClientPortAvailability(data) {
  return request({
    url: '/proxy/check-client-port',
    method: 'post',
    data
  })
}

/**
 * 获取代理实例列表
 * @param {Object} data - 查询参数
 * @param {number} data.page - 页码
 * @param {number} data.page_size - 每页数量
 * @param {string} data.proxy_id - 代理ID过滤
 * @param {string} data.name - 名称过滤
 * @param {string} data.type - 类型过滤 (forward/reverse)
 * @param {number} data.status - 状态过滤
 * @param {number} data.client_id - 客户端ID过滤
 * @returns {Promise}
 */
export function getProxyList(data) {
  return request({
    url: '/proxy/list',
    method: 'post',
    data
  })
}

/**
 * 获取代理实例详情
 * @param {string} id - 代理实例ID
 * @returns {Promise}
 */
export function getProxyDetail(id) {
  return request({
    url: `/proxy/${id}`,
    method: 'get'
  })
}

/**
 * 创建代理实例
 * @param {Object} data - 代理实例数据
 * @param {string} data.name - 代理名称
 * @param {string} data.description - 代理描述
 * @param {string} data.type - 代理类型 (forward/reverse)
 * @param {number} data.user_port - 用户端口 (0表示自动分配)
 * @param {boolean} data.auth_required - 是否需要认证
 * @param {string} data.alloc_mode - 分配模式
 * @param {string} data.username - 认证用户名
 * @param {string} data.password - 认证密码
 * @param {number} data.max_connections - 最大连接数
 * @param {number} data.timeout - 超时时间
 * @param {number} data.client_id - 客户端ID
 * @param {number} data.listener_id - 监听器ID
 * @param {string} data.allowed_ips - 允许的IP列表
 * @param {string} data.blocked_ips - 阻止的IP列表
 * @returns {Promise}
 */
export function createProxy(data) {
  return request({
    url: '/proxy/create',
    method: 'post',
    data
  })
}

/**
 * 更新代理实例
 * @param {Object} data - 更新数据
 * @param {number} data.id - 代理实例ID
 * @param {string} data.name - 代理名称
 * @param {string} data.description - 代理描述
 * @param {boolean} data.auth_required - 是否需要认证
 * @param {string} data.username - 认证用户名
 * @param {string} data.password - 认证密码
 * @param {number} data.max_connections - 最大连接数
 * @param {number} data.timeout - 超时时间(秒)
 * @param {string} data.allowed_ips - 允许的IP列表
 * @param {string} data.blocked_ips - 阻止的IP列表
 * @returns {Promise}
 */
export function updateProxy(data) {
  return request({
    url: '/proxy/update',
    method: 'put',
    data
  })
}

/**
 * 删除代理实例
 * @param {number} id - 代理实例ID (数据库ID)
 * @returns {Promise}
 */
export function deleteProxy(id) {
  return request({
    url: `/proxy/delete/${id}`,
    method: 'delete'
  })
}

/**
 * 控制代理实例
 * @param {Object} data - 控制数据
 * @param {string} data.proxy_id - 代理ID
 * @param {string} data.action - 操作类型 (start/stop/restart)
 * @returns {Promise}
 */
export function controlProxy(data) {
  return request({
    url: '/proxy/control',
    method: 'post',
    data
  })
}

// 暂时移除不匹配的API函数，保留基础功能

// ===== 代理链相关API - 暂未实现，保留空函数避免前端报错 =====

/**
 * 获取代理链列表 - 暂未实现
 */
export function getProxyChainList(params) {
  return Promise.resolve({ code: 200, data: { list: [], total: 0 } })
}

/**
 * 创建代理链 - 暂未实现
 */
export function createProxyChain(data) {
  return Promise.resolve({ code: 500, msg: '代理链功能暂未实现' })
}

/**
 * 控制代理链 - 暂未实现
 */
export function controlProxyChain(data) {
  return Promise.resolve({ code: 500, msg: '代理链功能暂未实现' })
}

/**
 * 删除代理链 - 暂未实现
 */
export function deleteProxyChain(id) {
  return Promise.resolve({ code: 500, msg: '代理链功能暂未实现' })
}

/**
 * 添加代理链节点 - 暂未实现
 */
export function addProxyChainNode(data) {
  return Promise.resolve({ code: 500, msg: '代理链功能暂未实现' })
}


