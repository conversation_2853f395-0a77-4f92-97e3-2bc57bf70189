import request from '@/utils/request'

/**
 * 获取日志文件列表
 */
export function getLogFileList() {
  return request({
    url: '/log/files',
    method: 'get'
  })
}

/**
 * 获取日志文件内容
 * @param {Object} params - 查询参数
 * @param {string} params.path - 文件路径
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 */
export function getLogContent(params) {
  return request({
    url: '/log/content',
    method: 'get',
    params
  })
}

/**
 * 获取日志统计信息
 */
export function getLogStats() {
  return request({
    url: '/log/stats',
    method: 'get'
  })
}

/**
 * 搜索日志
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 关键词
 * @param {string} params.level - 日志级别
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @param {number} params.limit - 限制数量
 */
export function searchLogs(params) {
  return request({
    url: '/log/search',
    method: 'get',
    params
  })
}

/**
 * 获取实时日志流
 * @param {string} path - 文件路径
 * @returns {EventSource} - 事件源对象
 */
export function getLogStream(path) {
  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
  const url = `/api/log/stream?path=${encodeURIComponent(path)}&token=${encodeURIComponent(token)}`
  return new EventSource(url)
}
