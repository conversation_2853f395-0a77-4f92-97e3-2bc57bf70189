<template>
  <div class="proxy-chain-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>代理链管理</h2>
        <p class="header-desc">管理多节点代理链的配置和运行状态</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            创建代理链
          </a-button>
          <a-button @click="showVisualizer = true">
            <template #icon><NodeIndexOutlined /></template>
            可视化管理
          </a-button>
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="代理链名称">
          <a-input
            v-model:value="searchForm.name"
            placeholder="请输入代理链名称"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="运行状态">
          <a-select v-model:value="searchForm.status" placeholder="选择状态" allow-clear style="width: 150px">
            <a-select-option :value="0">停止</a-select-option>
            <a-select-option :value="1">启动中</a-select-option>
            <a-select-option :value="2">运行中</a-select-option>
            <a-select-option :value="3">错误</a-select-option>
            <a-select-option :value="4">停止中</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button @click="resetSearch">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 代理链列表 -->
    <a-card class="table-card" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="chainList"
        :loading="loading"
        :pagination="paginationConfig"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="chain-name">
              <LinkOutlined />
              <span class="name-text">{{ record.name }}</span>
            </div>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'nodes'">
            <a-badge :count="record.nodes_info ? record.nodes_info.length : 0" show-zero>
              <ApiOutlined style="font-size: 16px" />
            </a-badge>
          </template>
          <template v-else-if="column.key === 'config'">
            <div class="config-info">
              <a-space wrap>
                <a-tag color="blue" size="small">超时: {{ record.timeout }}s</a-tag>
                <a-tag v-if="record.health_check" color="green" size="small">健康检查</a-tag>
                <a-tag v-if="record.load_balancing" color="orange" size="small">负载均衡</a-tag>
              </a-space>
            </div>
          </template>

          <template v-else-if="column.key === 'stats'">
            <div class="stats-info">
              <div>连接: {{ record.total_connections || 0 }}</div>
              <div>流量: {{ formatBytes((record.bytes_transferred || 0) + (record.bytes_received || 0)) }}</div>
            </div>
          </template>

          <template v-else-if="column.key === 'created_at'">
            {{ formatTime(record.created_at) }}
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                v-if="record.status === 0"
                type="primary"
                size="small"
                @click="startChain(record)"
              >
                启动
              </a-button>
              <a-button
                v-else-if="record.status === 2"
                size="small"
                @click="stopChain(record)"
              >
                停止
              </a-button>
              <a-button
                size="small"
                @click="optimizeChain(record)"
              >
                优化
              </a-button>
              <a-button type="link" size="small" @click="viewNodes(record)">
                节点
              </a-button>
              <a-popconfirm
                title="确定要删除这个代理链吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteChain(record)"
              >
                <a-button danger type="link" size="small">
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建代理链模态框 -->
    <CreateChainModal
      v-model:visible="showCreateModal"
      @success="handleCreateSuccess"
    />

    <!-- 代理链可视化管理 -->
    <a-modal
      v-model:visible="showVisualizer"
      title="代理链可视化管理"
      width="95vw"
      :footer="null"
      :body-style="{ padding: 0, height: '80vh' }"
    >
      <ProxyChainVisualizer />
    </a-modal>

    <!-- 节点管理模态框 -->
    <ChainNodesModal
      v-model:visible="showNodesModal"
      :chain-data="currentChain"
      @success="handleNodesSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { PlusOutlined, ReloadOutlined, LinkOutlined, ApiOutlined, NodeIndexOutlined } from '@ant-design/icons-vue'
// import { getProxyChainList, controlProxyChain, deleteProxyChain } from '@/api/proxy' // 暂未实现
import { formatBytes, formatTime } from '@/utils/format'
import CreateChainModal from '@/components/proxy/CreateChainModal.vue'
import ProxyChainVisualizer from '@/components/proxy/ProxyChainVisualizer.vue'
import ChainNodesModal from '@/components/proxy/ChainNodesModal.vue'

// 响应式数据
const loading = ref(false)
const chainList = ref([])
const showCreateModal = ref(false)
const showVisualizer = ref(false)
const showNodesModal = ref(false)
const currentChain = ref(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  status: undefined
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '代理链名称',
    key: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: '链ID',
    dataIndex: 'chain_id',
    key: 'chain_id',
    width: 200,
    ellipsis: true
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '节点数量',
    key: 'nodes',
    width: 100,
    align: 'center'
  },
  {
    title: '配置信息',
    key: 'config',
    width: 200
  },
  {
    title: '统计信息',
    key: 'stats',
    width: 150
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = computed(() => ({
  ...pagination,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    loadChainList()
  },
  onShowSizeChange: (current, size) => {
    pagination.current = 1
    pagination.pageSize = size
    loadChainList()
  }
}))

// 方法
const getStatusColor = (status) => {
  const colors = {
    0: 'default',   // 停止
    1: 'orange',    // 启动中
    2: 'green',     // 运行中
    3: 'red',       // 错误
    4: 'orange'     // 停止中
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    0: '停止',
    1: '启动中',
    2: '运行中',
    3: '错误',
    4: '停止中'
  }
  return texts[status] || '未知'
}

const loadChainList = async () => {
  loading.value = true
  try {
    // 暂未实现代理链功能
    chainList.value = []
    pagination.total = 0
    message.info('代理链功能暂未实现')
  } catch (error) {
    console.error('获取代理链列表失败:', error)
    message.error('获取代理链列表失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadChainList()
}

const handleSearch = () => {
  pagination.current = 1
  loadChainList()
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'status' ? undefined : ''
  })
  handleSearch()
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadChainList()
}

const startChain = async (chain) => {
  try {
    // 暂未实现代理链功能
    message.info('代理链功能暂未实现')
  } catch (error) {
    console.error('代理链启动失败:', error)
    message.error('代理链启动失败')
  }
}

const stopChain = async (chain) => {
  try {
    // 暂未实现代理链功能
    message.info('代理链功能暂未实现')
  } catch (error) {
    console.error('代理链停止失败:', error)
    message.error('代理链停止失败')
  }
}

const optimizeChain = async (chain) => {
  try {
    // 暂未实现代理链功能
    message.info('代理链功能暂未实现')
  } catch (error) {
    console.error('代理链优化失败:', error)
    message.error('代理链优化失败')
  }
}

const deleteChain = async (chain) => {
  try {
    // 暂未实现代理链功能
    message.info('代理链功能暂未实现')
  } catch (error) {
    console.error('代理链删除失败:', error)
    message.error('代理链删除失败')
  }
}

const viewNodes = (chain) => {
  currentChain.value = chain
  showNodesModal.value = true
}

const handleCreateSuccess = () => {
  showCreateModal.value = false
  loadChainList()
}

const handleNodesSuccess = () => {
  showNodesModal.value = false
  loadChainList()
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.proxy-chain-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.search-card {
  margin-bottom: 16px;
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chain-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-text {
  font-weight: 500;
  margin-left: 8px;
}

.config-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stats-info {
  font-size: 12px;
  line-height: 1.4;
  color: #595959;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-tag) {
  border-radius: 4px;
}

:deep(.ant-badge) {
  display: inline-flex;
  align-items: center;
}
</style>
