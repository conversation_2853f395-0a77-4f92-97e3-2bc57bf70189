<template>
  <div class="proxy-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>代理实例管理</h2>
        <p class="header-desc">管理和监控代理实例的运行状态</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button type="primary" @click="handleCreateProxy">
            <template #icon><PlusOutlined /></template>
            创建代理
          </a-button>
          <a-button @click="showConfigWizard = true">
            <template #icon><SettingOutlined /></template>
            配置向导
          </a-button>
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="代理名称">
          <a-input
            v-model:value="searchForm.name"
            placeholder="请输入代理名称"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="代理类型">
          <a-select v-model:value="searchForm.type" placeholder="选择类型" allow-clear style="width: 150px">
            <a-select-option value="forward">正向代理</a-select-option>
            <a-select-option value="reverse">反向代理</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="运行状态">
          <a-select v-model:value="searchForm.status" placeholder="选择状态" allow-clear style="width: 150px">
            <a-select-option :value="0">停止</a-select-option>
            <a-select-option :value="1">运行中</a-select-option>
            <a-select-option :value="2">错误</a-select-option>
            <a-select-option :value="3">启动中</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="客户端">
          <a-select v-model:value="searchForm.client_id" placeholder="选择客户端" allow-clear style="width: 200px">
            <a-select-option
              v-for="client in clientList"
              :key="client.id"
              :value="client.id"
            >
              {{ client.hostname }} ({{ client.client_id }})
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button @click="resetSearch">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 批量操作 -->
    <a-card v-if="selectedRowKeys.length > 0" class="batch-actions" :bordered="false">
      <a-alert
        :message="`已选择 ${selectedRowKeys.length} 个代理实例`"
        type="info"
        show-icon
        :closable="false"
      />
      <div class="batch-buttons">
        <a-space style="margin-top: 16px">
          <a-button type="primary" @click="batchStart">批量启动</a-button>
          <a-button @click="batchStop">批量停止</a-button>
          <a-button @click="batchRestart">批量重启</a-button>
          <a-button danger @click="batchDelete">批量删除</a-button>
        </a-space>
      </div>
    </a-card>

    <!-- 代理列表 -->
    <a-card class="table-card" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="proxyList"
        :loading="loading"
        :row-selection="rowSelection"
        :pagination="paginationConfig"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="proxy-name">
              <a-tag :color="getProxyTypeColor(record.type)" size="small">
                {{ getProxyTypeText(record.type) }}
              </a-tag>
              <span class="name-text">{{ record.name }}</span>
            </div>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'client'">
            <div v-if="record.client_info" class="client-info">
              <MonitorOutlined />
              <span style="margin-left: 8px">{{ record.client_info.hostname }}</span>
              <a-tag size="small" color="blue" style="margin-left: 8px">{{ record.client_info.os }}</a-tag>

            </div>
            <span v-else class="text-muted">未关联</span>
          </template>

          <template v-else-if="column.key === 'connections'">
            <div class="connection-stats">
              <div>活跃: {{ record.active_connections || 0 }}</div>
              <div>总计: {{ record.total_connections || 0 }}</div>
            </div>
          </template>

          <template v-else-if="column.key === 'traffic'">
            <div class="traffic-stats">
              <div>上传: {{ formatBytes(record.bytes_transferred || 0) }}</div>
              <div>下载: {{ formatBytes(record.bytes_received || 0) }}</div>
            </div>
          </template>

          <template v-else-if="column.key === 'user_port'">
            <template v-if="record.user_port && record.user_port > 0">
              <a-tag color="blue">{{ record.user_port }}</a-tag>
            </template>
            <template v-else>
              <a-tag color="orange">未分配</a-tag>
            </template>
          </template>

          <template v-else-if="column.key === 'created_at'">
            {{ formatTime(record.created_at) }}
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                v-if="record.status === 0"
                type="primary"
                size="small"
                @click="startProxy(record)"
              >
                启动
              </a-button>
              <a-button
                v-else-if="record.status === 1"
                size="small"
                @click="stopProxy(record)"
              >
                停止
              </a-button>
              <a-button
                v-if="record.status === 1 || record.status === 0"
                size="small"
                @click="restartProxy(record)"
              >
                重启
              </a-button>
              <a-button type="link" size="small" @click="viewDetail(record)">
                详情
              </a-button>
              <a-popconfirm
                title="确定要删除这个代理吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteProxy(record)"
              >
                <a-button danger type="link" size="small">
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建代理模态框 -->
    <CreateProxyModal
      v-model:visible="showCreateModal"
      :client-list="clientList"
      @success="handleCreateSuccess"
    />

    <!-- 代理配置向导 -->
    <ProxyConfigWizard
      v-model:visible="showConfigWizard"
      @success="handleConfigWizardSuccess"
    />

    <!-- 代理详情模态框 -->
    <ProxyDetailModal
      v-model:visible="showDetailModal"
      :proxy-data="currentProxy"
      @success="handleUpdateSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { PlusOutlined, ReloadOutlined, SettingOutlined } from '@ant-design/icons-vue'
import { getProxyList, controlProxy, deleteProxy as deleteProxyApi } from '@/api/proxy'
import { getClientList } from '@/api/client'
import { formatBytes, formatTime } from '@/utils/format'
import CreateProxyModal from '@/components/proxy/CreateProxyModal.vue'
import ProxyConfigWizard from '@/components/proxy/ProxyConfigWizard.vue'
import ProxyDetailModal from '@/components/proxy/ProxyDetailModal.vue'

// 响应式数据
const loading = ref(false)
const proxyList = ref([])
const clientList = ref([])
const selectedRowKeys = ref([])
const showCreateModal = ref(false)
const showConfigWizard = ref(false)
const showDetailModal = ref(false)
const currentProxy = ref(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  type: undefined,
  status: undefined,
  client_id: undefined
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '代理名称',
    key: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: '端口',
    dataIndex: 'user_port',
    key: 'user_port',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '客户端',
    key: 'client',
    width: 200,
    ellipsis: true
  },
  {
    title: '连接统计',
    key: 'connections',
    width: 120
  },
  {
    title: '流量统计',
    key: 'traffic',
    width: 120
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    fixed: 'right'
  }
]

// 表格行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys, rows) => {
    selectedRowKeys.value = keys
  }
}

// 分页配置
const paginationConfig = computed(() => ({
  ...pagination,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    loadProxyList()
  },
  onShowSizeChange: (current, size) => {
    pagination.current = 1
    pagination.pageSize = size
    loadProxyList()
  }
}))

// 方法
const getProxyTypeColor = (type) => {
  const colors = {
    forward: 'green',
    reverse: 'orange',
    chain: 'blue'
  }
  return colors[type] || 'default'
}

const getProxyTypeText = (type) => {
  const texts = {
    forward: '正向',
    reverse: '反向',
    chain: '链式'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    0: 'default',   // 停止
    1: 'green',     // 运行中
    2: 'red',       // 错误
    3: 'orange'     // 启动中
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    0: '停止',
    1: '运行中',
    2: '错误',
    3: '启动中'
  }
  return texts[status] || '未知'
}

const loadProxyList = async () => {
  console.log('🚀 开始加载代理列表...')
  loading.value = true
  try {
    const data = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    console.log('📤 代理列表请求参数:', data)

    const response = await getProxyList(data)
    console.log('📥 代理列表API响应:', response)
    console.log('📥 response.data完整内容:', JSON.stringify(response.data, null, 2))

    if (response.code === 200) {
      console.log('✅ 代理列表响应成功')
      console.log('📊 响应数据结构:', response.data)
      console.log('📋 实际数据在data字段中:', response.data.data)

      // 后端使用异步任务系统，实际数据在 response.data.data 中
      const actualData = response.data.data || {}
      console.log('📋 代理列表数据:', actualData.list)
      console.log('🔢 代理总数:', actualData.total)

      proxyList.value = actualData.list || []
      pagination.total = actualData.total || 0

      console.log('💾 设置后的proxyList:', proxyList.value)
      console.log('💾 设置后的pagination.total:', pagination.total)
    } else {
      console.error('❌ 代理列表响应失败:', response.msg)
      message.error(response.msg || '获取代理列表失败')
    }
  } catch (error) {
    console.error('💥 获取代理列表异常:', error)
    message.error('获取代理列表失败')
  } finally {
    loading.value = false
    console.log('🏁 代理列表加载完成')
  }
}

const loadClientList = async () => {
  try {
    console.log('🔄 开始加载客户端列表...')
    const response = await getClientList({ page: 1, pageSize: 1000 })
    console.log('📥 客户端列表API响应:', response)

    if (response.code === 200) {
      console.log('📊 客户端响应数据结构:', response.data)
      console.log('📊 response.data的所有键:', Object.keys(response.data))
      console.log('📊 response.data完整内容:', JSON.stringify(response.data, null, 2))

      // 尝试不同的数据结构
      let clientData = []
      if (response.data.data && response.data.data.list) {
        // 异步任务系统格式
        clientData = response.data.data.list
        console.log('✅ 使用异步任务格式: response.data.data.list')
      } else if (response.data.list) {
        // 直接列表格式
        clientData = response.data.list
        console.log('✅ 使用直接列表格式: response.data.list')
      } else if (Array.isArray(response.data)) {
        // 数组格式
        clientData = response.data
        console.log('✅ 使用数组格式: response.data')
      } else {
        console.log('❌ 未知的数据格式，尝试所有可能的字段')
        console.log('� response.data类型:', typeof response.data)
        console.log('📊 response.data是否为数组:', Array.isArray(response.data))
      }

      clientList.value = clientData || []
      console.log('✅ 客户端列表加载完成，数量:', clientList.value.length)

      // 过滤在线客户端
      const onlineClients = clientList.value.filter(client => client.status === 1)
      console.log('🟢 在线客户端数量:', onlineClients.length)
      console.log('🟢 在线客户端列表:', onlineClients.map(c => `${c.hostname}(${c.id})`))
    } else {
      console.error('❌ 客户端列表API返回错误:', response.msg)
    }
  } catch (error) {
    console.error('💥 获取客户端列表失败:', error)
  }
}

const refreshData = () => {
  loadProxyList()
  loadClientList()
}

const handleSearch = () => {
  pagination.current = 1
  loadProxyList()
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'status' || key === 'client_id' ? undefined : ''
  })
  handleSearch()
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadProxyList()
}

const startProxy = async (proxy) => {
  try {
    const response = await controlProxy({
      proxy_id: proxy.proxy_id,
      action: 'start'
    })
    if (response.code === 200) {
      message.success('代理启动成功')
      loadProxyList()
    } else {
      message.error(response.msg || '代理启动失败')
    }
  } catch (error) {
    console.error('代理启动失败:', error)
    message.error('代理启动失败')
  }
}

const stopProxy = async (proxy) => {
  try {
    const response = await controlProxy({
      proxy_id: proxy.proxy_id,
      action: 'stop'
    })
    if (response.code === 200) {
      message.success('代理停止成功')
      loadProxyList()
    } else {
      message.error(response.msg || '代理停止失败')
    }
  } catch (error) {
    console.error('代理停止失败:', error)
    message.error('代理停止失败')
  }
}

const restartProxy = async (proxy) => {
  try {
    const response = await controlProxy({
      proxy_id: proxy.proxy_id,
      action: 'restart'
    })
    if (response.code === 200) {
      message.success('代理重启成功')
      loadProxyList()
    } else {
      message.error(response.msg || '代理重启失败')
    }
  } catch (error) {
    console.error('代理重启失败:', error)
    message.error('代理重启失败')
  }
}

const deleteProxy = async (proxy) => {
  try {
    const response = await deleteProxyApi(proxy.id)
    if (response.code === 200) {
      message.success('代理删除成功')
      loadProxyList()
    } else {
      message.error(response.msg || '代理删除失败')
    }
  } catch (error) {
    console.error('代理删除失败:', error)
    message.error('代理删除失败')
  }
}

const viewDetail = (proxy) => {
  currentProxy.value = proxy
  showDetailModal.value = true
}

const batchStart = async () => {
  await batchOperation('start', '启动')
}

const batchStop = async () => {
  await batchOperation('stop', '停止')
}

const batchRestart = async () => {
  await batchOperation('restart', '重启')
}

const batchDelete = async () => {
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个代理吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      batchOperation('delete', '删除')
    }
  })
}

const batchOperation = async (action, actionText) => {
  message.info(`批量${actionText}功能暂未实现`)
}

const handleCreateProxy = async () => {
  console.log('🚀 点击创建代理按钮，先加载客户端列表...')
  await loadClientList()
  console.log('📋 客户端列表加载完成，当前数量:', clientList.value.length)
  showCreateModal.value = true
}

const handleCreateSuccess = () => {
  showCreateModal.value = false
  loadProxyList()
}

const handleConfigWizardSuccess = () => {
  showConfigWizard.value = false
  loadProxyList()
}

const handleUpdateSuccess = () => {
  showDetailModal.value = false
  loadProxyList()
}

// 健康检查 - 暂时移除，功能未实现
const checkProxyHealth = async () => {
  message.info('健康检查功能暂未实现')


      // 刷新代理列表以更新状态
      loadProxyList()


  }


// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.proxy-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.search-card {
  margin-bottom: 16px;
}

.batch-actions {
  margin-bottom: 16px;
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.proxy-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-text {
  font-weight: 500;
  margin-left: 8px;
}

.client-info {
  display: flex;
  align-items: center;
}

.connection-stats,
.traffic-stats {
  font-size: 12px;
  line-height: 1.4;
  color: #595959;
}

.text-muted {
  color: #8c8c8c;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-tag) {
  border-radius: 4px;
}
</style>
