<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-left">
        <div class="login-info">
          <div class="login-image">
            <img src="@/assets/login-image.svg" alt="Login Image" />
          </div>
          <h2>Ez后台管理系统</h2>
          <p>写来玩的</p>
        </div>
      </div>
      <div class="login-right">
        <div class="login-form">
          <h2>欢迎回来 👋</h2>
          <p class="login-desc">请输入您的用户名和密码以便我们验证您的身份</p>
          <a-form
            :model="formState"
            name="basic"
            autocomplete="off"
            @finish="onFinish"
            @finishFailed="onFinishFailed"
            layout="vertical"
          >
            <a-form-item
              label="账号"
              name="username"
              :rules="[{ required: true, message: '请输入您的用户名!' }]"
            >
              <a-input v-model:value="formState.username" placeholder="请输入用户名" size="large" />
            </a-form-item>

            <a-form-item
              label="密码"
              name="password"
              :rules="[{ required: true, message: '请输入您的密码!' }]"
            >
              <a-input-password v-model:value="formState.password" placeholder="请输入密码" size="large" />
            </a-form-item>

            <a-form-item
              v-if="showCaptcha"
              label="验证码"
              name="captcha"
              :rules="[{ required: true, message: '请输入验证码!' }]"
            >
              <div class="captcha-container">
                <a-input v-model:value="formState.captcha" placeholder="请输入验证码" size="small" />
                <div class="captcha-image" @click="refreshCaptcha">
                  <img :src="captchaImg" alt="验证码" v-if="captchaImg" />
                  <div class="captcha-loading" v-else>
                    <a-spin />
                  </div>
                </div>
              </div>
            </a-form-item>

            <a-form-item name="remember" :wrapper-col="{ span: 24 }">
              <div class="login-options">
                <a-checkbox v-model:checked="formState.remember">记住我</a-checkbox>
                <a href="javascript:void(0)" class="forgot-password">忘记密码?</a>
              </div>
            </a-form-item>

            <a-form-item :wrapper-col="{ span: 24 }">
              <a-button type="primary" html-type="submit" :loading="loading" block size="large">登录</a-button>
            </a-form-item>

            <div class="login-footer">
              <a-space>
                <a-button
                    type="text"
                    shape="circle"
                    @click="openGithub"
                >
                  <template #icon><GithubOutlined /></template>
                </a-button>

                <!-- Google 按钮 -->
                <a-button
                    type="text"
                    shape="circle"
                    @click="openGoogle"
                >
                  <template #icon><GoogleOutlined /></template>
                </a-button>
              </a-space>
            </div>
          </a-form>
        </div>
        <div class="login-copyright">
          EzC2后台管理系统 ©2025 Created by Script XiaoZi
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { GithubOutlined, GoogleOutlined } from '@ant-design/icons-vue';
import { authApi } from '@/api';
import { useRouter } from 'vue-router';
import notificationManager from '@/utils/notificationManager';

// 表单数据
const formState = reactive({
  username: '',
  password: '',
  remember: true,
  captcha: '',
  captchaId: ''
});
const openGithub = () => {
  window.open('https://github.com', '_blank');
};
const openGoogle = () => {
  window.open('https://google.com', '_blank');
};

// 状态变量
const loading = ref(false);
const showCaptcha = ref(true);
const captchaImg = ref('');
const router = useRouter();

// 获取验证码
const getCaptcha = async () => {
  try {
    console.log("调用了获取captcha");
    // 添加更详细的日志
    console.log("验证码请求开始");
    const res = await authApi.getCaptcha();
    console.log("验证码API响应:", res);
    
    if (res && res.data) {
      captchaImg.value = res.data.picPath;
      formState.captchaId = res.data.captchaId;
      showCaptcha.value = res.data.openCaptcha;
      console.log("验证码数据设置成功:", {
        picPath: res.data.picPath ? '有图片数据' : '无图片数据',
        captchaId: res.data.captchaId,
        openCaptcha: res.data.openCaptcha
      });
    } else {
      console.error('验证码响应格式不正确:', res);
      message.error('获取验证码失败，响应格式不正确');
    }
  } catch (error) {
    console.error('获取验证码失败:', error);
    message.error('获取验证码失败，请稍后重试');
  }
};

// 刷新验证码
const refreshCaptcha = () => {
  captchaImg.value = '';
  getCaptcha();
};

// 登录成功处理
const onFinish = async (values) => {
  loading.value = true;
  try {
    const loginData = {
      username: values.username,
      password: values.password,
      captcha: values.captcha,
      captcha_id: formState.captchaId
    };
    
    const res = await authApi.login(loginData);
    message.success('登录成功');
    // 存储token
    localStorage.setItem('token', res.data.token);
    // 存储用户信息
    localStorage.setItem('userInfo', JSON.stringify(res.data.user));

    // 🚀 登录成功后启动通知系统
    notificationManager.start();

    // 跳转到仪表盘页面
    setTimeout(() => {
      router.push('/');
    }, 1000);
  } catch (error) {
    console.error('登录失败:', error);
    refreshCaptcha();
  } finally {
    loading.value = false;
  }
};

// 登录失败处理
const onFinishFailed = (errorInfo) => {
  console.log('Failed:', errorInfo);
  message.error('请填写完整的登录信息');
};

// 组件挂载时获取验证码
onMounted(() => {
  getCaptcha();
});
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: #f0f2f5;
  overflow: hidden;
}

.login-box {
  display: flex;
  width: 100%;
  max-width: 1200px;
  height: 600px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.login-left {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #001529;
  color: white;
  padding: 40px;
}

.login-info {
  text-align: center;
  max-width: 400px;
}

.login-image {
  margin-bottom: 30px;
}

.login-image img {
  width: 240px;
  height: auto;
}

.login-left h2 {
  font-size: 28px;
  margin-bottom: 16px;
  color: white;
}

.login-left p {
  font-size: 16px;
  opacity: 0.8;
}

.login-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px;
}

.login-form {
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.login-form h2 {
  font-size: 24px;
  margin-bottom: 8px;
  color: #333;
}

.login-desc {
  color: #666;
  margin-bottom: 24px;
  font-size: 14px;
}

.captcha-container {
  display: flex;
  gap: 12px;
}

.captcha-image {
  width: 120px;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.captcha-image img {
  width: 100%;
  height: 100%;
  //object-fit: cover;
  object-fit: contain;
}

.captcha-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.forgot-password {
  color: #1890ff;
  font-size: 14px;
}

.login-footer {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.login-copyright {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: auto;
}

@media (max-width: 768px) {
  .login-box {
    flex-direction: column;
    height: auto;
    max-width: 90%;
  }
  
  .login-left {
    padding: 30px;
  }
  
  .login-right {
    padding: 30px;
  }
}
</style>