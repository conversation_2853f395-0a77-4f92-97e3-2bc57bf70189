// 主题色变量
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;
$info-color: #1890ff;

// 文字颜色
$text-color-primary: rgba(0, 0, 0, 0.85);
$text-color-secondary: rgba(0, 0, 0, 0.65);
$text-color-disabled: rgba(0, 0, 0, 0.45);

// 背景色
$background-color-base: #f0f2f5;
$background-color-light: #fafafa;
$background-color-dark: #001529;

// 边框颜色
$border-color-base: #d9d9d9;
$border-color-split: #f0f0f0;

// 字体大小
$font-size-base: 14px;
$font-size-sm: 12px;
$font-size-lg: 16px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 边框圆角
$border-radius-base: 4px;
$border-radius-sm: 2px;
$border-radius-lg: 8px;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 阴影
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
$box-shadow-light: 0 1px 4px rgba(0, 0, 0, 0.08);
$box-shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.2);

// 过渡
$transition-duration: 0.3s;
$transition-ease: cubic-bezier(0.645, 0.045, 0.355, 1);

// 布局
$header-height: 64px;
$sidebar-width: 256px;
$sidebar-collapsed-width: 80px;
$content-padding: 24px;

// 响应式断点
$screen-xs: 480px;
$screen-sm: 576px;
$screen-md: 768px;
$screen-lg: 992px;
$screen-xl: 1200px;
$screen-xxl: 1600px;