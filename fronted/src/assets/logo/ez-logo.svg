<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="shadowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:0.6" />
    </linearGradient>
    
    <!-- 发光效果 -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
    
    <!-- 阴影效果 -->
    <filter id="dropshadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="60" cy="60" r="58" fill="url(#shadowGradient)" filter="url(#dropshadow)" opacity="0.1"/>
  
  <!-- 主要几何框架 -->
  <g transform="translate(60,60)">
    <!-- 外层六边形框架 -->
    <polygon points="-35,-20 -17.5,-35 17.5,-35 35,-20 35,20 17.5,35 -17.5,35 -35,20" 
             fill="none" 
             stroke="url(#primaryGradient)" 
             stroke-width="2" 
             opacity="0.6"/>
    
    <!-- 内层菱形 -->
    <polygon points="-25,0 0,-25 25,0 0,25" 
             fill="url(#primaryGradient)" 
             opacity="0.15"/>
    
    <!-- EZ字母设计 -->
    <g transform="translate(-15,-8)">
      <!-- E字母 - 简化几何设计 -->
      <g>
        <rect x="0" y="0" width="3" height="16" fill="url(#primaryGradient)"/>
        <rect x="0" y="0" width="12" height="3" fill="url(#primaryGradient)"/>
        <rect x="0" y="6.5" width="10" height="3" fill="url(#accentGradient)"/>
        <rect x="0" y="13" width="12" height="3" fill="url(#primaryGradient)"/>
        
        <!-- E字母的科技装饰 -->
        <rect x="13" y="1" width="2" height="1" fill="url(#accentGradient)" opacity="0.8"/>
        <rect x="11" y="7.5" width="2" height="1" fill="url(#accentGradient)" opacity="0.8"/>
        <rect x="13" y="14" width="2" height="1" fill="url(#accentGradient)" opacity="0.8"/>
      </g>
      
      <!-- Z字母 - 几何化设计 -->
      <g transform="translate(18,0)">
        <polygon points="0,0 12,0 12,3 4,13 12,13 12,16 0,16 0,13 8,3 0,3" 
                 fill="url(#primaryGradient)"/>
        
        <!-- Z字母的科技装饰线 -->
        <line x1="2" y1="8" x2="10" y2="8" stroke="url(#accentGradient)" stroke-width="1" opacity="0.6"/>
        
        <!-- 科技点缀 -->
        <circle cx="13" cy="2" r="1" fill="url(#accentGradient)" opacity="0.8"/>
        <circle cx="13" cy="14" r="1" fill="url(#accentGradient)" opacity="0.8"/>
      </g>
    </g>
    
    <!-- 人形元素 - 抽象化设计 -->
    <g transform="translate(0,25)">
      <!-- 头部 -->
      <circle cx="0" cy="-5" r="3" fill="url(#accentGradient)" opacity="0.8"/>
      
      <!-- 身体 -->
      <rect x="-1.5" y="-2" width="3" height="8" fill="url(#primaryGradient)" opacity="0.7"/>
      
      <!-- 手臂 - 科技化线条 -->
      <line x1="-6" y1="1" x2="-1.5" y2="1" stroke="url(#accentGradient)" stroke-width="2" opacity="0.8"/>
      <line x1="1.5" y1="1" x2="6" y2="1" stroke="url(#accentGradient)" stroke-width="2" opacity="0.8"/>
      
      <!-- 腿部 -->
      <line x1="-1" y1="6" x2="-3" y2="12" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.8"/>
      <line x1="1" y1="6" x2="3" y2="12" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.8"/>
      
      <!-- 科技装饰点 -->
      <circle cx="-6" cy="1" r="1" fill="url(#accentGradient)"/>
      <circle cx="6" cy="1" r="1" fill="url(#accentGradient)"/>
    </g>
    
    <!-- 科技装饰元素 -->
    <g opacity="0.6">
      <!-- 角落装饰线 -->
      <line x1="-40" y1="-25" x2="-30" y2="-25" stroke="url(#accentGradient)" stroke-width="1"/>
      <line x1="-40" y1="-25" x2="-40" y2="-15" stroke="url(#accentGradient)" stroke-width="1"/>
      
      <line x1="40" y1="-25" x2="30" y2="-25" stroke="url(#accentGradient)" stroke-width="1"/>
      <line x1="40" y1="-25" x2="40" y2="-15" stroke="url(#accentGradient)" stroke-width="1"/>
      
      <line x1="-40" y1="25" x2="-30" y2="25" stroke="url(#accentGradient)" stroke-width="1"/>
      <line x1="-40" y1="25" x2="-40" y2="15" stroke="url(#accentGradient)" stroke-width="1"/>
      
      <line x1="40" y1="25" x2="30" y2="25" stroke="url(#accentGradient)" stroke-width="1"/>
      <line x1="40" y1="25" x2="40" y2="15" stroke="url(#accentGradient)" stroke-width="1"/>
      
      <!-- 中心装饰点 -->
      <circle cx="-30" cy="-15" r="1" fill="url(#accentGradient)" opacity="0.8"/>
      <circle cx="30" cy="-15" r="1" fill="url(#accentGradient)" opacity="0.8"/>
      <circle cx="-30" cy="15" r="1" fill="url(#accentGradient)" opacity="0.8"/>
      <circle cx="30" cy="15" r="1" fill="url(#accentGradient)" opacity="0.8"/>
    </g>
    
    <!-- 数据流装饰 -->
    <g opacity="0.4">
      <rect x="-45" y="-1" width="8" height="1" fill="url(#accentGradient)">
        <animate attributeName="width" values="8;20;8" dur="2s" repeatCount="indefinite"/>
      </rect>
      <rect x="37" y="-1" width="8" height="1" fill="url(#accentGradient)">
        <animate attributeName="width" values="8;20;8" dur="2s" repeatCount="indefinite" begin="1s"/>
      </rect>
    </g>
  </g>
  
  <!-- 外层发光环 -->
  <circle cx="60" cy="60" r="55" fill="none" stroke="url(#primaryGradient)" stroke-width="1" opacity="0.3" filter="url(#glow)"/>
</svg>