{"name": "fronted", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@monaco-editor/loader": "^1.5.0", "@tinymce/tinymce-vue": "^6.2.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "ant-design-vue": "^4.2.6", "axios": "^1.9.0", "country-flag-icons": "^1.5.19", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "docx-preview": "^0.3.5", "leaflet": "^1.9.4", "mammoth": "^1.9.1", "marked": "^16.0.0", "monaco-editor": "^0.52.2", "terser": "^5.43.1", "vue": "^3.5.13", "vue-office": "^0.0.1", "vue-pdf-embed": "^2.1.3", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-search": "^0.13.0", "xterm-addon-web-links": "^0.9.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "sass-embedded": "^1.89.2", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5"}}